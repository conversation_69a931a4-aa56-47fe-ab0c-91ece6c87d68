#ifndef __INC_IBUFFER_MANAGER_H__
#define __INC_IBUFFER_MANAGER_H__

#include "../stl.h"

/**
 * @brief Pure virtual interface for BUFFER_MANAGER singleton
 * 
 * Provides ABI-stable access to buffer management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on memory buffer management and optimization.
 */
class IBufferManager
{
public:
    virtual ~IBufferManager() = default;
    
    // ============================================================================
    // BUFFER ALLOCATION AND DEALLOCATION
    // ============================================================================
    
    // Buffer allocation
    virtual void* AllocateBuffer(size_t size) = 0;
    virtual void* AllocateBuffer(size_t size, const char* tag) = 0;
    virtual void* ReallocateBuffer(void* buffer, size_t newSize) = 0;
    virtual void DeallocateBuffer(void* buffer) = 0;
    
    // Aligned buffer allocation
    virtual void* AllocateAlignedBuffer(size_t size, size_t alignment) = 0;
    virtual void DeallocateAlignedBuffer(void* buffer) = 0;
    
    // ============================================================================
    // BUFFER POOLS
    // ============================================================================
    
    // Pool management
    virtual bool CreatePool(const char* poolName, size_t bufferSize, size_t poolSize) = 0;
    virtual void DestroyPool(const char* poolName) = 0;
    virtual void* GetFromPool(const char* poolName) = 0;
    virtual void ReturnToPool(const char* poolName, void* buffer) = 0;
    
    // Pool statistics
    virtual size_t GetPoolSize(const char* poolName) = 0;
    virtual size_t GetPoolUsed(const char* poolName) = 0;
    virtual size_t GetPoolFree(const char* poolName) = 0;
    virtual float GetPoolUsagePercent(const char* poolName) = 0;
    
    // ============================================================================
    // TEMPORARY BUFFERS
    // ============================================================================
    
    // Temporary buffer management
    virtual void* GetTempBuffer(size_t size) = 0;
    virtual void ReleaseTempBuffer(void* buffer) = 0;
    virtual void ClearTempBuffers() = 0;
    virtual size_t GetTempBufferCount() = 0;
    
    // ============================================================================
    // BUFFER UTILITIES
    // ============================================================================
    
    // Buffer operations
    virtual void ZeroBuffer(void* buffer, size_t size) = 0;
    virtual void CopyBuffer(void* dest, const void* src, size_t size) = 0;
    virtual void MoveBuffer(void* dest, const void* src, size_t size) = 0;
    virtual int CompareBuffer(const void* buf1, const void* buf2, size_t size) = 0;
    
    // Buffer validation
    virtual bool IsValidBuffer(void* buffer) = 0;
    virtual size_t GetBufferSize(void* buffer) = 0;
    virtual const char* GetBufferTag(void* buffer) = 0;
    
    // ============================================================================
    // MEMORY STATISTICS
    // ============================================================================
    
    // Memory usage statistics
    virtual size_t GetTotalAllocated() = 0;
    virtual size_t GetTotalDeallocated() = 0;
    virtual size_t GetCurrentUsage() = 0;
    virtual size_t GetPeakUsage() = 0;
    virtual size_t GetAllocationCount() = 0;
    virtual size_t GetDeallocationCount() = 0;
    
    // Memory fragmentation
    virtual float GetFragmentationRatio() = 0;
    virtual size_t GetLargestFreeBlock() = 0;
    virtual size_t GetTotalFreeMemory() = 0;
    
    // ============================================================================
    // BUFFER TRACKING
    // ============================================================================
    
    // Allocation tracking
    virtual void EnableTracking(bool enable) = 0;
    virtual bool IsTrackingEnabled() = 0;
    virtual void DumpAllocations() = 0;
    virtual void DumpAllocationsByTag(const char* tag) = 0;
    
    // Leak detection
    virtual void CheckForLeaks() = 0;
    virtual size_t GetLeakCount() = 0;
    virtual void ReportLeaks() = 0;
    
    // ============================================================================
    // PERFORMANCE OPTIMIZATION
    // ============================================================================
    
    // Cache management
    virtual void FlushCaches() = 0;
    virtual void OptimizeMemory() = 0;
    virtual void DefragmentMemory() = 0;
    virtual void CompactMemory() = 0;
    
    // Preallocation
    virtual void PreallocateBuffers(size_t size, size_t count) = 0;
    virtual void PreallocatePool(const char* poolName, size_t count) = 0;
    
    // ============================================================================
    // SYSTEM MANAGEMENT
    // ============================================================================
    
    // System operations
    virtual bool Initialize() = 0;
    virtual void Destroy() = 0;
    virtual void Update() = 0;
    
    // Configuration
    virtual void SetMaxMemoryUsage(size_t maxBytes) = 0;
    virtual size_t GetMaxMemoryUsage() = 0;
    virtual void SetGrowthPolicy(int policy) = 0;
    virtual int GetGrowthPolicy() = 0;
    
    // ============================================================================
    // DEBUGGING AND MONITORING
    // ============================================================================
    
    // Debug information
    virtual void GetMemoryStatistics(char* buffer, int bufferSize) = 0;
    virtual void GetPoolStatistics(char* buffer, int bufferSize) = 0;
    virtual void GetAllocationStatistics(char* buffer, int bufferSize) = 0;
    
    // Memory validation
    virtual bool ValidateMemory() = 0;
    virtual bool ValidatePool(const char* poolName) = 0;
    virtual bool ValidateBuffer(void* buffer) = 0;
    
    // ============================================================================
    // EMERGENCY OPERATIONS
    // ============================================================================
    
    // Emergency cleanup
    virtual void EmergencyCleanup() = 0;
    virtual void FreeUnusedMemory() = 0;
    virtual void ReduceMemoryUsage(float percentage) = 0;
    
    // Memory pressure handling
    virtual bool IsMemoryPressure() = 0;
    virtual void HandleMemoryPressure() = 0;
    virtual void SetMemoryPressureThreshold(float threshold) = 0;
    
    // ============================================================================
    // UTILITY FUNCTIONS
    // ============================================================================
    
    // Size utilities
    virtual size_t AlignSize(size_t size, size_t alignment) = 0;
    virtual size_t GetOptimalSize(size_t requestedSize) = 0;
    virtual size_t GetPageSize() = 0;
    
    // String utilities
    virtual char* AllocateString(const char* str) = 0;
    virtual char* AllocateString(size_t length) = 0;
    virtual void DeallocateString(char* str) = 0;
    
    // Array utilities
    virtual void* AllocateArray(size_t elementSize, size_t count) = 0;
    virtual void* ReallocateArray(void* array, size_t elementSize, size_t newCount) = 0;
    virtual void DeallocateArray(void* array) = 0;
};

#endif // __INC_IBUFFER_MANAGER_H__
