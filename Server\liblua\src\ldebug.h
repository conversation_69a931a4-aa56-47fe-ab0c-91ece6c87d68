/*
* $Id: ldebug.h,v 1.32 2002/11/18 11:01:55 roberto Exp $
* Auxiliary functions from Debug Interface module
* See Copyright Notice in lua.h
*/

#ifndef __INC_LDEBUG_H__
#define __INC_LDEBUG_H__

#include "lstate.h"

#define pcRel(pc, p) (cast(int, (pc) - (p)->code) - 1)

#define getline(f,pc) (((f)->lineinfo) ? (f)->lineinfo[pc] : 0)

#define resethookcount(L) (L->hookcount = L->basehookcount)

void luaG_inithooks(lua_State* L);
void luaG_typeerror(lua_State* L, const TObject* o, const char* opname);
void luaG_concaterror(lua_State* L, StkId p1, StkId p2);
void luaG_aritherror(lua_State* L, const TObject* p1, const TObject* p2);
int luaG_ordererror(lua_State* L, const TObject* p1, const TObject* p2);
void luaG_runerror(lua_State* L, const char* fmt, ...);
void luaG_errormsg(lua_State* L);
int luaG_checkcode(const Proto* pt);

#endif // __INC_LDEBUG_H__
