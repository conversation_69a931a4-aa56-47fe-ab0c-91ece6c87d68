#ifndef __INC_ICMONARCH_H__
#define __INC_ICMONARCH_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
struct TMonarchInfo;

/**
 * @brief Pure virtual interface for CMonarch singleton
 * 
 * Provides ABI-stable access to monarch system functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on empire monarch system management.
 */
class ICMonarch
{
public:
    virtual ~ICMonarch() = default;
    
    // ============================================================================
    // INITIALIZATION
    // ============================================================================
    
    // System initialization
    virtual bool Initialize() = 0;
    
    // ============================================================================
    // MONARCH OPERATIONS
    // ============================================================================
    
    // Monarch healing
    virtual int HealMyEmpire(LPCHARACTER ch, DWORD price) = 0;
    
    // Monarch information
    virtual void SetMonarchInfo(TMonarchInfo* pInfo) = 0;
    virtual TMonarchInfo* GetMonarch() = 0;
    virtual DWORD GetMonarchPID(BYTE Empire) = 0;
    
    // ============================================================================
    // MONARCH STATUS QUERIES
    // ============================================================================
    
    // Status checking
    virtual bool IsMonarch(DWORD pid, BYTE bEmpire) = 0;
    
    // ============================================================================
    // MONEY MANAGEMENT
    // ============================================================================
    
    // Money operations
    virtual bool IsMoneyOk(int price, BYTE bEmpire) = 0;
    virtual bool SendtoDBAddMoney(int Money, BYTE bEmpire, LPCHARACTER ch) = 0;
    virtual bool SendtoDBDecMoney(int Money, BYTE bEmpire, LPCHARACTER ch) = 0;
    virtual bool AddMoney(int Money, BYTE bEmpire) = 0;
    virtual bool DecMoney(int Money, BYTE bEmpire) = 0;
    virtual int GetMoney(BYTE bEmpire) = 0;
    
    // ============================================================================
    // EMPIRE BUFFS
    // ============================================================================
    
    // Power up system
    virtual bool IsPowerUp(BYTE Empire) = 0;
    virtual int GetPowerUpCT(BYTE Empire) = 0;
    virtual bool CheckPowerUpCT(BYTE Empire) = 0;
    virtual void PowerUp(BYTE Empire, bool On) = 0;
    
    // Defense up system
    virtual bool IsDefenceUp(BYTE Empire) = 0;
    virtual int GetDefenseUpCT(BYTE Empire) = 0;
    virtual bool CheckDefenseUpCT(BYTE Empire) = 0;
    virtual void DefenseUp(BYTE Empire, bool On) = 0;
};

#endif // __INC_ICMONARCH_H__
