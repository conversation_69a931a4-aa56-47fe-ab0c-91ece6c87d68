#ifndef __INC_ICOXEVENT_MANAGER_H__
#define __INC_ICOXEVENT_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
enum OXEventStatus;

/**
 * @brief Pure virtual interface for COXEventManager singleton
 * 
 * Provides ABI-stable access to OX Event management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on OX Quiz Event system management.
 */
class ICOXEventManager
{
public:
    virtual ~ICOXEventManager() = default;
    
    // ============================================================================
    // LIFECYCLE MANAGEMENT
    // ============================================================================
    
    // System lifecycle
    virtual bool Initialize() = 0;
    virtual void Destroy() = 0;
    
    // ============================================================================
    // EVENT STATUS MANAGEMENT
    // ============================================================================
    
    // Status operations
    virtual OXEventStatus GetStatus() = 0;
    virtual void SetStatus(OXEventStatus status) = 0;
    
    // ============================================================================
    // QUIZ MANAGEMENT
    // ============================================================================
    
    // Quiz loading and management
    virtual bool LoadQuizScript(const char* szFileName) = 0;
    virtual void ClearQuiz() = 0;
    virtual bool AddQuiz(unsigned char level, const char* pszQuestion, bool answer) = 0;
    virtual bool ShowQuizList(LPCHARACTER pChar) = 0;
    
    // ============================================================================
    // EVENT OPERATIONS
    // ============================================================================
    
    // Event control
    virtual bool Enter(LPCHARACTER pChar) = 0;
    virtual bool CloseEvent() = 0;
    
    // ============================================================================
    // QUIZ EXECUTION
    // ============================================================================
    
    // Quiz operations
    virtual bool Quiz(unsigned char level, int timelimit) = 0;
    virtual bool CheckAnswer(bool answer) = 0;
    virtual void WarpToAudience() = 0;
    
    // ============================================================================
    // REWARD SYSTEM
    // ============================================================================
    
    // Reward operations
    virtual bool GiveItemToAttender(DWORD dwItemVnum, WORD count) = 0;
    virtual bool LogWinner() = 0;
    
    // ============================================================================
    // STATISTICS
    // ============================================================================
    
    // Event statistics
    virtual DWORD GetAttenderCount() = 0;
};

#endif // __INC_ICOXEVENT_MANAGER_H__
