# Plugin System Troubleshooting Guide

This guide helps you diagnose and fix common issues with the ProjectZ plugin system.

## Table of Contents

1. [Plugin Loading Issues](#plugin-loading-issues)
2. [Build and Compilation Problems](#build-and-compilation-problems)
3. [Runtime Errors](#runtime-errors)
4. [Performance Issues](#performance-issues)
5. [Configuration Problems](#configuration-problems)
6. [Event System Issues](#event-system-issues)
7. [Database Plugin Issues](#database-plugin-issues)
8. [Debugging Techniques](#debugging-techniques)
9. [Common Error Messages](#common-error-messages)
10. [Getting Help](#getting-help)

## Plugin Loading Issues

### Plugin Not Loading

**Symptoms:**
- Plugin doesn't appear in loaded plugins list
- No error messages in logs
- Server starts normally but plugin is inactive

**Possible Causes & Solutions:**

1. **File Permissions**
   ```bash
   # Check file permissions
   ls -la plugins/game/libYourPlugin.so
   
   # Fix permissions if needed
   chmod 755 plugins/game/libYourPlugin.so
   ```

2. **Wrong Directory**
   - Game plugins: `plugins/game/`
   - Database plugins: `plugins/db/`
   - Check server configuration for plugin directories

3. **Missing Dependencies**
   ```bash
   # Check library dependencies
   ldd plugins/game/libYourPlugin.so
   
   # Install missing libraries
   # For MySQL (DB plugins):
   sudo apt-get install libmysqlclient-dev
   ```

4. **Plugin Manager Not Initialized**
   - Check server logs for plugin manager initialization
   - Ensure plugin system is enabled in server configuration

### Plugin Loads But Doesn't Start

**Symptoms:**
- Plugin appears in loaded list
- Plugin state is INITIALIZED but not RUNNING
- Error messages during startup

**Possible Causes & Solutions:**

1. **Configuration Issues**
   - Check plugin configuration file exists
   - Verify configuration syntax
   - Check for required configuration values

2. **Dependency Problems**
   - Check plugin dependencies are loaded first
   - Verify dependency versions are compatible

3. **Initialization Failures**
   - Check plugin logs for specific error messages
   - Verify required resources are available
   - Check database connections (for DB plugins)

## Build and Compilation Problems

### Compilation Errors

**Common Issues:**

1. **Missing Headers**
   ```cpp
   // Error: 'LPCHARACTER' was not declared
   // Solution: Include proper headers
   #include "../../Server/game/src/char.h"
   ```

2. **Linking Errors**
   ```bash
   # Error: undefined reference to 'mysql_init'
   # Solution: Add MySQL libraries to Makefile
   LIBS += -lmysqlclient
   ```

3. **C++ Standard Issues**
   ```bash
   # Error: 'auto' not supported
   # Solution: Ensure C++14 is used
   CXXFLAGS = -std=c++14
   ```

### CMake Issues

**Common Problems:**

1. **Plugin Type Not Set**
   ```bash
   # Error: PLUGIN_TYPE must be defined
   # Solution: Set plugin type
   cmake -DPLUGIN_TYPE=game ..
   ```

2. **MySQL Not Found**
   ```bash
   # Error: Could NOT find MySQL
   # Solution: Install MySQL development packages
   sudo apt-get install libmysqlclient-dev
   ```

### Makefile Issues

**Common Problems:**

1. **Source Files Not Listed**
   ```makefile
   # Add your source files to SOURCES
   SOURCES = MyPlugin.cpp AnotherFile.cpp
   ```

2. **Plugin Name Not Set**
   ```makefile
   # Change from template default
   PLUGIN_NAME = YourActualPluginName
   ```

## Runtime Errors

### Segmentation Faults

**Common Causes:**

1. **Null Pointer Access**
   ```cpp
   // Always check pointers
   void OnCharacterLogin(LPCHARACTER ch)
   {
       if (!ch || !ch->GetName()) return;
       // Safe to use ch here
   }
   ```

2. **Invalid Memory Access**
   ```cpp
   // Don't store pointers to game objects
   // They may become invalid
   class MyPlugin {
       LPCHARACTER m_storedChar; // BAD!
   };
   ```

3. **Stack Overflow**
   ```cpp
   // Avoid deep recursion
   // Use iterative approaches when possible
   ```

### Memory Leaks

**Prevention:**

1. **Proper Cleanup**
   ```cpp
   void MyPlugin::Shutdown()
   {
       // Clean up all allocated memory
       for (auto* ptr : m_allocatedObjects) {
           delete ptr;
       }
       m_allocatedObjects.clear();
   }
   ```

2. **RAII Principles**
   ```cpp
   // Use smart pointers
   std::unique_ptr<MyObject> obj = std::make_unique<MyObject>();
   ```

### Plugin Crashes

**Debugging Steps:**

1. **Enable Debug Mode**
   ```ini
   # In plugin configuration
   debug_mode=true
   log_level=0
   ```

2. **Check Server Logs**
   ```bash
   tail -f log/syserr
   grep "YourPlugin" log/syserr
   ```

3. **Use GDB**
   ```bash
   gdb ./game
   (gdb) run
   # When crash occurs:
   (gdb) bt
   (gdb) info registers
   ```

## Performance Issues

### High CPU Usage

**Causes & Solutions:**

1. **Expensive Event Handlers**
   ```cpp
   // BAD: Heavy processing in event handler
   void OnHeartbeat() {
       ProcessHeavyTask(); // Called every few seconds!
   }
   
   // GOOD: Use separate thread or timer
   void OnHeartbeat() {
       if (++m_counter % 100 == 0) { // Every 100 heartbeats
           ScheduleHeavyTask();
       }
   }
   ```

2. **Excessive Logging**
   ```cpp
   // BAD: Logging in frequently called events
   void OnPacketReceive(...) {
       LogEvent(0, "Packet received"); // Too much!
   }
   
   // GOOD: Use debug mode checks
   void OnPacketReceive(...) {
       if (m_config.debugMode) {
           LogEvent(0, "Packet received");
       }
   }
   ```

### Memory Usage

**Monitoring:**

1. **Check Plugin Memory**
   ```bash
   # Monitor server memory usage
   top -p $(pidof game)
   
   # Check for memory leaks
   valgrind --leak-check=full ./game
   ```

2. **Optimize Data Structures**
   ```cpp
   // Use appropriate containers
   std::unordered_map<int, Data> m_fastLookup; // O(1) lookup
   std::vector<Data> m_sequentialData;         // Memory efficient
   ```

## Configuration Problems

### Configuration Not Loading

**Symptoms:**
- Plugin uses default values
- Configuration changes have no effect

**Solutions:**

1. **Check File Location**
   ```bash
   # Configuration should be in conf/ directory
   ls -la conf/yourplugin.conf
   ```

2. **Verify File Format**
   ```ini
   # Correct format
   key=value
   
   # Incorrect formats
   key = value  # Spaces around = may cause issues
   key: value   # Wrong separator
   ```

3. **Check File Permissions**
   ```bash
   chmod 644 conf/yourplugin.conf
   ```

### Configuration Validation Errors

**Common Issues:**

1. **Invalid Values**
   ```cpp
   // Add validation in your plugin
   bool ValidateConfig() {
       if (m_config.maxLevel < 1 || m_config.maxLevel > 999) {
           LogEvent(2, "Invalid max_level: " + std::to_string(m_config.maxLevel));
           return false;
       }
       return true;
   }
   ```

2. **Missing Required Values**
   ```cpp
   // Provide defaults for optional values
   m_config.optionalValue = GetString("optional_value", "default");
   ```

## Event System Issues

### Events Not Triggering

**Debugging Steps:**

1. **Check Plugin State**
   ```cpp
   // Events only trigger for RUNNING plugins
   if (GetState() != PluginState::RUNNING) {
       LogEvent(1, "Plugin not running, events won't trigger");
   }
   ```

2. **Verify Capabilities**
   ```cpp
   // Ensure plugin declares required capabilities
   reg.capabilities = CAPABILITY_CHARACTER_EVENTS | CAPABILITY_ITEM_EVENTS;
   ```

3. **Check Integration Points**
   ```cpp
   // Verify integration macros are called in server code
   GAME_PLUGIN_CALL_CHARACTER_LOGIN(ch);
   ```

### Event Handler Errors

**Common Problems:**

1. **Exception in Handler**
   ```cpp
   void OnCharacterLogin(LPCHARACTER ch) {
       try {
           // Your code here
       } catch (const std::exception& e) {
           LogEvent(2, "Error in OnCharacterLogin: " + std::string(e.what()));
       }
   }
   ```

2. **Blocking Operations**
   ```cpp
   // BAD: Blocking the main thread
   void OnCharacterLogin(LPCHARACTER ch) {
       sleep(5); // Don't do this!
   }
   
   // GOOD: Async operations
   void OnCharacterLogin(LPCHARACTER ch) {
       ScheduleAsyncTask([=]() {
           // Long running task
       });
   }
   ```

## Database Plugin Issues

### MySQL Connection Problems

**Common Issues:**

1. **Connection Refused**
   ```bash
   # Check MySQL is running
   systemctl status mysql
   
   # Check connection
   mysql -h localhost -u username -p
   ```

2. **Authentication Failures**
   ```sql
   -- Create plugin user
   CREATE USER 'plugin_user'@'localhost' IDENTIFIED BY 'password';
   GRANT ALL PRIVILEGES ON plugin_db.* TO 'plugin_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **Library Issues**
   ```bash
   # Install MySQL client libraries
   sudo apt-get install libmysqlclient-dev
   
   # Check library linking
   ldd plugins/db/libYourDBPlugin.so | grep mysql
   ```

### Query Performance

**Optimization:**

1. **Slow Queries**
   ```cpp
   // Log slow queries
   void OnQueryExecute(const char* query, int queryType) {
       auto start = std::chrono::high_resolution_clock::now();
       // ... query execution ...
       auto end = std::chrono::high_resolution_clock::now();
       auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
       
       if (duration.count() > 1000) { // > 1 second
           LogEvent(1, "Slow query: " + std::string(query));
       }
   }
   ```

2. **Connection Pooling**
   ```cpp
   // Use connection pooling for better performance
   class ConnectionPool {
       std::queue<MYSQL*> m_connections;
       std::mutex m_mutex;
   };
   ```

## Debugging Techniques

### Logging

**Effective Logging:**

1. **Use Appropriate Log Levels**
   ```cpp
   LogEvent(0, "Debug: Detailed information");     // Debug
   LogEvent(1, "Info: General information");       // Info
   LogEvent(2, "Warning: Something unexpected");   // Warning
   LogEvent(3, "Error: Something failed");         // Error
   LogEvent(4, "Critical: System unstable");       // Critical
   ```

2. **Structured Logging**
   ```cpp
   LogEvent(1, "Player login: " + std::string(ch->GetName()) + 
              " IP: " + std::string(ch->GetDesc()->GetHostName()) +
              " Level: " + std::to_string(ch->GetLevel()));
   ```

### Debugging Tools

1. **GDB (GNU Debugger)**
   ```bash
   gdb ./game
   (gdb) set args
   (gdb) break MyPlugin::OnCharacterLogin
   (gdb) run
   ```

2. **Valgrind (Memory Debugging)**
   ```bash
   valgrind --tool=memcheck --leak-check=full ./game
   ```

3. **Static Analysis**
   ```bash
   # Use static analysis tools
   cppcheck --enable=all src/
   clang-static-analyzer src/
   ```

## Common Error Messages

### "Plugin failed to initialize"

**Causes:**
- Configuration file missing or invalid
- Required dependencies not available
- Database connection failed
- Insufficient permissions

**Solutions:**
- Check plugin logs for specific error
- Verify configuration file exists and is valid
- Check all dependencies are installed
- Verify file permissions

### "Symbol not found"

**Causes:**
- Missing library dependencies
- Incorrect linking
- ABI compatibility issues

**Solutions:**
```bash
# Check missing symbols
nm -D plugins/game/libYourPlugin.so | grep "U "

# Check library dependencies
ldd plugins/game/libYourPlugin.so
```

### "Plugin version mismatch"

**Causes:**
- Plugin compiled for different API version
- Server version incompatible with plugin

**Solutions:**
- Recompile plugin with correct API version
- Update plugin to support current API
- Check version compatibility in manifest

## Getting Help

### Information to Provide

When seeking help, provide:

1. **Server Information**
   - Server version
   - Operating system
   - Compiler version

2. **Plugin Information**
   - Plugin name and version
   - Plugin type (game/database)
   - Source code (if possible)

3. **Error Information**
   - Complete error messages
   - Server logs (syserr, syslog)
   - Steps to reproduce

4. **Environment Information**
   - Build configuration
   - Dependencies installed
   - Configuration files

### Resources

- **Documentation**: Check `Documentation/` directory
- **Examples**: See `examples/` directories
- **Templates**: Use provided templates as reference
- **Community**: Join developer forums and discussions

### Reporting Bugs

When reporting bugs:

1. **Search existing issues** first
2. **Provide minimal reproduction case**
3. **Include all relevant information**
4. **Follow up with additional information** if requested

Remember: The more information you provide, the easier it is to help you solve the problem!
