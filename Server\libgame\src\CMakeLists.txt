# libgame/src Library
cmake_minimum_required(VERSION 3.16)

create_server_library(libgame
    SOURCES
        grid.cc
        attribute.cc
        targa.cc
    INCLUDE_DIRS
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
    DEPENDENCIES
        Server::libthecore
)

# Set output directory to match original Makefile
set_target_properties(libgame PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../lib
    ARCHIVE_OUTPUT_NAME game
)

# Disable exceptions for this library (matching original Makefile)
if(NOT WIN32)
    target_compile_options(libgame PRIVATE -fno-exceptions)
endif()
