# DB Server Executable
cmake_minimum_required(VERSION 3.16)

# Source files from the original Makefile
set(DB_SOURCES
    Config.cpp
    NetBase.cpp
    Peer.cpp
    PeerBase.cpp
    Main.cpp
    Lock.cpp
    DBManager.cpp
    Cache.cpp
    LoginData.cpp
    ClientManager.cpp
    ClientManagerPlayer.cpp
    ClientManagerLogin.cpp
    ClientManagerBoot.cpp
    ClientManagerParty.cpp
    ClientManagerGuild.cpp
    GuildManager.cpp
    HB.cpp
    PrivManager.cpp
    MoneyLog.cpp
    ItemAwardManager.cpp
    ClientManagerEventFlag.cpp
    Marriage.cpp
    Monarch.cpp
    BlockCountry.cpp
    ItemIDRangeManager.cpp
    ClientManagerHorseName.cpp
    ProtoReader.cpp
    CsvReader.cpp
    db_plugin_manager.cpp
    plugin_qid_manager.cpp

    # Common plugin files
    ${CMAKE_SOURCE_DIR}/Server/common/plugin_manager.cpp
    ${CMAKE_SOURCE_DIR}/Server/common/plugin_registry.cpp
    ${CMAKE_SOURCE_DIR}/Server/common/plugin_packet_utils.cpp
)

# Create the executable
add_executable(db ${DB_SOURCES})

# Set target properties
set_target_properties(db PROPERTIES
    CXX_STANDARD 17
    C_STANDARD 11
    FOLDER "Server/Run"
    OUTPUT_NAME "db"
    DEBUG_POSTFIX "_d"
)

# Apply ProjectZ compiler settings for Windows builds
if(WIN32)
    apply_projectz_compiler_settings(db)
endif()

# Include directories
target_include_directories(db PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/Server/common
    ${CMAKE_SOURCE_DIR}/Server/common/interfaces
    ${CMAKE_SOURCE_DIR}/Server/libthecore/include
    ${CMAKE_SOURCE_DIR}/Server/libgame/include
    ${CMAKE_SOURCE_DIR}/Server/libsql
    ${CMAKE_SOURCE_DIR}/Server/libpoly
    ${CMAKE_SOURCE_DIR}/Server/libserverkey
    ${MYSQL_INCLUDE_DIRS}
)

# Link with libraries
target_link_libraries(db PRIVATE
    Server::libthecore
    Server::libsql
    Server::libpoly
    Server::libgame
    Server::libserverkey
    ${MYSQL_LIBRARIES}
    OpenSSL::SSL
    OpenSSL::Crypto
    Threads::Threads
)

# Add library directories for MySQL
if(MYSQL_LIBRARY_DIRS)
    target_link_directories(db PRIVATE ${MYSQL_LIBRARY_DIRS})
endif()

# Platform-specific libraries
if(NOT WIN32)
    target_link_libraries(db PRIVATE dl z zstd)
else()
    target_link_libraries(db PRIVATE ws2_32)
endif()

# DB-specific compiler definitions
if(NOT WIN32)
    target_compile_definitions(db PRIVATE
        __UNIX__
        _GNU_SOURCE
    )
else()
    # Windows-specific definitions (basic ones handled by apply_projectz_compiler_settings)
    target_compile_definitions(db PRIVATE
        _CONSOLE
        _USE_32BIT_TIME_T
        _WINSOCK_DEPRECATED_NO_WARNINGS
    )
endif()

# Set output directory
set_target_properties(db PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/..
)

# Copy DB executable to the correct directory for testing
if(WIN32)
    add_custom_command(TARGET db POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        $<TARGET_FILE:db>
        ${CMAKE_SOURCE_DIR}/../Server/db/$<TARGET_FILE_NAME:db>
        COMMENT "Copying DB executable to Server/db directory"
    )
endif()
