#ifndef __INC_ISKILL_MANAGER_H__
#define __INC_ISKILL_MANAGER_H__

#include "../stl.h"
#include "../tables.h"

// Forward declarations
class CSkillProto;

/**
 * @brief Pure virtual interface for CSkillManager singleton
 *
 * Provides ABI-stable access to skill management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 *
 * This interface focuses on skill data and usage management.
 */
class ISkillManager
{
public:
    virtual ~ISkillManager() = default;

    // ============================================================================
    // SKILL TABLE MANAGEMENT
    // ============================================================================

    // Skill table access - based on actual CSkillManager usage
    virtual CSkillProto* Get(DWORD dwSkillVnum) = 0;
};

#endif // __INC_ISKILL_MANAGER_H__
