#ifndef __INC_COMPREHENSIVE_GAME_PLUGIN_H__
#define __INC_COMPREHENSIVE_GAME_PLUGIN_H__

#include "../../Server/common/plugin_interface.h"
#include "../../Server/common/plugin_game_interfaces.h"
#include "../../Server/common/plugin_manager_interfaces.h"
#include "../../Server/game/src/game_plugin_manager.h"

/**
 * @brief Comprehensive Game Plugin demonstrating full access to game objects and systems
 *
 * This plugin demonstrates how to:
 * - Access ICHARACTER interface and all its methods
 * - Use IITEM and IITEM_MANAGER for item operations
 * - Access singleton manager interfaces (ICHARACTER_MANAGER, IDESC_MANAGER, etc.)
 * - Interact with guilds, shops, parties, quests through interfaces
 * - Handle combat, skills, and affects using ABI-stable interfaces
 * - Process packets and commands
 * - Use game utilities and helper functions through interfaces
 *
 * Updated to use pure virtual interfaces for cross-compiler compatibility.
 */
class ComprehensiveGamePlugin : public IGamePlugin
{
public:
    ComprehensiveGamePlugin();
    virtual ~ComprehensiveGamePlugin();
    
    // IPlugin interface implementation
    virtual bool Initialize() override;
    virtual bool Start() override;
    virtual void Stop() override;
    virtual void Shutdown() override;
    virtual const PluginInfo& GetInfo() const override;
    virtual PluginState GetState() const override;
    
    // IGamePlugin interface - Character events (using ABI-stable interfaces)
    virtual void OnCharacterCreate(ICHARACTER* ch) override;
    virtual void OnCharacterDestroy(ICHARACTER* ch) override;
    virtual void OnCharacterLogin(ICHARACTER* ch) override;
    virtual void OnCharacterLogout(ICHARACTER* ch) override;
    virtual void OnCharacterLevelUp(ICHARACTER* ch, PLUGIN_BYTE newLevel) override;
    virtual void OnCharacterDead(ICHARACTER* ch, ICHARACTER* killer) override;
    virtual void OnCharacterRevive(ICHARACTER* ch) override;

    // IGamePlugin interface - Item events (using ABI-stable interfaces)
    virtual void OnItemCreate(IITEM* item) override;
    virtual void OnItemDestroy(IITEM* item) override;
    virtual void OnItemEquip(ICHARACTER* ch, IITEM* item) override;
    virtual void OnItemUnequip(ICHARACTER* ch, IITEM* item) override;
    virtual void OnItemUse(ICHARACTER* ch, IITEM* item) override;
    virtual void OnItemDrop(ICHARACTER* ch, IITEM* item) override;
    virtual void OnItemPickup(ICHARACTER* ch, IITEM* item) override;

    // IGamePlugin interface - Combat events (using ABI-stable interfaces)
    virtual void OnAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage) override;
    virtual void OnKill(ICHARACTER* killer, ICHARACTER* victim) override;
    virtual void OnDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage) override;

    // IGamePlugin interface - Guild events (using ABI-stable interfaces)
    virtual void OnGuildCreate(IGUILD* guild) override;
    virtual void OnGuildDestroy(IGUILD* guild) override;
    virtual void OnGuildJoin(ICHARACTER* ch, IGUILD* guild) override;
    virtual void OnGuildLeave(ICHARACTER* ch, IGUILD* guild) override;
    virtual void OnGuildWar(IGUILD* guild1, IGUILD* guild2) override;

    // IGamePlugin interface - Shop events (using ABI-stable interfaces)
    virtual void OnShopBuy(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count) override;
    virtual void OnShopSell(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count) override;

    // IGamePlugin interface - Quest events (using ABI-stable interfaces)
    virtual void OnQuestStart(ICHARACTER* ch, int questIndex) override;
    virtual void OnQuestComplete(ICHARACTER* ch, int questIndex) override;
    virtual void OnQuestGiveUp(ICHARACTER* ch, int questIndex) override;

    // IGamePlugin interface - Chat events (using ABI-stable interfaces)
    virtual void OnChat(ICHARACTER* ch, const char* message, int type) override;
    virtual void OnWhisper(ICHARACTER* from, ICHARACTER* to, const char* message) override;
    virtual void OnShout(ICHARACTER* ch, const char* message) override;
    
    // IGamePlugin interface - Command handling (using ABI-stable interfaces)
    virtual bool OnCommand(ICHARACTER* ch, const char* command, const char* args) override;

    // IGamePlugin interface - Map events (using ABI-stable interfaces)
    virtual void OnMapEnter(ICHARACTER* ch, PLUGIN_LONG mapIndex) override;
    virtual void OnMapLeave(ICHARACTER* ch, PLUGIN_LONG mapIndex) override;

    // IGamePlugin interface - System events
    virtual void OnServerStart() override;
    virtual void OnServerShutdown() override;
    virtual void OnHeartbeat() override;
    virtual void OnMinuteUpdate() override;
    virtual void OnHourUpdate() override;
    virtual void OnDayUpdate() override;
    
private:
    // Plugin state
    bool m_initialized;
    bool m_running;
    PluginInfo m_info;
    PluginState m_state;
    
    // Configuration settings
    bool m_debugMode;
    bool m_enableCharacterTracking;
    bool m_enableItemTracking;
    bool m_enableCombatTracking;
    bool m_enableGuildTracking;
    bool m_enableShopTracking;
    bool m_enableQuestTracking;
    bool m_enableChatTracking;
    bool m_enableCommandTracking;
    bool m_enableDetailedLogging;
    
    // Statistics tracking
    struct Statistics
    {
        int charactersCreated;
        int itemsCreated;
        int combatEvents;
        int questsCompleted;
        int commandsProcessed;
        int packetsProcessed;
    } m_stats;
    
    // Helper methods (updated for ABI-stable interfaces)
    void LoadConfiguration();
    void SaveStatistics();
    void DemonstrateManagerAccess();
    void DemonstrateCharacterAccess(ICHARACTER* ch);
    void DemonstrateItemAccess(IITEM* item);
    void DemonstrateGuildAccess(IGUILD* guild);
    void DemonstrateShopAccess(ISHOP* shop);
    void DemonstrateQuestAccess(ICHARACTER* ch, int questIndex);
    void DemonstrateSkillAccess(ICHARACTER* ch, PLUGIN_DWORD skillVnum);
    void DemonstrateAffectAccess(ICHARACTER* ch);
    void DemonstrateMapAccess(ICHARACTER* ch);
    void DemonstrateUtilityAccess();

    // Command handlers (updated for ABI-stable interfaces)
    bool HandleInfoCommand(ICHARACTER* ch, const std::string& args);
    bool HandleStatsCommand(ICHARACTER* ch, const std::string& args);
    bool HandleTestCommand(ICHARACTER* ch, const std::string& args);
    bool HandleItemCommand(ICHARACTER* ch, const std::string& args);
    bool HandleGuildCommand(ICHARACTER* ch, const std::string& args);
    bool HandleQuestCommand(ICHARACTER* ch, const std::string& args);
    bool HandleSkillCommand(ICHARACTER* ch, const std::string& args);
    bool HandleMapCommand(ICHARACTER* ch, const std::string& args);
    bool HandleDebugCommand(ICHARACTER* ch, const std::string& args);

    // Utility methods (updated for ABI-stable interfaces)
    bool HasPermission(ICHARACTER* ch, const std::string& permission) const;
    void SendMessage(ICHARACTER* ch, const std::string& message) const;
    void LogEvent(const std::string& event, const std::string& details = "") const;
    std::string GetCharacterInfo(ICHARACTER* ch) const;
    std::string GetItemInfo(IITEM* item) const;
    std::string GetGuildInfo(IGUILD* guild) const;
    std::string GetShopInfo(ISHOP* shop) const;
};

#endif // __INC_COMPREHENSIVE_GAME_PLUGIN_H__
