<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectName>liblua</ProjectName>
    <ProjectGuid>{EC666345-5F4E-4A4E-8C66-C7A6326F8235}</ProjectGuid>
    <RootNamespace>liblua</RootNamespace>
    <SccProjectName>
    </SccProjectName>
    <SccAuxPath>
    </SccAuxPath>
    <SccLocalPath>
    </SccLocalPath>
    <SccProvider>
    </SccProvider>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>16.0.29511.113</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(ProjectDir)lib\</OutDir>
    <IntDir>$(ProjectDir)win32/$(Configuration)\</IntDir>
    <TargetName>$(ProjectName)_d</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(ProjectDir)lib\</OutDir>
    <IntDir>$(ProjectDir)win32/$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE;_USE_32BIT_TIME_T;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <PrecompiledHeader />
      <ProgramDataBaseFileName>$(OutDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <CompileAs>CompileAsCpp</CompileAs>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <LanguageStandard_C>Default</LanguageStandard_C>
      <SupportJustMyCode>true</SupportJustMyCode>
      <MinimalRebuild>true</MinimalRebuild>
    </ClCompile>
    <Lib>
      <OutputFile>$(OutDir)$(ProjectName)_d.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(ProjectDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader />
      <ProgramDataBaseFileName>$(OutDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>CompileAsCpp</CompileAs>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <LanguageStandard_C>Default</LanguageStandard_C>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="src\lapi.c" />
    <ClCompile Include="src\lcode.c" />
    <ClCompile Include="src\ldebug.c" />
    <ClCompile Include="src\ldo.c" />
    <ClCompile Include="src\ldump.c" />
    <ClCompile Include="src\lfunc.c" />
    <ClCompile Include="src\lgc.c" />
    <ClCompile Include="src\lib\lauxlib.c" />
    <ClCompile Include="src\lib\lbaselib.c" />
    <ClCompile Include="src\lib\ldblib.c" />
    <ClCompile Include="src\lib\liolib.c" />
    <ClCompile Include="src\lib\lmathlib.c" />
    <ClCompile Include="src\lib\loadlib.c" />
    <ClCompile Include="src\lib\lstrlib.c" />
    <ClCompile Include="src\lib\ltablib.c" />
    <ClCompile Include="src\llex.c">
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">CompileAsCpp</CompileAs>
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="src\lmem.c" />
    <ClCompile Include="src\lobject.c" />
    <ClCompile Include="src\lopcodes.c" />
    <ClCompile Include="src\lparser.c" />
    <ClCompile Include="src\lstate.c" />
    <ClCompile Include="src\lstring.c" />
    <ClCompile Include="src\ltable.c" />
    <ClCompile Include="src\ltests.c" />
    <ClCompile Include="src\ltm.c" />
    <ClCompile Include="src\lundump.c" />
    <ClCompile Include="src\lvm.c" />
    <ClCompile Include="src\lzio.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\lauxlib.h" />
    <ClInclude Include="include\lua.h" />
    <ClInclude Include="include\lualib.h" />
    <ClInclude Include="src\lapi.h" />
    <ClInclude Include="src\lcode.h" />
    <ClInclude Include="src\ldebug.h" />
    <ClInclude Include="src\ldo.h" />
    <ClInclude Include="src\lfunc.h" />
    <ClInclude Include="src\lgc.h" />
    <ClInclude Include="src\llex.h" />
    <ClInclude Include="src\llimits.h" />
    <ClInclude Include="src\lmem.h" />
    <ClInclude Include="src\lobject.h" />
    <ClInclude Include="src\lopcodes.h" />
    <ClInclude Include="src\lparser.h" />
    <ClInclude Include="src\lstate.h" />
    <ClInclude Include="src\lstring.h" />
    <ClInclude Include="src\ltable.h" />
    <ClInclude Include="src\ltm.h" />
    <ClInclude Include="src\lundump.h" />
    <ClInclude Include="src\lvm.h" />
    <ClInclude Include="src\lzio.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>