/*
* $Id: lgc.h,v 1.19a 2003/02/28 19:45:15 roberto Exp $
* Garbage Collector
* See Copyright Notice in lua.h
*/

#ifndef __INC_LGC_H__
#define __INC_LGC_H__

#include "lobject.h"

#define luaC_checkGC(L) { lua_assert(!(L->ci->state & CI_CALLING)); \
	if (G(L)->nblocks >= G(L)->GCthreshold) luaC_collectgarbage(L); }

size_t luaC_separateudata(lua_State* L);
void luaC_callGCTM(lua_State* L);
void luaC_sweep(lua_State* L, int all);
void luaC_collectgarbage(lua_State* L);
void luaC_link(lua_State* L, GCObject* o, lu_byte tt);

#endif // __INC_LGC_H__
