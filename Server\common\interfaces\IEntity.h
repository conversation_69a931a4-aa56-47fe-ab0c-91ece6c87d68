#ifndef __INC_IENTITY_H__
#define __INC_IENTITY_H__

#include "../stl.h"

// Forward declarations
#include "../game/src/typedef.h"
class VID;
/**
 * @brief Pure virtual interface for CEntity class
 *
 * Provides ABI-stable access to basic entity functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 *
 * This interface focuses on core entity operations that all entities share.
 */
class IEntity
{
public:
    virtual ~IEntity() = default;

    // ============================================================================
    // CORE ENTITY PROPERTIES
    // ============================================================================

    // Type management
    virtual void SetType(int type) = 0;
    virtual int GetType() const = 0;
    virtual bool IsType(int type) const = 0;

    // VID (Virtual ID) management - implemented by derived classes
    virtual void SetVID(DWORD vid) = 0;
    virtual DWORD GetVID() const = 0;

    // ============================================================================
    // POSITION AND MAP
    // ============================================================================

    // Map management
    virtual void SetMapIndex(long mapIndex) = 0;
    virtual long GetMapIndex() const = 0;

    // Position management
    virtual void SetPosition(long x, long y) = 0;
    virtual void GetPosition(long& x, long& y) const = 0;
    virtual void SetXYZ(long x, long y, long z) = 0;
    virtual void GetXYZ(long& x, long& y, long& z) const = 0;
    virtual long GetX() const = 0;
    virtual long GetY() const = 0;
    virtual long GetZ() const = 0;

    // ============================================================================
    // PACKET ENCODING (PURE VIRTUAL)
    // ============================================================================

    // These must be implemented by concrete classes
    virtual void EncodeInsertPacket(LPENTITY entity) = 0;
    virtual void EncodeRemovePacket(LPENTITY entity) = 0;

    // ============================================================================
    // VIEW MANAGEMENT
    // ============================================================================

    // View system for entity visibility
    virtual void ViewCleanup() = 0;
    virtual void ViewInsert(LPENTITY entity, bool recursive = true) = 0;
    virtual void ViewRemove(LPENTITY entity, bool recursive = true) = 0;
    virtual void ViewReencode() = 0;
    virtual int GetViewAge() const = 0;

    // ============================================================================
    // NETWORK AND COMMUNICATION
    // ============================================================================

    // Sectree and spatial management
    virtual void UpdateSectree() = 0;
    virtual void PacketAround(const void* data, int bytes, LPENTITY except = nullptr) = 0;
    virtual void PacketView(const void* data, int bytes, LPENTITY except = nullptr) = 0;

    // Descriptor management
    virtual void BindDesc(LPDESC desc) = 0;
    virtual LPDESC GetDesc() const = 0;

    // ============================================================================
    // ENTITY STATE
    // ============================================================================

    // Observer mode
    virtual void SetObserverMode(bool flag) = 0;
    virtual bool IsObserverMode() const = 0;

    // Entity lifecycle
    virtual bool IsDestroyed() const = 0;
};

#endif // __INC_IENTITY_H__
