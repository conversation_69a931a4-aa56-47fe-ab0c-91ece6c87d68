#ifndef __INC_CHAR_MANAGER_API_H__
#define __INC_CHAR_MANAGER_API_H__

/**
 * @file char_manager_api.h
 * @brief External C API for CHARACTER_MANAGER class functionality
 * 
 * This file provides a C API wrapper around the CHARACTER_MANAGER singleton
 * to ensure cross-compiler compatibility between game server (clang) and 
 * plugins (gcc/vs).
 * 
 * All functions use extern "C" linkage to maintain ABI stability across
 * different compilers and prevent C++ name mangling issues.
 * 
 * Design principles:
 * - Pure C interface with no C++ features exposed
 * - Opaque handles to hide C++ implementation details
 * - Simple data types (int, char*, etc.) for parameters
 * - Error codes instead of exceptions
 * - No STL containers in the interface
 */

#ifdef __cplusplus
extern "C" {
#endif

// Include the character API for shared types
#include "char_api.h"

// ============================================================================
// FORWARD DECLARATIONS AND TYPES
// ============================================================================

// Opaque handle to CHARACTER_MANAGER instance
typedef struct CharacterManagerHandle* CharacterManagerHandle_t;

// Character search callback function type
typedef void (*CharacterCallback)(CharacterHandle_t character, void* user_data);

// Spawn parameters structure
typedef struct {
    DWORD vnum;
    long map_index;
    long x;
    long y;
    long z;
    int rotation;
    int spawn_motion;  // 0 = false, 1 = true
    int show;          // 0 = false, 1 = true
    int aggressive;    // 0 = false, 1 = true
} SpawnParams;

// ============================================================================
// MANAGER ACCESS
// ============================================================================

/**
 * @brief Get the CHARACTER_MANAGER singleton instance
 * @return Handle to CHARACTER_MANAGER instance, NULL on error
 */
CharacterManagerHandle_t char_manager_api_get_instance(void);

// ============================================================================
// CHARACTER CREATION AND DESTRUCTION
// ============================================================================

/**
 * @brief Create a new character
 * @param manager_handle Manager handle
 * @param name Character name
 * @param player_id Player ID (0 for NPCs)
 * @param character_handle Output parameter for created character handle
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_create_character(
    CharacterManagerHandle_t manager_handle,
    const char* name,
    DWORD player_id,
    CharacterHandle_t* character_handle
);

/**
 * @brief Destroy a character
 * @param manager_handle Manager handle
 * @param character_handle Character to destroy
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_destroy_character(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle
);

// ============================================================================
// CHARACTER LOOKUP
// ============================================================================

/**
 * @brief Find character by VID
 * @param manager_handle Manager handle
 * @param vid Virtual ID to search for
 * @param character_handle Output parameter for found character (NULL if not found)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_find_by_vid(
    CharacterManagerHandle_t manager_handle,
    DWORD vid,
    CharacterHandle_t* character_handle
);

/**
 * @brief Find PC by name
 * @param manager_handle Manager handle
 * @param name Character name to search for
 * @param character_handle Output parameter for found character (NULL if not found)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_find_pc_by_name(
    CharacterManagerHandle_t manager_handle,
    const char* name,
    CharacterHandle_t* character_handle
);

/**
 * @brief Find character by player ID
 * @param manager_handle Manager handle
 * @param player_id Player ID to search for
 * @param character_handle Output parameter for found character (NULL if not found)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_find_by_pid(
    CharacterManagerHandle_t manager_handle,
    DWORD player_id,
    CharacterHandle_t* character_handle
);

// ============================================================================
// MOB SPAWNING
// ============================================================================

/**
 * @brief Spawn a mob at specific coordinates
 * @param manager_handle Manager handle
 * @param params Spawn parameters
 * @param character_handle Output parameter for spawned character
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_spawn_mob(
    CharacterManagerHandle_t manager_handle,
    const SpawnParams* params,
    CharacterHandle_t* character_handle
);

/**
 * @brief Spawn a mob in a random position within a range
 * @param manager_handle Manager handle
 * @param vnum Mob vnum
 * @param map_index Map index
 * @param start_x Start X coordinate
 * @param start_y Start Y coordinate
 * @param end_x End X coordinate
 * @param end_y End Y coordinate
 * @param is_exception Exception flag
 * @param spawn_motion Spawn motion flag
 * @param aggressive Aggressive flag
 * @param character_handle Output parameter for spawned character
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_spawn_mob_range(
    CharacterManagerHandle_t manager_handle,
    DWORD vnum,
    long map_index,
    int start_x,
    int start_y,
    int end_x,
    int end_y,
    int is_exception,
    int spawn_motion,
    int aggressive,
    CharacterHandle_t* character_handle
);

/**
 * @brief Spawn a mob at random position on map
 * @param manager_handle Manager handle
 * @param vnum Mob vnum
 * @param map_index Map index
 * @param character_handle Output parameter for spawned character
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_spawn_mob_random_position(
    CharacterManagerHandle_t manager_handle,
    DWORD vnum,
    long map_index,
    CharacterHandle_t* character_handle
);

// ============================================================================
// CHARACTER ITERATION
// ============================================================================

/**
 * @brief Iterate over all PC characters
 * @param manager_handle Manager handle
 * @param callback Callback function to call for each PC
 * @param user_data User data to pass to callback
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_for_each_pc(
    CharacterManagerHandle_t manager_handle,
    CharacterCallback callback,
    void* user_data
);

/**
 * @brief Get characters by race number
 * @param manager_handle Manager handle
 * @param race_num Race number to search for
 * @param callback Callback function to call for each matching character
 * @param user_data User data to pass to callback
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_get_characters_by_race(
    CharacterManagerHandle_t manager_handle,
    DWORD race_num,
    CharacterCallback callback,
    void* user_data
);

// ============================================================================
// DELAYED SAVE OPERATIONS
// ============================================================================

/**
 * @brief Add character to delayed save list
 * @param manager_handle Manager handle
 * @param character_handle Character to save later
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_delayed_save(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle
);

/**
 * @brief Flush delayed save for specific character
 * @param manager_handle Manager handle
 * @param character_handle Character to flush save for
 * @param was_saved Output parameter (1 if save was performed, 0 if not)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_flush_delayed_save(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* was_saved
);

/**
 * @brief Process all delayed saves
 * @param manager_handle Manager handle
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_process_delayed_save(
    CharacterManagerHandle_t manager_handle
);

// ============================================================================
// STATE MANAGEMENT
// ============================================================================

/**
 * @brief Add character to state list
 * @param manager_handle Manager handle
 * @param character_handle Character to add
 * @param was_added Output parameter (1 if added, 0 if already in list)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_add_to_state_list(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* was_added
);

/**
 * @brief Remove character from state list
 * @param manager_handle Manager handle
 * @param character_handle Character to remove
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_remove_from_state_list(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle
);

// ============================================================================
// RATE MANAGEMENT
// ============================================================================

/**
 * @brief Get mob item rate for character
 * @param manager_handle Manager handle
 * @param character_handle Character handle
 * @param rate Output parameter for item rate
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_get_mob_item_rate(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* rate
);

/**
 * @brief Get mob damage rate for character
 * @param manager_handle Manager handle
 * @param character_handle Character handle
 * @param rate Output parameter for damage rate
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_get_mob_damage_rate(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* rate
);

/**
 * @brief Get mob gold amount rate for character
 * @param manager_handle Manager handle
 * @param character_handle Character handle
 * @param rate Output parameter for gold amount rate
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_get_mob_gold_amount_rate(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* rate
);

/**
 * @brief Get mob experience rate for character
 * @param manager_handle Manager handle
 * @param character_handle Character handle
 * @param rate Output parameter for experience rate
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_get_mob_exp_rate(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* rate
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * @brief Update character manager (called each pulse)
 * @param manager_handle Manager handle
 * @param pulse Current pulse value
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_update(
    CharacterManagerHandle_t manager_handle,
    int pulse
);

/**
 * @brief Register race number for tracking
 * @param manager_handle Manager handle
 * @param race_num Race number to register
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_manager_api_register_race_num(
    CharacterManagerHandle_t manager_handle,
    DWORD race_num
);

#ifdef __cplusplus
}
#endif

#endif // __INC_CHAR_MANAGER_API_H__
