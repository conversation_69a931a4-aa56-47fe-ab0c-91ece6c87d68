#ifndef __INC_IGUILD_H__
#define __INC_IGUILD_H__

#include "../stl.h"
#include "../tables.h"
#include "../../game/src/typedef.h"
struct SGuildMember;
typedef SGuildMember TGuildMember;;
/**
 * @brief Pure virtual interface for CGuild class
 * 
 * Provides ABI-stable access to guild functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on guild properties and operations.
 */
class IGUILD
{
public:
    virtual ~IGUILD() = default;
    
    // ============================================================================
    // BASIC GUILD PROPERTIES
    // ============================================================================
    
    // Guild identification
    virtual DWORD GetID() const = 0;
    virtual const char* GetName() const = 0;
    virtual DWORD GetMasterPID() const = 0;
    virtual LPCHARACTER GetMasterCharacter() = 0;

    // Guild level and experience
    virtual BYTE GetLevel() const = 0;
    
    // ============================================================================
    // GUILD MEMBERS
    // ============================================================================
    
    // Member management
    virtual void RequestAddMember(LPCHARACTER ch, int grade = 15) = 0;
    virtual bool RequestRemoveMember(DWORD pid) = 0;
    virtual bool RemoveMember(DWORD pid) = 0;
    virtual void ChangeMemberGrade(DWORD pid, BYTE grade) = 0;
    virtual bool ChangeMemberGeneral(DWORD pid, BYTE is_general) = 0;
    
    // Member queries
    virtual int GetMemberCount() = 0;
    virtual int GetMaxMemberCount() = 0;
    virtual TGuildMember* GetMember(DWORD pid) = 0;
   
    // ============================================================================
    // GUILD LEADERSHIP
    // ============================================================================
    
    // Leadership management
    virtual bool ChangeMasterTo(DWORD newMasterPID) = 0;
    
    // ============================================================================
    // GUILD COMMUNICATION
    // ============================================================================
    
    // Guild chat and messaging
    virtual void Chat(const char* c_pszText) = 0;
    virtual void P2PChat(const char* c_pszText) = 0;
    virtual void SendGuildInfoPacket(LPCHARACTER ch) = 0;
    virtual void SendListPacket(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // GUILD SKILLS
    // ============================================================================
    
    // Skill management
    virtual void UseSkill(DWORD dwVnum, LPCHARACTER ch, DWORD pid) = 0;

    
    // ============================================================================
    // GUILD FINANCES
    // ============================================================================
    
    // Guild money management
    virtual int GetGuildMoney() const = 0;

    
    // ============================================================================
    // GUILD WAR
    // ============================================================================
    
    // War management
    virtual bool DeclareWar(DWORD guild_id, BYTE type, BYTE state) = 0;
    virtual void RefuseWar(DWORD targetGuildID) = 0;
    virtual void StartWar(DWORD targetGuildID) = 0;
    virtual void EndWar(DWORD targetGuildID) = 0;

    
    // War statistics
    virtual void SetWarScoreAgainstTo(DWORD guild_opponent, int newpoint) = 0;
    virtual int GetWarScoreAgainstTo(DWORD guild_opponent) = 0;

    
    // ============================================================================
    // GUILD LAND AND BUILDING
    // ============================================================================
    
    // Guild land management
    virtual bool HasLand() = 0;

    

    // ============================================================================
    // GUILD VALIDATION
    // ============================================================================
    

    // Permission checks
    virtual bool HasGradeAuth(int grade, int auth_flag) const = 0;
    

    // ============================================================================
    // UTILITY FUNCTIONS
    // ============================================================================

    // Guild events
    virtual void LoginMember(LPCHARACTER ch) = 0;
    virtual void LogoutMember(LPCHARACTER ch) = 0;

};

#endif // __INC_IGUILD_H__
