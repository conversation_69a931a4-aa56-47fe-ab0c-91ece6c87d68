# Plugin Examples CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# =================================================================
# COMPREHENSIVE WORKING PLUGIN
# =================================================================

create_plugin_library(ComprehensiveWorkingPlugin "Game"
    SOURCES ${CMAKE_SOURCE_DIR}/External/PluginTemplate/Examples/ComprehensiveWorkingPlugin.cpp
)

# =================================================================
# TEMPLATE FILES (NOT COMPILED - FOR REFERENCE ONLY)
# =================================================================
# The following files are templates and examples for developers:
# - BasicDBPlugin.cpp (template)
# - BasicGamePlugin.cpp (template)
# - AdvancedDBPlugin.cpp (template)
# - AdvancedGamePlugin.cpp (template)
# - ComprehensiveExamplePlugin.cpp (template)
#
# These are not compiled as they contain incomplete code for demonstration.
# Use ComprehensiveWorkingPlugin.cpp as the working example.
# Template files are available in External/PluginTemplate/Examples/

# =================================================================
# PLUGIN CONFIGURATION FILES
# =================================================================

# Create plugin configuration files
file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/plugins/config)

# Create configuration for ComprehensiveWorkingPlugin
file(WRITE ${CMAKE_SOURCE_DIR}/plugins/config/ComprehensiveWorkingPlugin.json
"{
    \"name\": \"ComprehensiveWorkingPlugin\",
    \"version\": \"1.0.0\",
    \"enabled\": true,
    \"settings\": {
        \"enableCharacterEvents\": true,
        \"enableItemEvents\": true,
        \"enableCombatEvents\": true,
        \"enableGuildEvents\": true,
        \"enableShopEvents\": true,
        \"enableQuestEvents\": true,
        \"enableSystemEvents\": true,
        \"enableDetailedLogging\": true
    }
}")

# =================================================================
# DOCUMENTATION REFERENCE
# =================================================================
# Documentation is available in External/PluginTemplate/Documentation/
# Configuration examples are available in External/PluginTemplate/PluginExample/config/

# =================================================================
# INSTALLATION
# =================================================================

# Install working plugin
install(TARGETS ComprehensiveWorkingPlugin
    RUNTIME DESTINATION plugins/game
    LIBRARY DESTINATION plugins/game
)

# Install configuration files
install(FILES
    ${CMAKE_SOURCE_DIR}/plugins/config/ComprehensiveWorkingPlugin.json
    DESTINATION plugins/config
)

# Install template files for reference
install(FILES
    ${CMAKE_SOURCE_DIR}/External/PluginTemplate/Examples/BasicDBPlugin.cpp
    ${CMAKE_SOURCE_DIR}/External/PluginTemplate/Examples/BasicGamePlugin.cpp
    ${CMAKE_SOURCE_DIR}/External/PluginTemplate/Examples/AdvancedDBPlugin.cpp
    ${CMAKE_SOURCE_DIR}/External/PluginTemplate/Examples/AdvancedGamePlugin.cpp
    ${CMAKE_SOURCE_DIR}/External/PluginTemplate/Examples/ComprehensiveExamplePlugin.cpp
    DESTINATION examples/plugins/templates
    OPTIONAL
)

# Install documentation
install(DIRECTORY ${CMAKE_SOURCE_DIR}/External/PluginTemplate/Documentation/
    DESTINATION docs/plugins
    OPTIONAL
)

# Install configuration examples
install(DIRECTORY ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/config/
    DESTINATION examples/plugins/config
    OPTIONAL
)

# =================================================================
# CUSTOM TARGETS
# =================================================================

# Create a target to build all example plugins
add_custom_target(AllExamplePlugins
    DEPENDS ComprehensiveWorkingPlugin
)

# Create a target to clean plugin outputs
add_custom_target(CleanExamplePlugins
    COMMAND ${CMAKE_COMMAND} -E remove_directory "${CMAKE_SOURCE_DIR}/plugins/game"
    COMMENT "Cleaning example plugin output directory"
)

# Print configuration information
message(STATUS "=== Plugin Examples Configuration ===")
message(STATUS "Working Plugin: ComprehensiveWorkingPlugin")
message(STATUS "Template Files: BasicDBPlugin, BasicGamePlugin, AdvancedDBPlugin, AdvancedGamePlugin, ComprehensiveExamplePlugin")
message(STATUS "Output directory: ${CMAKE_SOURCE_DIR}/plugins/game")
message(STATUS "=====================================")
