#ifndef __INC_ILOGMANAGER_H__
#define __INC_ILOGMANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

/**
 * @brief Pure virtual interface for LogManager singleton
 * 
 * Provides ABI-stable access to logging functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on game event logging and database logging.
 */
class ILogManager
{
public:
    virtual ~ILogManager() = default;
    
    // ============================================================================
    // CONNECTION MANAGEMENT
    // ============================================================================
    
    // Connection status
    virtual bool IsConnected() = 0;
    virtual bool Connect(const char* host, const int port, const char* user, const char* pwd, const char* db) = 0;
    
    // ============================================================================
    // ITEM LOGGING
    // ============================================================================
    
    // Item operations
    virtual void ItemLog(DWORD dwPID, DWORD x, DWORD y, DWORD dwItemID, const char* c_pszText, const char* c_pszHint, const char* c_pszIP, DWORD dwVnum) = 0;
    virtual void ItemLog(LPCHARACTER ch, LPITEM item, const char* c_pszText, const char* c_pszHint) = 0;
    virtual void ItemLog(LPCHARACTER ch, int itemID, int itemVnum, const char* c_pszText, const char* c_pszHint) = 0;
    
    // ============================================================================
    // CHARACTER LOGGING
    // ============================================================================
    
    // Character operations
    virtual void CharLog(DWORD dwPID, DWORD x, DWORD y, DWORD dw, const char* c_pszText, const char* c_pszHint, const char* c_pszIP) = 0;
    virtual void CharLog(LPCHARACTER ch, DWORD dw, const char* c_pszText, const char* c_pszHint) = 0;
    
    // ============================================================================
    // GOLD AND MONEY LOGGING
    // ============================================================================
    
    // Gold operations
    virtual void GoldLog(DWORD dwPID, int iGold, const char* c_pszText, const char* c_pszHint) = 0;
    virtual void GoldLog(LPCHARACTER ch, int iGold, const char* c_pszText, const char* c_pszHint) = 0;
    
    // ============================================================================
    // SPECIALIZED LOGGING
    // ============================================================================
    
    // Level and experience
    virtual void LevelLog(LPCHARACTER ch, unsigned int level, unsigned int playhour) = 0;
    
    // PvP and combat
    virtual void BootLog(const char* c_pszHostName, BYTE bChannel) = 0;
    virtual void LoginLog(bool isLogin, DWORD dwAccountID, DWORD dwPID, int iLevel, BYTE bJob, DWORD dwPlayTime) = 0;
    virtual void MoneyLog(BYTE type, DWORD vnum, int gold) = 0;
    
    // Refining and crafting
    virtual void RefineLog(DWORD pid, const char* item_name, DWORD item_id, int item_refine_level, int is_success, const char* how) = 0;
    virtual void CubeLog(DWORD dwPID, DWORD x, DWORD y, DWORD item_vnum, DWORD item_uid, int item_count, int gold) = 0;
    virtual void SpeedHackLog(DWORD pid, DWORD x, DWORD y, int hack_count) = 0;
    
    // Chat and communication
    virtual void ChatLog(BYTE channel, DWORD dvid, const char* c_pszName, DWORD pid, const char* c_pszText) = 0;
    
    // Guild operations
    virtual void GMCommandLog(DWORD dwPID, const char* szName, const char* szIP, BYTE byChannel, const char* szCommand) = 0;
    virtual void DragonSlayLog(DWORD dwGuildID, DWORD dwDragonVnum, DWORD dwStartTime, DWORD dwEndTime) = 0;
    
    // Security
    virtual void HackShieldLog(unsigned long ErrorCode, LPCHARACTER ch) = 0;
    
#if defined(__MAILBOX__)
    // Mail system
    virtual void MailLog(const char* const szName, const char* const szWho, const char* const szTitle, const char* const szMessage, const bool bIsGM, const DWORD dwItemVnum, const DWORD dwItemCount, const int iYang, const int iWon) = 0;
#endif
};

#endif // __INC_ILOGMANAGER_H__
