#ifndef __INC_PLUGIN_INTERFACE_H__
#define __INC_PLUGIN_INTERFACE_H__

#include "stl.h"
#include <memory>
#include <string>
#include <vector>

#ifdef _WIN32
#include <windows.h>
typedef HMODULE PluginHandle_t;
#else
#include <dlfcn.h>
typedef void* PluginHandle_t;
#endif

// Forward declarations
class IPlugin;
class IGamePlugin;
class IDBPlugin;
class PluginConfig;

// Plugin version structure
struct PluginVersion
{
    int major;
    int minor;
    int patch;

    PluginVersion(int maj = 1, int min = 0, int p = 0)
        : major(maj), minor(min), patch(p) {}

    PluginVersion(const char* versionStr)
    {
        // Parse version string like "1.0.0"
        major = 1; minor = 0; patch = 0;
        std::string str(versionStr);
        size_t pos1 = str.find('.');
        if (pos1 != std::string::npos) {
            major = std::stoi(str.substr(0, pos1));
            size_t pos2 = str.find('.', pos1 + 1);
            if (pos2 != std::string::npos) {
                minor = std::stoi(str.substr(pos1 + 1, pos2 - pos1 - 1));
                patch = std::stoi(str.substr(pos2 + 1));
            } else {
                minor = std::stoi(str.substr(pos1 + 1));
            }
        } else {
            major = std::stoi(str);
        }
    }

    PluginVersion(const std::string& versionStr) : PluginVersion(versionStr.c_str()) {}

    bool IsCompatible(const PluginVersion& other) const
    {
        return major == other.major && minor >= other.minor;
    }

    std::string ToString() const
    {
        return std::to_string(major) + "." + std::to_string(minor) + "." + std::to_string(patch);
    }
};

// Plugin states - unified enum
enum class PluginState
{
    PLUGIN_UNLOADED,
    PLUGIN_LOADING,
    PLUGIN_LOADED,
    PLUGIN_INITIALIZING,
    PLUGIN_INITIALIZED,
    PLUGIN_RUNNING,
    PLUGIN_STOPPING,
    PLUGIN_STOPPED,
    PLUGIN_UNLOADING,
    PLUGIN_ERROR
};

// Plugin error types
enum class PluginError
{
    NONE,
    FILE_NOT_FOUND,
    INVALID_FORMAT,
    MISSING_SYMBOLS,
    INITIALIZATION_FAILED,
    DEPENDENCY_MISSING,
    VERSION_MISMATCH,
    CONFIGURATION_ERROR,
    RUNTIME_ERROR
};

// Plugin metadata - unified structure
struct PluginInfo
{
    std::string name;
    std::string description;
    std::string author;
    std::string filePath;
    std::string configPath;  // Configuration file path
    PluginVersion version;
	PluginVersion requiredApiVersion; // Required API version for compatibility
    std::vector<std::string> dependencies;
    PluginState state;
    PluginError lastError;
    std::string errorMessage;
    PluginHandle_t handle;
    std::shared_ptr<IPlugin> instance;  // Plugin instance
    std::shared_ptr<PluginConfig> config;  // Plugin configuration

    PluginInfo() : version(1, 0, 0), requiredApiVersion(1, 0, 0),
                   state(PluginState::PLUGIN_UNLOADED), lastError(PluginError::NONE), handle(nullptr) {}
};
// Base plugin interface - declare first
class IPlugin
{
public:
    virtual ~IPlugin() = default;

    // Plugin lifecycle methods
    virtual bool Initialize() = 0;
    virtual bool Start() = 0;
    virtual void Stop() = 0;
    virtual void Shutdown() = 0;
    
    // Plugin information
    virtual const PluginInfo& GetInfo() const = 0;
    virtual PluginState GetState() const = 0;
    virtual void SetState(PluginState state) { m_state = state; }
    
    // Plugin configuration
    virtual bool LoadConfig(const std::string& configPath) = 0;
    //virtual void SaveConfig(const std::string& configPath) = 0;
    
    // Plugin events
    virtual void OnPluginLoaded(const std::string& pluginName) {}
    virtual void OnPluginUnloaded(const std::string& pluginName) {}
    
protected:
    PluginState m_state = PluginState::PLUGIN_UNLOADED;
};

// Forward declaration - actual definition in game-specific headers
class IGamePlugin;

// Database-specific plugin interface - defined in DB-specific headers

// Plugin factory function types
typedef IPlugin* (*CreatePluginFunc)();
typedef void (*DestroyPluginFunc)(IPlugin*);

// Plugin handle structure
struct PluginHandle
{
    PluginHandle_t libraryHandle;
    std::shared_ptr<IPlugin> plugin;
    CreatePluginFunc createFunc;
    DestroyPluginFunc destroyFunc;
    std::shared_ptr<PluginInfo> info;
    bool isLoaded;

    PluginHandle() : libraryHandle(nullptr), createFunc(nullptr),
                     destroyFunc(nullptr), isLoaded(false) {}
};

// Plugin export macros
#define PLUGIN_EXPORT extern "C"

#define DECLARE_PLUGIN(ClassName) \
    PLUGIN_EXPORT std::unique_ptr<IPlugin> CreatePlugin() { \
        return std::make_unique<ClassName>(); \
    } \
    PLUGIN_EXPORT void DestroyPlugin(IPlugin* plugin) { \
        delete plugin; \
    }

#endif // __INC_PLUGIN_INTERFACE_H__
