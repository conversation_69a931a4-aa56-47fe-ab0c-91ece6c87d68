# Game Server Executable
cmake_minimum_required(VERSION 3.16)

# C source files from the original Makefile
set(GAME_C_SOURCES
    minilzo.c
)

# C++ source files from the original Makefile
set(GAME_CPP_SOURCES
    affect.cpp ani.cpp arena.cpp auth_brazil.cpp banword.cpp battle.cpp BattleArena.cpp blend_item.cpp block_country.cpp
    BlueDragon.cpp BlueDragon_Binder.cpp buff_on_attributes.cpp buffer_manager.cpp building.cpp castle.cpp changelook.cpp char.cpp
    char_acce.cpp char_affect.cpp char_aura.cpp char_battle.cpp char_change_empire.cpp char_dragonsoul.cpp char_horse.cpp char_item.cpp
    char_manager.cpp char_quickslot.cpp char_resist.cpp char_skill.cpp char_state.cpp cipher.cpp ClientPackageCryptInfo.cpp cmd.cpp cmd_emotion.cpp
    cmd_general.cpp cmd_gm.cpp cmd_oxevent.cpp config.cpp constants.cpp crc32.cpp CsvReader.cpp cube.cpp dawnmist_dungeon.cpp db.cpp desc.cpp
    desc_client.cpp desc_manager.cpp desc_p2p.cpp dev_log.cpp dragon_soul_table.cpp DragonLair.cpp DragonSoul.cpp dungeon.cpp empire_text_convert.cpp
    entity.cpp entity_view.cpp event.cpp event_queue.cpp exchange.cpp file_loader.cpp FileMonitor_FreeBSD.cpp fishing.cpp FSM.cpp GemShop.cpp gm.cpp
    group_text_parse_tree.cpp guild.cpp guild_manager.cpp guild_war.cpp horse_rider.cpp horsename_manager.cpp input.cpp input_auth.cpp input_db.cpp
    input_login.cpp input_main.cpp input_p2p.cpp input_udp.cpp ip_ban.cpp item.cpp item_addon.cpp item_apply_random_table.cpp item_attribute.cpp
    item_manager.cpp item_manager_idrange.cpp item_manager_read_tables.cpp locale.cpp locale_service.cpp log.cpp login_data.cpp LootFilter.cpp
    lzo_manager.cpp MailBox.cpp map_location.cpp MarkConvert.cpp MarkImage.cpp MarkManager.cpp marriage.cpp messenger_manager.cpp ingame_event_manager.cpp
    minigame_catchking.cpp minigame_rumi.cpp minigame_yutnori.cpp mining.cpp mob_manager.cpp monarch.cpp motion.cpp mt_thunder_dungeon.cpp over9refine.cpp OXEvent.cpp
    p2p.cpp packet_info.cpp panama.cpp party.cpp pcbang.cpp PetSystem.cpp polymorph.cpp priv_manager.cpp pvp.cpp questevent.cpp questlua.cpp
    questlua_affect.cpp questlua_arena.cpp questlua_attr67add.cpp questlua_ba.cpp questlua_building.cpp questlua_danceevent.cpp questlua_dragonlair.cpp
    questlua_dragonsoul.cpp questlua_dungeon.cpp questlua_forked.cpp questlua_game.cpp questlua_global.cpp questlua_guild.cpp questlua_horse.cpp
    questlua_item.cpp questlua_marriage.cpp questlua_mgmt.cpp questlua_monarch.cpp questlua_npc.cpp questlua_oxevent.cpp questlua_party.cpp questlua_pc.cpp
    questlua_pet.cpp questlua_quest.cpp questlua_defense_wave.cpp questlua_target.cpp questmanager.cpp questnpc.cpp questpc.cpp Ranking.cpp refine.cpp
    regen.cpp safebox.cpp sectree.cpp sectree_manager.cpp sequence.cpp defense_wave.cpp shop.cpp shop_manager.cpp shopEx.cpp skill.cpp skill_power.cpp
    start_position.cpp target.cpp text_file_loader.cpp threeway_war.cpp TrafficProfiler.cpp trigger.cpp utils.cpp vector.cpp war_map.cpp wedding.cpp xmas_event.cpp
    guild_dragonlair.cpp questlua_guild_dragonlair.cpp minigame_roulette.cpp flower_event.cpp
    # Plugin system files
    plugin_config.cpp plugin_loader.cpp game_plugin_manager.cpp plugin_packet_manager.cpp
    # Common plugin files
    ${CMAKE_SOURCE_DIR}/Server/common/plugin_manager.cpp
    ${CMAKE_SOURCE_DIR}/Server/common/plugin_registry.cpp
    ${CMAKE_SOURCE_DIR}/Server/common/plugin_packet_utils.cpp
    main.cpp
)

# Create the executable
add_executable(game ${GAME_C_SOURCES} ${GAME_CPP_SOURCES})

# Set target properties
set_target_properties(game PROPERTIES
    CXX_STANDARD 17
    C_STANDARD 11
    FOLDER "Server/Run"
    OUTPUT_NAME "game"
    DEBUG_POSTFIX "_d"
)

# Apply ProjectZ compiler settings for Windows builds
if(WIN32)
    apply_projectz_compiler_settings(game)
endif()

# Find required packages
find_package(cryptopp CONFIG REQUIRED)

# Include directories
target_include_directories(game PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/Server/common
    ${CMAKE_SOURCE_DIR}/Server/common/interfaces
    ${CMAKE_SOURCE_DIR}/Server/libthecore/include
    ${CMAKE_SOURCE_DIR}/Server/libgame/include
    ${CMAKE_SOURCE_DIR}/Server/libsql
    ${CMAKE_SOURCE_DIR}/Server/libpoly
    ${CMAKE_SOURCE_DIR}/Server/liblua/include
    ${CMAKE_SOURCE_DIR}/Server/libserverkey
    ${CMAKE_SOURCE_DIR}/External/include
    ${CMAKE_SOURCE_DIR}/External/include/devil
    ${MYSQL_INCLUDE_DIRS}
)

# Link with libraries
target_link_libraries(game PRIVATE
    Server::libthecore
    Server::libpoly
    Server::libsql
    Server::libgame
    Server::liblua
    Server::libserverkey
    ${MYSQL_LIBRARIES}
    OpenSSL::SSL
    OpenSSL::Crypto
    Threads::Threads
)

# Add library directories for MySQL
if(MYSQL_LIBRARY_DIRS)
    target_link_directories(game PRIVATE ${MYSQL_LIBRARY_DIRS})
endif()

# Platform-specific libraries
if(NOT WIN32)
    target_link_libraries(game PRIVATE
        dl m md z zstd
        IL png tiff mng lcms jpeg  # DevIL and image libraries
    )

    # Add external library directory for Unix
    target_link_directories(game PRIVATE ${CMAKE_SOURCE_DIR}/External/library)
else()
    target_link_libraries(game PRIVATE
        ws2_32 kernel32 user32 gdi32 winspool shell32 ole32 oleaut32 uuid comdlg32 advapi32
        # Add debug/release specific libraries
        $<$<CONFIG:Debug>:cryptlibd>
        $<$<NOT:$<CONFIG:Debug>>:cryptlib>
        $<$<CONFIG:Debug>:lzo2MTd>
        $<$<NOT:$<CONFIG:Debug>>:lzo2MT>
        DevIL ILU ILUT
        # Add vcpkg libraries
        cryptopp::cryptopp
    )

    # Add external library directories for Windows
    target_link_directories(game PRIVATE
        ${CMAKE_SOURCE_DIR}/External/library
        ${CMAKE_SOURCE_DIR}/External/library/win32
    )
endif()

# Game-specific compiler definitions
target_compile_definitions(game PRIVATE
    _THREAD_SAFE
)

if(NOT WIN32)
    target_compile_definitions(game PRIVATE
        __UNIX__
        _GNU_SOURCE
    )

    # Additional compiler flags for Unix (matching original Makefile)
    target_compile_options(game PRIVATE
        -fexceptions
        -Wno-unused-private-field
        -Wno-unknown-pragmas
        -Wno-format-security
    )
else()
    # Windows-specific definitions (basic ones handled by apply_projectz_compiler_settings)
    target_compile_definitions(game PRIVATE
        _CONSOLE
        _USE_32BIT_TIME_T
        _WINSOCK_DEPRECATED_NO_WARNINGS
    )
endif()

# Set output directory
set_target_properties(game PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/..
)
