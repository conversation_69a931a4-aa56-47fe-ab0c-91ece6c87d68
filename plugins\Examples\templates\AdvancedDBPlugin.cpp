/**
 * @file AdvancedDBPlugin.cpp
 * @brief Advanced Database Plugin Example
 * 
 * This example demonstrates all the advanced features of the database plugin system:
 * - Custom QID registration and handling
 * - Packet communication with game servers
 * - Boot-time initialization and custom table creation
 * - Safe database operations with compiled queries
 * - Player data, item data, and guild data operations
 * - Statistics tracking and logging
 */

#include "AdvancedDBPlugin.h"
#include "../../Server/db/src/plugin_qid_manager.h"
#include "../../Server/db/src/plugin_packet_manager.h"
#include <sstream>
#include <cstring>

// ============================================================================
// Compiled Query Strings (Security: Not exposed in source)
// ============================================================================

namespace {
    // Custom table creation queries
    const char* CREATE_PLAYER_STATS_TABLE = 
        "CREATE TABLE IF NOT EXISTS `player_stats` ("
        "`player_id` int(11) NOT NULL,"
        "`kills` int(11) DEFAULT 0,"
        "`deaths` int(11) DEFAULT 0,"
        "`playtime` int(11) DEFAULT 0,"
        "`last_login` int(11) DEFAULT 0,"
        "PRIMARY KEY (`player_id`)"
        ") ENGINE=MyISAM DEFAULT CHARSET=utf8";
    
    const char* CREATE_ITEM_USAGE_TABLE = 
        "CREATE TABLE IF NOT EXISTS `item_usage_log` ("
        "`id` int(11) NOT NULL AUTO_INCREMENT,"
        "`player_id` int(11) NOT NULL,"
        "`item_vnum` int(11) NOT NULL,"
        "`usage_count` int(11) DEFAULT 1,"
        "`last_used` timestamp DEFAULT CURRENT_TIMESTAMP,"
        "PRIMARY KEY (`id`),"
        "UNIQUE KEY `player_item` (`player_id`, `item_vnum`)"
        ") ENGINE=MyISAM DEFAULT CHARSET=utf8";
    
    const char* CREATE_GUILD_RANKING_TABLE = 
        "CREATE TABLE IF NOT EXISTS `guild_ranking` ("
        "`guild_id` int(11) NOT NULL,"
        "`guild_name` varchar(32) NOT NULL,"
        "`total_exp` bigint(20) DEFAULT 0,"
        "`member_count` int(11) DEFAULT 0,"
        "`last_updated` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,"
        "PRIMARY KEY (`guild_id`)"
        ") ENGINE=MyISAM DEFAULT CHARSET=utf8";
    
    // Player statistics queries
    const char* QUERY_LOAD_PLAYER_STATS = 
        "SELECT kills, deaths, playtime, last_login FROM player_stats WHERE player_id = %u";
    
    const char* QUERY_SAVE_PLAYER_STATS = 
        "INSERT INTO player_stats (player_id, kills, deaths, playtime, last_login) "
        "VALUES (%u, %d, %d, %d, %u) ON DUPLICATE KEY UPDATE "
        "kills = VALUES(kills), deaths = VALUES(deaths), playtime = VALUES(playtime), last_login = VALUES(last_login)";
    
    // Guild ranking queries
    const char* QUERY_UPDATE_GUILD_RANKING = 
        "INSERT INTO guild_ranking (guild_id, guild_name, total_exp, member_count) "
        "VALUES (%u, '%s', %lld, %d) ON DUPLICATE KEY UPDATE "
        "total_exp = VALUES(total_exp), member_count = VALUES(member_count)";
    
    const char* QUERY_GET_GUILD_RANKING = 
        "SELECT guild_id, guild_name, total_exp, member_count FROM guild_ranking ORDER BY total_exp DESC LIMIT 10";
}

// ============================================================================
// Constructor & Initialization
// ============================================================================

AdvancedDBPlugin::AdvancedDBPlugin()
    : m_state(PluginState::UNLOADED), m_packetInterface(nullptr), m_dbInterface(nullptr)
{
    // Initialize plugin information
    m_info.name = "AdvancedDBPlugin";
    m_info.description = "Advanced database plugin demonstrating all features";
    m_info.author = "Plugin Developer";
    m_info.version = PluginVersion(1, 0, 0);
    m_info.requiredApiVersion = PluginVersion(1, 0, 0);
    
    // Initialize statistics
    m_stats.startTime = time(nullptr);
    
    LogEvent(0, "Advanced DB plugin created");
}

bool AdvancedDBPlugin::Initialize()
{
    if (m_state != PluginState::UNLOADED)
        return false;
    
    LogEvent(0, "Initializing advanced DB plugin...");
    
    // Load configuration
    LoadDefaultConfig();
    if (!ValidateConfig())
    {
        LogEvent(2, "Configuration validation failed");
        m_state = PluginState::ERROR;
        return false;
    }
    
    // Initialize database interface
    m_dbInterface = GetPluginDBInterface();
    if (!m_dbInterface)
    {
        LogEvent(2, "Failed to get database interface");
        m_state = PluginState::ERROR;
        return false;
    }
    
    // Initialize packet communication
    if (!InitializePacketCommunication())
    {
        LogEvent(2, "Failed to initialize packet communication");
        m_state = PluginState::ERROR;
        return false;
    }
    
    // Register custom QIDs
    if (!RegisterCustomQIDs())
    {
        LogEvent(2, "Failed to register custom QIDs");
        m_state = PluginState::ERROR;
        return false;
    }
    
    m_state = PluginState::INITIALIZED;
    LogEvent(0, "Advanced DB plugin initialized successfully");
    return true;
}

// ============================================================================
// Boot-Time Initialization
// ============================================================================

void AdvancedDBPlugin::OnBootStart()
{
    LogEvent(0, "Boot initialization starting");
    
    // Prepare for custom table creation
    m_stats.eventsProcessed++;
}

bool AdvancedDBPlugin::ProcessBootInitialization()
{
    LogEvent(0, "Processing boot initialization");
    
    // Create custom tables
    if (!CreateCustomTables())
    {
        LogEvent(2, "Failed to create custom tables");
        return false;
    }
    
    // Initialize custom data
    if (!InitializeCustomData())
    {
        LogEvent(2, "Failed to initialize custom data");
        return false;
    }
    
    // Load initial rankings
    LoadInitialRankings();
    
    LogEvent(0, "Boot initialization completed successfully");
    return true;
}

void AdvancedDBPlugin::OnBootComplete()
{
    LogEvent(0, "Boot initialization completed");
    
    // Start background tasks
    StartPeriodicTasks();
}

// ============================================================================
// Custom QID Registration and Handling
// ============================================================================

bool AdvancedDBPlugin::RegisterCustomQIDs()
{
    // Register player statistics QID
    if (!PluginQIDManager::instance().RegisterQueryHandler(m_info.name, QID_PLAYER_STATS,
        [this](CPeer* peer, CQueryInfo* qi, SQLMsg* result) {
            HandlePlayerStatsQuery(peer, qi, result);
        }))
    {
        LogEvent(2, "Failed to register QID_PLAYER_STATS");
        return false;
    }
    
    // Register guild ranking QID
    if (!PluginQIDManager::instance().RegisterQueryHandler(m_info.name, QID_GUILD_RANKING,
        [this](CPeer* peer, CQueryInfo* qi, SQLMsg* result) {
            HandleGuildRankingQuery(peer, qi, result);
        }))
    {
        LogEvent(2, "Failed to register QID_GUILD_RANKING");
        return false;
    }
    
    // Register item usage tracking QID
    if (!PluginQIDManager::instance().RegisterQueryHandler(m_info.name, QID_ITEM_USAGE,
        [this](CPeer* peer, CQueryInfo* qi, SQLMsg* result) {
            HandleItemUsageQuery(peer, qi, result);
        }))
    {
        LogEvent(2, "Failed to register QID_ITEM_USAGE");
        return false;
    }
    
    m_stats.customQIDsRegistered = 3;
    LogEvent(0, "Custom QIDs registered successfully");
    return true;
}

void AdvancedDBPlugin::HandlePlayerStatsQuery(CPeer* peer, CQueryInfo* qi, SQLMsg* result)
{
    m_stats.eventsProcessed++;
    m_stats.queriesExecuted++;
    
    if (!result || !peer)
    {
        LogEvent(2, "Invalid player stats query result");
        return;
    }
    
    // Extract query data
    const auto* queryData = static_cast<const PlayerStatsQuery*>(qi->pvData);
    if (!queryData)
    {
        LogEvent(2, "Invalid player stats query data");
        return;
    }
    
    // Process query result
    if (result->Get()->uiNumRows > 0)
    {
        MYSQL_ROW row = mysql_fetch_row(result->Get()->pSQLResult);
        if (row)
        {
            // Parse player statistics
            PlayerStatsResult statsResult;
            statsResult.type = PACKET_TYPE_PLAYER_STATS_RESULT;
            statsResult.playerID = queryData->playerID;
            statsResult.kills = atoi(row[0]);
            statsResult.deaths = atoi(row[1]);
            statsResult.playtime = atoi(row[2]);
            statsResult.lastLogin = static_cast<uint32_t>(atoi(row[3]));
            
            // Send result back to game server
            m_packetInterface->SendToGame(peer, &statsResult, sizeof(statsResult), "AdvancedGamePlugin");
            m_stats.packetsSent++;
            
            LogEvent(0, "Player stats loaded for player " + std::to_string(queryData->playerID));
        }
    }
    else
    {
        // No statistics found, send default values
        PlayerStatsResult statsResult = {};
        statsResult.type = PACKET_TYPE_PLAYER_STATS_RESULT;
        statsResult.playerID = queryData->playerID;
        
        m_packetInterface->SendToGame(peer, &statsResult, sizeof(statsResult), "AdvancedGamePlugin");
        m_stats.packetsSent++;
        
        LogEvent(0, "Default stats sent for new player " + std::to_string(queryData->playerID));
    }
}

void AdvancedDBPlugin::HandleGuildRankingQuery(CPeer* peer, CQueryInfo* qi, SQLMsg* result)
{
    m_stats.eventsProcessed++;
    m_stats.queriesExecuted++;
    
    if (!result || !peer)
    {
        LogEvent(2, "Invalid guild ranking query result");
        return;
    }
    
    // Process guild ranking results
    GuildRankingResult rankingResult;
    rankingResult.type = PACKET_TYPE_GUILD_RANKING_RESULT;
    rankingResult.count = 0;
    
    if (result->Get()->uiNumRows > 0)
    {
        MYSQL_ROW row;
        while ((row = mysql_fetch_row(result->Get()->pSQLResult)) && rankingResult.count < MAX_GUILD_RANKING)
        {
            auto& entry = rankingResult.guilds[rankingResult.count];
            entry.guildID = static_cast<uint32_t>(atoi(row[0]));
            strncpy(entry.guildName, row[1], sizeof(entry.guildName) - 1);
            entry.guildName[sizeof(entry.guildName) - 1] = '\0';
            entry.totalExp = static_cast<uint64_t>(atoll(row[2]));
            entry.memberCount = static_cast<uint32_t>(atoi(row[3]));
            
            rankingResult.count++;
        }
    }
    
    // Send ranking result back to game server
    m_packetInterface->SendToGame(peer, &rankingResult, sizeof(rankingResult), "AdvancedGamePlugin");
    m_stats.packetsSent++;
    
    LogEvent(0, "Guild ranking sent with " + std::to_string(rankingResult.count) + " entries");
}

// ============================================================================
// Database Operations
// ============================================================================

bool AdvancedDBPlugin::CreateCustomTables()
{
    LogEvent(0, "Creating custom tables...");
    
    // Create player stats table
    SQLMsg* result = m_dbInterface->DirectQuery(CREATE_PLAYER_STATS_TABLE);
    if (!result)
    {
        LogEvent(2, "Failed to create player_stats table");
        return false;
    }
    delete result;
    
    // Create item usage table
    result = m_dbInterface->DirectQuery(CREATE_ITEM_USAGE_TABLE);
    if (!result)
    {
        LogEvent(2, "Failed to create item_usage_log table");
        return false;
    }
    delete result;
    
    // Create guild ranking table
    result = m_dbInterface->DirectQuery(CREATE_GUILD_RANKING_TABLE);
    if (!result)
    {
        LogEvent(2, "Failed to create guild_ranking table");
        return false;
    }
    delete result;
    
    LogEvent(0, "Custom tables created successfully");
    return true;
}

bool AdvancedDBPlugin::InitializeCustomData()
{
    LogEvent(0, "Initializing custom data...");
    
    // Initialize any default data here
    // For example, create default guild rankings
    
    LogEvent(0, "Custom data initialized successfully");
    return true;
}

// ============================================================================
// Event Handlers
// ============================================================================

void AdvancedDBPlugin::OnPlayerLoad(DWORD playerID, TPlayerTable* playerTable)
{
    if (!playerTable) return;
    
    m_stats.eventsProcessed++;
    m_stats.playersLoaded++;
    
    if (m_config.logPlayerOperations)
    {
        LogEvent(0, "Player loaded: " + std::string(playerTable->name) + " (ID: " + std::to_string(playerID) + ")");
    }
    
    // Load additional player data
    LoadPlayerCustomData(playerID);
}

void AdvancedDBPlugin::OnPlayerSave(DWORD playerID, TPlayerTable* playerTable)
{
    if (!playerTable) return;
    
    m_stats.eventsProcessed++;
    m_stats.playersSaved++;
    
    if (m_config.logPlayerOperations)
    {
        LogEvent(0, "Player saved: " + std::string(playerTable->name) + " (ID: " + std::to_string(playerID) + ")");
    }
    
    // Save additional player data
    SavePlayerCustomData(playerID, playerTable);
}

void AdvancedDBPlugin::OnGuildCreate(DWORD guildID, const char* guildName, DWORD masterPID)
{
    m_stats.eventsProcessed++;
    
    if (m_config.logGuildOperations)
    {
        LogEvent(0, "Guild created: " + std::string(guildName) + " (ID: " + std::to_string(guildID) + ")");
    }
    
    // Initialize guild ranking entry
    InitializeGuildRanking(guildID, guildName);
}

// ============================================================================
// Packet Communication
// ============================================================================

bool AdvancedDBPlugin::InitializePacketCommunication()
{
    m_packetInterface = GetDBPluginPacketInterface();
    if (!m_packetInterface)
    {
        LogEvent(2, "Failed to get packet interface");
        return false;
    }
    
    // Register packet handler
    m_packetInterface->RegisterPacketHandler([this](CPeer* peer, const void* data, uint32_t size, const std::string& source) {
        HandleGamePacket(peer, data, size, source);
    });
    
    LogEvent(0, "Packet communication initialized");
    return true;
}

void AdvancedDBPlugin::HandleGamePacket(CPeer* peer, const void* data, uint32_t size, const std::string& sourcePlugin)
{
    m_stats.packetsReceived++;
    
    if (size < sizeof(uint32_t)) return;
    
    uint32_t packetType = *static_cast<const uint32_t*>(data);
    
    switch (packetType)
    {
        case PACKET_TYPE_PLAYER_STATS_SAVE:
            HandlePlayerStatsSave(peer, data, size);
            break;
            
        case PACKET_TYPE_ITEM_USAGE:
            HandleItemUsageLog(peer, data, size);
            break;
            
        case PACKET_TYPE_GUILD_RANKING_UPDATE:
            HandleGuildRankingUpdate(peer, data, size);
            break;
            
        default:
            LogEvent(1, "Unknown packet type from " + sourcePlugin + ": " + std::to_string(packetType));
            break;
    }
}

// ============================================================================
// Plugin Registration
// ============================================================================

extern "C" {
    IPlugin* CreatePlugin() {
        return new AdvancedDBPlugin();
    }
    
    void DestroyPlugin(IPlugin* plugin) {
        delete plugin;
    }
}

REGISTER_DB_PLUGIN("AdvancedDBPlugin", AdvancedDBPlugin,
    CAPABILITY_PLAYER_DATA | CAPABILITY_ITEM_DATA | CAPABILITY_GUILD_DATA |
    CAPABILITY_QUERY_EVENTS | CAPABILITY_SYSTEM_EVENTS | CAPABILITY_BACKUP_EVENTS)
