# External C API for Character Management

This directory contains a cross-compiler compatible C API for accessing character and character manager functionality from plugins. The API is designed to work across different compilers (GCC, Clang, Visual Studio) by using pure C interfaces with `extern "C"` linkage.

## Overview

The external API provides C wrappers around the existing C++ CHARACTER and CHARACTER_MANAGER classes. This ensures ABI stability and prevents issues with C++ name mangling, vtable layouts, and STL container compatibility across different compilers.

## Files

### Header Files
- `external_api.h` - Main include file that includes all APIs
- `char_api.h` - C API for CHARACTER class functionality
- `char_manager_api.h` - C API for CHARACTER_MANAGER singleton functionality

### Implementation Files
- `external_api.cpp` - Utility functions and API management
- `char_api.cpp` - Implementation of CHARACTER C API
- `char_manager_api.cpp` - Implementation of CHARACTER_MANAGER C API

### Documentation
- `README.md` - This file

## Design Principles

### 1. Cross-Compiler Compatibility
- Pure C interface with `extern "C"` linkage
- No C++ features exposed in the interface
- Simple data types (int, char*, etc.) for parameters
- Opaque handles to hide C++ implementation details

### 2. Error Handling
- Error codes instead of exceptions
- Consistent error reporting through `CharApiResult` enum
- Last error message tracking for debugging

### 3. Memory Safety
- No direct pointer exposure
- Opaque handles for all C++ objects
- Validation of all input parameters

### 4. ABI Stability
- No STL containers in the interface
- Fixed-size structures for data exchange
- Stable function signatures

## Usage Example

```c
#include "external_api.h"

void plugin_example() {
    // Initialize the API
    if (char_api_initialize() != CHAR_API_SUCCESS) {
        printf("Failed to initialize API\n");
        return;
    }
    
    // Get character manager instance
    CharacterManagerHandle_t mgr = char_manager_api_get_instance();
    if (!mgr) {
        printf("Failed to get character manager\n");
        return;
    }
    
    // Find a character by name
    CharacterHandle_t character = NULL;
    CharApiResult result = char_manager_api_find_pc_by_name(mgr, "TestPlayer", &character);
    
    if (result == CHAR_API_SUCCESS && character) {
        // Get character information
        BYTE level;
        int gold;
        char name[64];
        
        char_api_get_level(character, &level);
        char_api_get_gold(character, &gold);
        char_api_get_name(character, name, sizeof(name));
        
        printf("Found character: %s (Level %d, Gold: %d)\n", name, level, gold);
        
        // Send a message to the character
        char_api_chat_packet(character, "Hello from plugin!");
        
        // Modify character stats
        char_api_set_gold(character, gold + 1000);
    }
    
    // Cleanup
    char_api_cleanup();
}
```

## API Reference

### Character API (char_api.h)

#### Basic Information
- `char_api_get_name()` - Get character name
- `char_api_get_vid()` - Get virtual ID
- `char_api_get_player_id()` - Get player ID
- `char_api_get_race_num()` - Get race number
- `char_api_get_job()` - Get job/class
- `char_api_is_pc()` - Check if player character
- `char_api_is_npc()` - Check if NPC
- `char_api_is_monster()` - Check if monster

#### Position and Movement
- `char_api_get_position()` - Get current position
- `char_api_get_map_index()` - Get map index
- `char_api_warp()` - Warp character to location

#### Stats and Points
- `char_api_get_stats()` - Get basic stats structure
- `char_api_get_point()` - Get specific point value
- `char_api_set_point()` - Set specific point value
- `char_api_get_level()` - Get character level
- `char_api_get_exp()` - Get experience points
- `char_api_get_gold()` - Get gold amount
- `char_api_set_gold()` - Set gold amount

#### Health and Status
- `char_api_get_hp()` - Get current HP
- `char_api_get_max_hp()` - Get maximum HP
- `char_api_set_hp()` - Set current HP
- `char_api_get_sp()` - Get current SP
- `char_api_set_sp()` - Set current SP
- `char_api_is_dead()` - Check if character is dead

#### Communication
- `char_api_chat_packet()` - Send chat message
- `char_api_notice()` - Send notice message

### Character Manager API (char_manager_api.h)

#### Manager Access
- `char_manager_api_get_instance()` - Get singleton instance

#### Character Creation/Destruction
- `char_manager_api_create_character()` - Create new character
- `char_manager_api_destroy_character()` - Destroy character

#### Character Lookup
- `char_manager_api_find_by_vid()` - Find by virtual ID
- `char_manager_api_find_pc_by_name()` - Find PC by name
- `char_manager_api_find_by_pid()` - Find by player ID

#### Mob Spawning
- `char_manager_api_spawn_mob()` - Spawn mob at coordinates
- `char_manager_api_spawn_mob_range()` - Spawn mob in range
- `char_manager_api_spawn_mob_random_position()` - Spawn at random position

#### Character Iteration
- `char_manager_api_for_each_pc()` - Iterate over all PCs
- `char_manager_api_get_characters_by_race()` - Get characters by race

#### State Management
- `char_manager_api_add_to_state_list()` - Add to state list
- `char_manager_api_remove_from_state_list()` - Remove from state list
- `char_manager_api_delayed_save()` - Add to delayed save
- `char_manager_api_process_delayed_save()` - Process delayed saves

#### Rate Management
- `char_manager_api_get_mob_item_rate()` - Get mob item drop rate
- `char_manager_api_get_mob_damage_rate()` - Get mob damage rate
- `char_manager_api_get_mob_exp_rate()` - Get mob experience rate

## Error Handling

All API functions return a `CharApiResult` enum value:

```c
typedef enum {
    CHAR_API_SUCCESS = 0,
    CHAR_API_ERROR_NULL_HANDLE = -1,
    CHAR_API_ERROR_INVALID_PARAM = -2,
    CHAR_API_ERROR_BUFFER_TOO_SMALL = -3,
    CHAR_API_ERROR_NOT_FOUND = -4,
    CHAR_API_ERROR_PERMISSION_DENIED = -5,
    CHAR_API_ERROR_INTERNAL = -6
} CharApiResult;
```

Use the convenience macros for error checking:
- `CHAR_API_SUCCEEDED(result)` - Check for success
- `CHAR_API_FAILED(result)` - Check for failure

## Debugging

Enable debug mode for detailed logging:

```c
char_api_set_debug_mode(1);  // Enable debugging
char_api_debug_log("Custom debug message");
```

Or set the environment variable `CHAR_API_DEBUG=1`.

## Thread Safety

The API inherits the thread safety characteristics of the underlying C++ classes. Most operations are not thread-safe and should be called from the main game thread only.

## Compilation

Include the API files in your plugin's build system:

```cmake
# In your plugin's CMakeLists.txt
target_sources(your_plugin PRIVATE
    ${CMAKE_SOURCE_DIR}/Server/common/ExternalApi/char_api.cpp
    ${CMAKE_SOURCE_DIR}/Server/common/ExternalApi/char_manager_api.cpp
    ${CMAKE_SOURCE_DIR}/Server/common/ExternalApi/external_api.cpp
)

target_include_directories(your_plugin PRIVATE
    ${CMAKE_SOURCE_DIR}/Server/common/ExternalApi
)
```

## Best Practices

1. Always check return values for errors
2. Initialize the API before use with `char_api_initialize()`
3. Clean up with `char_api_cleanup()` when done
4. Validate handles before use with `CHAR_API_HANDLE_VALID()`
5. Use debug mode during development
6. Handle all possible error conditions
7. Don't store handles across plugin reloads

## Limitations

1. No direct access to C++ STL containers
2. Limited to functionality exposed through the API
3. Some advanced C++ features are not accessible
4. Performance overhead due to C wrapper layer
5. Must be compiled with the same basic ABI (calling convention, etc.)

## Future Extensions

The API can be extended to include:
- Item management functions
- Guild management functions
- Party management functions
- Quest system integration
- Combat system integration
- Database access functions
