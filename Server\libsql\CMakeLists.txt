# libsql Library
cmake_minimum_required(VERSION 3.16)

file(GLOB_RECURSE SOURCES "*.cpp" "*.c")
file(GLOB_RECURSE HEADERS "*.h")

create_server_library(libsql
    SOURCES ${SOURCES}
    HEADERS ${HEADERS}
    DEPENDENCIES
        Server::libthecore
        OpenSSL::SSL
        OpenSSL::Crypto
        unofficial::libmysql::libmysql
)

# Set output directory to match original structure
set_target_properties(libsql PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    ARCHIVE_OUTPUT_NAME sql
)
