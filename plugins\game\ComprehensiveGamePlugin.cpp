#include "ComprehensiveGamePlugin.h"
#include <string>
#include <sstream>

// Updated to use pure virtual interfaces for cross-compiler compatibility
// This plugin demonstrates access to game objects through ABI-stable interfaces

ComprehensiveGamePlugin::ComprehensiveGamePlugin()
    : m_initialized(false)
    , m_running(false)
    , m_debugMode(true)
    , m_enableCharacterTracking(true)
    , m_enableItemTracking(true)
    , m_enableCombatTracking(true)
    , m_enableGuildTracking(true)
    , m_enableShopTracking(true)
    , m_enableQuestTracking(true)
    , m_enableChatTracking(true)
    , m_enableCommandTracking(true)
    , m_enableDetailedLogging(true)
{
    // Initialize plugin info
    m_info.name = "ComprehensiveGamePlugin";
    m_info.version = PluginVersion(1, 0, 0);
    m_info.description = "Comprehensive game plugin demonstrating full access to game objects and systems using ABI-stable interfaces";
    m_info.author = "Plugin System Developer";
    
    m_state = PluginState::PLUGIN_UNLOADED;
    
    sys_log(0, "[ComprehensiveGamePlugin] Constructor called");
}

ComprehensiveGamePlugin::~ComprehensiveGamePlugin()
{
    if (m_running)
        Stop();
    if (m_initialized)
        Shutdown();
    
    sys_log(0, "[ComprehensiveGamePlugin] Destructor called");
}

bool ComprehensiveGamePlugin::Initialize()
{
    if (m_initialized)
        return true;
    
    sys_log(0, "[ComprehensiveGamePlugin] Initialize() called");
    
    try
    {
        // Load configuration from JSON file
        LoadConfiguration();
        
        // Initialize statistics
        m_stats.charactersCreated = 0;
        m_stats.itemsCreated = 0;
        m_stats.combatEvents = 0;
        m_stats.questsCompleted = 0;
        m_stats.commandsProcessed = 0;
        m_stats.packetsProcessed = 0;
        
        m_state = PluginState::PLUGIN_INITIALIZED;
        m_initialized = true;
        
        sys_log(0, "[ComprehensiveGamePlugin] Plugin initialized successfully");
        return true;
    }
    catch (const std::exception& e)
    {
        sys_err("[ComprehensiveGamePlugin] Initialize failed: %s", e.what());
        m_state = PluginState::PLUGIN_ERROR;
        return false;
    }
}

bool ComprehensiveGamePlugin::Start()
{
    if (!m_initialized)
    {
        sys_err("[ComprehensiveGamePlugin] Cannot start - plugin not initialized");
        return false;
    }
    
    if (m_running)
        return true;
    
    sys_log(0, "[ComprehensiveGamePlugin] Start() called");
    
    try
    {
        // Demonstrate access to singleton managers
        DemonstrateManagerAccess();
        
        m_state = PluginState::PLUGIN_RUNNING;
        m_running = true;
        
        sys_log(0, "[ComprehensiveGamePlugin] Plugin started successfully");
        return true;
    }
    catch (const std::exception& e)
    {
        sys_err("[ComprehensiveGamePlugin] Start failed: %s", e.what());
        m_state = PluginState::PLUGIN_ERROR;
        return false;
    }
}

void ComprehensiveGamePlugin::Stop()
{
    if (!m_running)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Stop() called");
    
    m_running = false;
    m_state = PluginState::PLUGIN_STOPPED;
    
    sys_log(0, "[ComprehensiveGamePlugin] Plugin stopped");
}

void ComprehensiveGamePlugin::Shutdown()
{
    if (!m_initialized)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Shutdown() called");
    
    if (m_running)
        Stop();
    
    // Save final statistics
    SaveStatistics();
    
    m_initialized = false;
    m_state = PluginState::PLUGIN_UNLOADED;
    
    sys_log(0, "[ComprehensiveGamePlugin] Plugin shutdown complete");
}

const PluginInfo& ComprehensiveGamePlugin::GetInfo() const
{
    return m_info;
}

PluginState ComprehensiveGamePlugin::GetState() const
{
    return m_state;
}

// Removed GetName, GetVersion, GetDescription, and GetCapabilities methods
// as they are not part of the IPlugin interface

void ComprehensiveGamePlugin::LoadConfiguration()
{
    // Load settings from JSON configuration file
    // This would typically read from plugins/config/ComprehensiveGamePlugin.json
    // For now, use default values
    
    m_debugMode = true;
    m_enableCharacterTracking = true;
    m_enableItemTracking = true;
    m_enableCombatTracking = true;
    m_enableGuildTracking = true;
    m_enableShopTracking = true;
    m_enableQuestTracking = true;
    m_enableChatTracking = true;
    m_enableCommandTracking = true;
    m_enableDetailedLogging = true;
    
    sys_log(0, "[ComprehensiveGamePlugin] Configuration loaded");
}

void ComprehensiveGamePlugin::SaveStatistics()
{
    sys_log(0, "[ComprehensiveGamePlugin] Final Statistics:");
    sys_log(0, "  Characters Created: %d", m_stats.charactersCreated);
    sys_log(0, "  Items Created: %d", m_stats.itemsCreated);
    sys_log(0, "  Combat Events: %d", m_stats.combatEvents);
    sys_log(0, "  Quests Completed: %d", m_stats.questsCompleted);
    sys_log(0, "  Commands Processed: %d", m_stats.commandsProcessed);
    sys_log(0, "  Packets Processed: %d", m_stats.packetsProcessed);
}

void ComprehensiveGamePlugin::DemonstrateManagerAccess()
{
    sys_log(0, "[ComprehensiveGamePlugin] Plugin has access to game managers through the interface");
    sys_log(0, "  Game objects will be accessed through event parameters");
    sys_log(0, "  This demonstrates that the plugin system is working correctly");
}

// ============================================================================
// CHARACTER EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnCharacterCreate(ICHARACTER* ch)
{
    if (!m_running || !m_enableCharacterTracking || !ch)
        return;

    m_stats.charactersCreated++;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Character created - plugin working!");
    }

    // Basic demonstration - the CHARACTER object is accessible here
    // In a real implementation, you would access ch->GetName(), ch->GetLevel(), etc.
    sys_log(0, "[ComprehensiveGamePlugin] CHARACTER object received in OnCharacterCreate");

    LogEvent("CHARACTER_CREATE", "Character created successfully");
}

void ComprehensiveGamePlugin::OnCharacterDestroy(ICHARACTER* ch)
{
    if (!m_running || !m_enableCharacterTracking || !ch)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Character destroyed: %s", GetCharacterInfo(ch).c_str());
    }

    LogEvent("CHARACTER_DESTROY", GetCharacterInfo(ch));
}

void ComprehensiveGamePlugin::OnCharacterLogin(ICHARACTER* ch)
{
    if (!m_running || !m_enableCharacterTracking || !ch)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Character login: %s", GetCharacterInfo(ch).c_str());
    }

    // Demonstrate full CHARACTER access
    DemonstrateCharacterAccess(ch);

    // Show player information
    SendMessage(ch, "Login detected! Your info:");
    SendMessage(ch, GetCharacterInfo(ch));

    LogEvent("CHARACTER_LOGIN", GetCharacterInfo(ch));
}

void ComprehensiveGamePlugin::OnCharacterLogout(ICHARACTER* ch)
{
    if (!m_running || !m_enableCharacterTracking || !ch)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Character logout: %s", GetCharacterInfo(ch).c_str());
    }

    LogEvent("CHARACTER_LOGOUT", GetCharacterInfo(ch));
}

void ComprehensiveGamePlugin::OnCharacterLevelUp(ICHARACTER* ch, PLUGIN_BYTE newLevel)
{
    if (!m_running || !m_enableCharacterTracking || !ch)
        return;

    sys_log(0, "[ComprehensiveGamePlugin] Character %s leveled up to %d", ch->GetName(), newLevel);

    // Congratulate on milestone levels
    if (newLevel % 10 == 0)
    {
        SendMessage(ch, "Congratulations on reaching level " + std::to_string(newLevel) + "!");

        // Note: Item creation would be done through IITEM_MANAGER interface
        // when the interface factory is fully integrated
        sys_log(0, "[ComprehensiveGamePlugin] Milestone level reached: %d", newLevel);
    }

    LogEvent("CHARACTER_LEVELUP", GetCharacterInfo(ch) + " -> Level " + std::to_string(newLevel));
}

void ComprehensiveGamePlugin::OnCharacterDead(ICHARACTER* ch, ICHARACTER* killer)
{
    if (!m_running || !m_enableCombatTracking || !ch)
        return;

    std::string killerInfo = killer ? GetCharacterInfo(killer) : "Unknown";

    if (killer)
    {
        sys_log(0, "[ComprehensiveGamePlugin] %s was killed by %s", ch->GetName(), killer->GetName());
    }
    else
    {
        sys_log(0, "[ComprehensiveGamePlugin] %s died", ch->GetName());
    }

    LogEvent("CHARACTER_DEATH", GetCharacterInfo(ch) + " killed by " + killerInfo);
}

void ComprehensiveGamePlugin::OnCharacterRevive(ICHARACTER* ch)
{
    if (!m_running || !m_enableCharacterTracking || !ch)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Character revived: %s", ch->GetName());
    }

    SendMessage(ch, "Welcome back to life!");

    LogEvent("CHARACTER_REVIVE", GetCharacterInfo(ch));
}

// ============================================================================
// ITEM EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnItemCreate(IITEM* item)
{
    if (!m_running || !m_enableItemTracking || !item)
        return;

    m_stats.itemsCreated++;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Item created: %s", GetItemInfo(item).c_str());
    }

    // Demonstrate CItem access
    DemonstrateItemAccess(item);

    LogEvent("ITEM_CREATE", GetItemInfo(item));
}

void ComprehensiveGamePlugin::OnItemDestroy(IITEM* item)
{
    if (!m_running || !m_enableItemTracking || !item)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Item destroyed: %s", GetItemInfo(item).c_str());
    }

    LogEvent("ITEM_DESTROY", GetItemInfo(item));
}

void ComprehensiveGamePlugin::OnItemEquip(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !m_enableItemTracking || !ch || !item)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] %s equipped %s", ch->GetName(), GetItemInfo(item).c_str());
    }

    SendMessage(ch, "Item equipped: " + GetItemInfo(item));

    LogEvent("ITEM_EQUIP", GetCharacterInfo(ch) + " equipped " + GetItemInfo(item));
}

void ComprehensiveGamePlugin::OnItemUnequip(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !m_enableItemTracking || !ch || !item)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] %s unequipped %s", ch->GetName(), GetItemInfo(item).c_str());
    }

    SendMessage(ch, "Item unequipped: " + GetItemInfo(item));

    LogEvent("ITEM_UNEQUIP", GetCharacterInfo(ch) + " unequipped " + GetItemInfo(item));
}

void ComprehensiveGamePlugin::OnItemUse(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !m_enableItemTracking || !ch || !item)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] %s used %s", ch->GetName(), GetItemInfo(item).c_str());
    }

    SendMessage(ch, "Item used: " + GetItemInfo(item));

    LogEvent("ITEM_USE", GetCharacterInfo(ch) + " used " + GetItemInfo(item));
}

void ComprehensiveGamePlugin::OnItemDrop(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !m_enableItemTracking || !ch || !item)
        return;

    LogEvent("ITEM_DROP", GetCharacterInfo(ch) + " dropped " + GetItemInfo(item));
}

void ComprehensiveGamePlugin::OnItemPickup(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !m_enableItemTracking || !ch || !item)
        return;

    LogEvent("ITEM_PICKUP", GetCharacterInfo(ch) + " picked up " + GetItemInfo(item));
}

// ============================================================================
// COMBAT EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnAttack(LPCHARACTER attacker, LPCHARACTER victim, int damage)
{
    if (!m_running || !m_enableCombatTracking || !attacker || !victim)
        return;

    m_stats.combatEvents++;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] %s attacked %s for %d damage",
                attacker->GetName(), victim->GetName(), damage);
    }

    LogEvent("ATTACK", GetCharacterInfo(attacker) + " attacked " + GetCharacterInfo(victim) + " (" + std::to_string(damage) + " damage)");
}

void ComprehensiveGamePlugin::OnKill(LPCHARACTER killer, LPCHARACTER victim)
{
    if (!m_running || !m_enableCombatTracking || !killer || !victim)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] %s killed %s",
                killer->GetName(), victim->GetName());
    }

    LogEvent("KILL", GetCharacterInfo(killer) + " killed " + GetCharacterInfo(victim));
}

void ComprehensiveGamePlugin::OnDamage(LPCHARACTER victim, LPCHARACTER attacker, int damage)
{
    if (!m_running || !m_enableCombatTracking || !victim || !attacker)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] %s took %d damage from %s",
                victim->GetName(), damage, attacker->GetName());
    }

    LogEvent("DAMAGE", GetCharacterInfo(victim) + " took " + std::to_string(damage) + " damage from " + GetCharacterInfo(attacker));
}

// ============================================================================
// GUILD EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnGuildCreate(LPGUILD guild)
{
    if (!m_running || !m_enableGuildTracking || !guild)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Guild created: %s", GetGuildInfo(guild).c_str());
    }

    // Demonstrate guild access
    DemonstrateGuildAccess(guild);

    LogEvent("GUILD_CREATE", GetGuildInfo(guild));
}

void ComprehensiveGamePlugin::OnGuildDestroy(LPGUILD guild)
{
    if (!m_running || !m_enableGuildTracking || !guild)
        return;

    LogEvent("GUILD_DESTROY", GetGuildInfo(guild));
}

void ComprehensiveGamePlugin::OnGuildJoin(LPCHARACTER ch, LPGUILD guild)
{
    if (!m_running || !m_enableGuildTracking || !ch || !guild)
        return;

    SendMessage(ch, "Welcome to guild: " + std::string(guild->GetName()));

    LogEvent("GUILD_JOIN", GetCharacterInfo(ch) + " joined " + GetGuildInfo(guild));
}

void ComprehensiveGamePlugin::OnGuildLeave(LPCHARACTER ch, LPGUILD guild)
{
    if (!m_running || !m_enableGuildTracking || !ch || !guild)
        return;

    LogEvent("GUILD_LEAVE", GetCharacterInfo(ch) + " left " + GetGuildInfo(guild));
}

void ComprehensiveGamePlugin::OnGuildWar(LPGUILD guild1, LPGUILD guild2)
{
    if (!m_running || !m_enableGuildTracking || !guild1 || !guild2)
        return;

    LogEvent("GUILD_WAR", GetGuildInfo(guild1) + " vs " + GetGuildInfo(guild2));
}

// ============================================================================
// SHOP EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnShopOpen(LPCHARACTER ch, LPSHOP shop)
{
    if (!m_running || !m_enableShopTracking || !ch || !shop)
        return;

    SendMessage(ch, "Shop opened: " + GetShopInfo(shop));

    LogEvent("SHOP_OPEN", GetCharacterInfo(ch) + " opened " + GetShopInfo(shop));
}

void ComprehensiveGamePlugin::OnShopClose(LPCHARACTER ch, LPSHOP shop)
{
    if (!m_running || !m_enableShopTracking || !ch || !shop)
        return;

    LogEvent("SHOP_CLOSE", GetCharacterInfo(ch) + " closed " + GetShopInfo(shop));
}

void ComprehensiveGamePlugin::OnShopBuy(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count)
{
    if (!m_running || !m_enableShopTracking || !ch || !shop || !item)
        return;

    SendMessage(ch, "Purchased: " + std::to_string(count) + "x " + GetItemInfo(item));

    LogEvent("SHOP_BUY", GetCharacterInfo(ch) + " bought " + std::to_string(count) + "x " + GetItemInfo(item));
}

void ComprehensiveGamePlugin::OnShopSell(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count)
{
    if (!m_running || !m_enableShopTracking || !ch || !shop || !item)
        return;

    LogEvent("SHOP_SELL", GetCharacterInfo(ch) + " sold " + std::to_string(count) + "x " + GetItemInfo(item));
}

// ============================================================================
// QUEST EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnQuestStart(LPCHARACTER ch, int questIndex)
{
    if (!m_running || !m_enableQuestTracking || !ch)
        return;

    m_stats.questsCompleted++;

    SendMessage(ch, "Quest started: " + std::to_string(questIndex));

    LogEvent("QUEST_START", GetCharacterInfo(ch) + " started quest " + std::to_string(questIndex));
}

void ComprehensiveGamePlugin::OnQuestComplete(LPCHARACTER ch, int questIndex)
{
    if (!m_running || !m_enableQuestTracking || !ch)
        return;

    SendMessage(ch, "Quest completed: " + std::to_string(questIndex));

    // Example: Bonus reward for completing certain quests
    if (questIndex == 1001) // Example special quest
    {
        SendMessage(ch, "Bonus reward for completing the special quest!");

        // Create bonus item
        LPITEM bonus = ITEM_MANAGER::instance().CreateItem(50300, 1);
        if (bonus)
        {
            ch->AutoGiveItem(bonus);
        }
    }

    LogEvent("QUEST_COMPLETE", GetCharacterInfo(ch) + " completed quest " + std::to_string(questIndex));
}

void ComprehensiveGamePlugin::OnQuestGiveUp(LPCHARACTER ch, int questIndex)
{
    if (!m_running || !m_enableQuestTracking || !ch)
        return;

    SendMessage(ch, "Quest given up: " + std::to_string(questIndex));

    LogEvent("QUEST_GIVEUP", GetCharacterInfo(ch) + " gave up quest " + std::to_string(questIndex));
}

// ============================================================================
// CHAT EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnChat(LPCHARACTER ch, const char* message, int type)
{
    if (!m_running || !m_enableChatTracking || !ch || !message)
        return;

    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Chat from %s (type %d): %s", ch->GetName(), type, message);
    }

    LogEvent("CHAT", GetCharacterInfo(ch) + " said: " + std::string(message));
}

void ComprehensiveGamePlugin::OnWhisper(LPCHARACTER from, LPCHARACTER to, const char* message)
{
    if (!m_running || !m_enableChatTracking || !from || !to || !message)
        return;

    LogEvent("WHISPER", GetCharacterInfo(from) + " -> " + GetCharacterInfo(to) + ": " + std::string(message));
}

void ComprehensiveGamePlugin::OnShout(LPCHARACTER ch, const char* message)
{
    if (!m_running || !m_enableChatTracking || !ch || !message)
        return;

    LogEvent("SHOUT", GetCharacterInfo(ch) + " shouted: " + std::string(message));
}

// ============================================================================
// SYSTEM EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnServerStart()
{
    if (!m_running)
        return;

    sys_log(0, "[ComprehensiveGamePlugin] Server started");
    LogEvent("SERVER_START", "Game server has started");
}

void ComprehensiveGamePlugin::OnServerShutdown()
{
    if (!m_running)
        return;

    sys_log(0, "[ComprehensiveGamePlugin] Server shutting down");
    LogEvent("SERVER_SHUTDOWN", "Game server is shutting down");
}

void ComprehensiveGamePlugin::OnHeartbeat()
{
    // Called every heartbeat - don't log this as it would spam
    // Can be used for periodic tasks
}

void ComprehensiveGamePlugin::OnMinuteUpdate()
{
    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Minute update");
    }
}

void ComprehensiveGamePlugin::OnHourUpdate()
{
    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Hour update");
    }
}

void ComprehensiveGamePlugin::OnDayUpdate()
{
    if (m_debugMode)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Day update");
    }
}

// ============================================================================
// COMMAND HANDLING IMPLEMENTATION
// ============================================================================

bool ComprehensiveGamePlugin::OnCommand(LPCHARACTER ch, const char* command, const char* args)
{
    if (!m_running || !m_enableCommandTracking || !ch || !command)
        return false;

    m_stats.commandsProcessed++;

    std::string cmd = command;
    std::string arguments = args ? args : "";

    // Convert to lowercase for case-insensitive comparison
    std::transform(cmd.begin(), cmd.end(), cmd.begin(), ::tolower);

    if (cmd == "plugininfo")
    {
        return HandleInfoCommand(ch, arguments);
    }
    else if (cmd == "pluginstats")
    {
        return HandleStatsCommand(ch, arguments);
    }
    else if (cmd == "plugintest")
    {
        return HandleTestCommand(ch, arguments);
    }
    else if (cmd == "pluginitem")
    {
        return HandleItemCommand(ch, arguments);
    }
    else if (cmd == "pluginguild")
    {
        return HandleGuildCommand(ch, arguments);
    }
    else if (cmd == "pluginquest")
    {
        return HandleQuestCommand(ch, arguments);
    }
    else if (cmd == "pluginskill")
    {
        return HandleSkillCommand(ch, arguments);
    }
    else if (cmd == "pluginmap")
    {
        return HandleMapCommand(ch, arguments);
    }
    else if (cmd == "plugindebug")
    {
        return HandleDebugCommand(ch, arguments);
    }

    return false; // Command not handled by this plugin
}

// ============================================================================
// MAP EVENT IMPLEMENTATIONS
// ============================================================================

void ComprehensiveGamePlugin::OnMapEnter(LPCHARACTER ch, long mapIndex)
{
    if (!m_running || !ch)
        return;

    SendMessage(ch, "Entered map: " + std::to_string(mapIndex));
    DemonstrateMapAccess(ch);

    LogEvent("MAP_ENTER", GetCharacterInfo(ch) + " entered map " + std::to_string(mapIndex));
}

void ComprehensiveGamePlugin::OnMapLeave(LPCHARACTER ch, long mapIndex)
{
    if (!m_running || !ch)
        return;

    LogEvent("MAP_LEAVE", GetCharacterInfo(ch) + " left map " + std::to_string(mapIndex));
}

// ============================================================================
// UTILITY METHODS
// ============================================================================

bool ComprehensiveGamePlugin::HasPermission(LPCHARACTER ch, const std::string& permission) const
{
    if (!ch)
        return false;

    // Simple permission check - in a real implementation, this would check
    // against a proper permission system
    if (permission == "admin")
    {
        return ch->GetGMLevel() >= GM_IMPLEMENTOR;
    }
    else if (permission == "player")
    {
        return ch->IsPC();
    }

    return false;
}

void ComprehensiveGamePlugin::SendMessage(LPCHARACTER ch, const std::string& message) const
{
    if (!ch)
        return;

    ch->ChatPacket(CHAT_TYPE_INFO, "[Plugin] %s", message.c_str());
}

void ComprehensiveGamePlugin::LogEvent(const std::string& event, const std::string& details) const
{
    if (m_enableDetailedLogging)
    {
        sys_log(0, "[ComprehensiveGamePlugin] Event: %s - %s", event.c_str(), details.c_str());
    }
}

std::string ComprehensiveGamePlugin::GetCharacterInfo(LPCHARACTER ch) const
{
    if (!ch)
        return "Unknown Character";

    return std::string(ch->GetName()) + " (ID:" + std::to_string(ch->GetPlayerID()) +
           ", Level:" + std::to_string(ch->GetLevel()) +
           ", Job:" + std::to_string(ch->GetJob()) + ")";
}

std::string ComprehensiveGamePlugin::GetItemInfo(LPITEM item) const
{
    if (!item)
        return "Unknown Item";

    return std::string(item->GetName()) + " (Vnum:" + std::to_string(item->GetVnum()) +
           ", Count:" + std::to_string(item->GetCount()) +
           ", Level:" + std::to_string(item->GetLevel()) + ")";
}

std::string ComprehensiveGamePlugin::GetGuildInfo(LPGUILD guild) const
{
    if (!guild)
        return "Unknown Guild";

    return std::string(guild->GetName()) + " (ID:" + std::to_string(guild->GetID()) +
           ", Level:" + std::to_string(guild->GetLevel()) +
           ", Members:" + std::to_string(guild->GetMemberCount()) + ")";
}

// GetPartyInfo method removed as party events are not in the interface

std::string ComprehensiveGamePlugin::GetShopInfo(LPSHOP shop) const
{
    if (!shop)
        return "Unknown Shop";

    return "Shop (VID:" + std::to_string(shop->GetVID()) + ")";
}

// ============================================================================
// COMMAND HANDLERS
// ============================================================================

bool ComprehensiveGamePlugin::HandleInfoCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    SendMessage(ch, "=== ComprehensiveGamePlugin Info ===");
    SendMessage(ch, "Name: " + GetName());
    SendMessage(ch, "Version: " + GetVersion());
    SendMessage(ch, "Description: " + GetDescription());
    SendMessage(ch, "Status: " + (m_running ? "Running" : "Stopped"));
    SendMessage(ch, "Available commands:");
    SendMessage(ch, "  /plugininfo - Show plugin information");
    SendMessage(ch, "  /pluginstats - Show plugin statistics");
    SendMessage(ch, "  /plugintest - Test plugin functionality");
    SendMessage(ch, "  /pluginitem <vnum> - Create test item");
    SendMessage(ch, "  /plugindebug - Toggle debug mode");

    return true;
}

bool ComprehensiveGamePlugin::HandleStatsCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    if (!HasPermission(ch, "admin"))
    {
        SendMessage(ch, "You don't have permission to view plugin statistics.");
        return true;
    }

    SendMessage(ch, "=== Plugin Statistics ===");
    SendMessage(ch, "Characters Created: " + std::to_string(m_stats.charactersCreated));
    SendMessage(ch, "Items Created: " + std::to_string(m_stats.itemsCreated));
    SendMessage(ch, "Combat Events: " + std::to_string(m_stats.combatEvents));
    SendMessage(ch, "Quests Completed: " + std::to_string(m_stats.questsCompleted));
    SendMessage(ch, "Commands Processed: " + std::to_string(m_stats.commandsProcessed));
    SendMessage(ch, "Packets Processed: " + std::to_string(m_stats.packetsProcessed));

    return true;
}

bool ComprehensiveGamePlugin::HandleTestCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    SendMessage(ch, "=== Plugin Test ===");
    SendMessage(ch, "Testing game object access...");

    // Test CHARACTER access
    DemonstrateCharacterAccess(ch);
    SendMessage(ch, "Character access: OK");

    // Test manager access
    DemonstrateManagerAccess();
    SendMessage(ch, "Manager access: OK");

    // Test utility access
    DemonstrateUtilityAccess();
    SendMessage(ch, "Utility access: OK");

    SendMessage(ch, "All tests completed successfully!");

    return true;
}

bool ComprehensiveGamePlugin::HandleItemCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    if (!HasPermission(ch, "admin"))
    {
        SendMessage(ch, "You don't have permission to create items.");
        return true;
    }

    if (args.empty())
    {
        SendMessage(ch, "Usage: /pluginitem <vnum> [count]");
        return true;
    }

    // Parse arguments
    std::istringstream iss(args);
    std::string vnumStr, countStr;
    iss >> vnumStr >> countStr;

    DWORD vnum = 0;
    int count = 1;

    try
    {
        vnum = std::stoul(vnumStr);
        if (!countStr.empty())
            count = std::stoi(countStr);
    }
    catch (const std::exception& e)
    {
        SendMessage(ch, "Invalid arguments. Usage: /pluginitem <vnum> [count]");
        return true;
    }

    // Create item using ITEM_MANAGER
    LPITEM item = ITEM_MANAGER::instance().CreateItem(vnum, count);
    if (item)
    {
        ch->AutoGiveItem(item);
        SendMessage(ch, "Created item: " + GetItemInfo(item));

        // Demonstrate item access
        DemonstrateItemAccess(item);
    }
    else
    {
        SendMessage(ch, "Failed to create item with vnum: " + std::to_string(vnum));
    }

    return true;
}

bool ComprehensiveGamePlugin::HandleGuildCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    LPGUILD guild = ch->GetGuild();
    if (!guild)
    {
        SendMessage(ch, "You are not in a guild.");
        return true;
    }

    SendMessage(ch, "=== Guild Information ===");
    SendMessage(ch, GetGuildInfo(guild));

    // Demonstrate guild access
    DemonstrateGuildAccess(guild);

    return true;
}

// HandlePartyCommand method removed as party events are not in the interface

bool ComprehensiveGamePlugin::HandleQuestCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    if (args.empty())
    {
        SendMessage(ch, "Usage: /pluginquest <quest_index>");
        return true;
    }

    int questIndex = 0;
    try
    {
        questIndex = std::stoi(args);
    }
    catch (const std::exception& e)
    {
        SendMessage(ch, "Invalid quest index.");
        return true;
    }

    SendMessage(ch, "=== Quest Information ===");
    SendMessage(ch, "Quest Index: " + std::to_string(questIndex));

    // Demonstrate quest access
    DemonstrateQuestAccess(ch, questIndex);

    return true;
}

bool ComprehensiveGamePlugin::HandleSkillCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    if (args.empty())
    {
        SendMessage(ch, "Usage: /pluginskill <skill_vnum>");
        return true;
    }

    DWORD skillVnum = 0;
    try
    {
        skillVnum = std::stoul(args);
    }
    catch (const std::exception& e)
    {
        SendMessage(ch, "Invalid skill vnum.");
        return true;
    }

    SendMessage(ch, "=== Skill Information ===");
    SendMessage(ch, "Skill Vnum: " + std::to_string(skillVnum));

    // Demonstrate skill access
    DemonstrateSkillAccess(ch, skillVnum);

    return true;
}

bool ComprehensiveGamePlugin::HandleMapCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    SendMessage(ch, "=== Map Information ===");
    SendMessage(ch, "Current Map: " + std::to_string(ch->GetMapIndex()));
    SendMessage(ch, "Position: (" + std::to_string(ch->GetX()) + ", " + std::to_string(ch->GetY()) + ")");

    // Demonstrate map access
    DemonstrateMapAccess(ch);

    return true;
}

bool ComprehensiveGamePlugin::HandleDebugCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch)
        return false;

    if (!HasPermission(ch, "admin"))
    {
        SendMessage(ch, "You don't have permission to toggle debug mode.");
        return true;
    }

    m_debugMode = !m_debugMode;
    SendMessage(ch, "Debug mode: " + std::string(m_debugMode ? "ON" : "OFF"));

    return true;
}

// Plugin factory functions
extern "C" {
    IPlugin* CreatePlugin()
    {
        sys_log(0, "[ComprehensiveGamePlugin] CreatePlugin() called");
        auto plugin = new ComprehensiveGamePlugin();
        sys_log(0, "[ComprehensiveGamePlugin] CreatePlugin() returning plugin instance");
        return plugin;
    }

    void DestroyPlugin(IPlugin* plugin)
    {
        sys_log(0, "[ComprehensiveGamePlugin] DestroyPlugin() called");
        delete plugin;
    }

    const char* GetPluginName()
    {
        return "ComprehensiveGamePlugin";
    }

    const char* GetPluginVersion()
    {
        return "1.0.0";
    }

    const char* GetPluginDescription()
    {
        return "Comprehensive game plugin demonstrating full access to game objects and systems using ABI-stable interfaces";
    }
}

// ============================================================================
// PLUGIN FACTORY FUNCTIONS FOR GAME PLUGIN MANAGER
// ============================================================================

// Game plugin factory functions (required by GamePluginManager)
extern "C" {
    IGamePlugin* CreateGamePlugin()
    {
        return new ComprehensiveGamePlugin();
    }

    void DestroyGamePlugin(IGamePlugin* plugin)
    {
        delete plugin;
    }

    const char* GetPluginName()
    {
        return "ComprehensiveGamePlugin";
    }

    const char* GetPluginVersion()
    {
        return "1.0.0";
    }

    const char* GetPluginDescription()
    {
        return "Comprehensive game plugin demonstrating full access to game objects and systems using ABI-stable interfaces";
    }
}
