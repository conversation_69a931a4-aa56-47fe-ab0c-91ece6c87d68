#ifndef __INC_EXTERNAL_API_H__
#define __INC_EXTERNAL_API_H__

/**
 * @file external_api.h
 * @brief Main header for all external C APIs
 * 
 * This file provides a convenient single include for all external C APIs
 * that can be used by the plugin system. It ensures cross-compiler 
 * compatibility between game server (clang) and plugins (gcc/vs).
 * 
 * The external APIs provide C wrappers around C++ classes to maintain
 * ABI stability across different compilers and prevent C++ name mangling
 * and vtable layout issues.
 * 
 * Usage in plugins:
 * ```c
 * #include "external_api.h"
 * 
 * void my_plugin_function() {
 *     // Get character manager instance
 *     CharacterManagerHandle_t mgr = char_manager_api_get_instance();
 *     
 *     // Find a character by name
 *     CharacterHandle_t ch = NULL;
 *     CharApiResult result = char_manager_api_find_pc_by_name(mgr, "PlayerName", &ch);
 *     
 *     if (result == CHAR_API_SUCCESS && ch != NULL) {
 *         // Get character's level
 *         BYTE level;
 *         char_api_get_level(ch, &level);
 *         
 *         // Send a message to the character
 *         char_api_chat_packet(ch, "Hello from plugin!");
 *     }
 * }
 * ```
 * 
 * Key features:
 * - Pure C interface with extern "C" linkage
 * - Opaque handles to hide C++ implementation details
 * - Simple data types for cross-compiler compatibility
 * - Error codes instead of exceptions
 * - No STL containers exposed in the interface
 * - Thread-safe where the underlying C++ classes are thread-safe
 */

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// CORE API INCLUDES
// ============================================================================

// Character API - provides access to individual CHARACTER instances
#include "char_api.h"

// Character Manager API - provides access to CHARACTER_MANAGER singleton
#include "char_manager_api.h"

// ============================================================================
// CONVENIENCE MACROS
// ============================================================================

/**
 * @brief Check if API result indicates success
 */
#define CHAR_API_SUCCEEDED(result) ((result) == CHAR_API_SUCCESS)

/**
 * @brief Check if API result indicates failure
 */
#define CHAR_API_FAILED(result) ((result) != CHAR_API_SUCCESS)

/**
 * @brief Check if handle is valid (not NULL)
 */
#define CHAR_API_HANDLE_VALID(handle) ((handle) != NULL)

/**
 * @brief Check if handle is invalid (NULL)
 */
#define CHAR_API_HANDLE_INVALID(handle) ((handle) == NULL)

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * @brief Convert API result code to human-readable string
 * @param result API result code
 * @return String description of the result code
 */
const char* char_api_result_to_string(CharApiResult result);

/**
 * @brief Get the last error message for debugging
 * @return Last error message, or NULL if no error
 */
const char* char_api_get_last_error(void);

/**
 * @brief Clear the last error message
 */
void char_api_clear_last_error(void);

// ============================================================================
// VERSION INFORMATION
// ============================================================================

/**
 * @brief Get the external API version
 * @param major Output parameter for major version
 * @param minor Output parameter for minor version
 * @param patch Output parameter for patch version
 */
void char_api_get_version(int* major, int* minor, int* patch);

/**
 * @brief Check if the external API is compatible with a specific version
 * @param major Required major version
 * @param minor Required minor version
 * @param patch Required patch version
 * @return 1 if compatible, 0 if not compatible
 */
int char_api_is_compatible(int major, int minor, int patch);

// ============================================================================
// INITIALIZATION AND CLEANUP
// ============================================================================

/**
 * @brief Initialize the external API system
 * @return CHAR_API_SUCCESS on success, error code on failure
 * 
 * This function should be called once when the plugin is loaded.
 * It performs any necessary initialization for the API system.
 */
CharApiResult char_api_initialize(void);

/**
 * @brief Cleanup the external API system
 * 
 * This function should be called once when the plugin is unloaded.
 * It performs any necessary cleanup for the API system.
 */
void char_api_cleanup(void);

// ============================================================================
// DEBUGGING AND LOGGING
// ============================================================================

/**
 * @brief Enable or disable API debugging
 * @param enable 1 to enable debugging, 0 to disable
 */
void char_api_set_debug_mode(int enable);

/**
 * @brief Check if API debugging is enabled
 * @return 1 if debugging is enabled, 0 if disabled
 */
int char_api_is_debug_mode(void);

/**
 * @brief Log a debug message (only if debugging is enabled)
 * @param format Printf-style format string
 * @param ... Format arguments
 */
void char_api_debug_log(const char* format, ...);

// ============================================================================
// COMMON CONSTANTS
// ============================================================================

// API version constants
#define CHAR_API_VERSION_MAJOR 1
#define CHAR_API_VERSION_MINOR 0
#define CHAR_API_VERSION_PATCH 0

// Maximum string lengths
#define CHAR_API_MAX_NAME_LENGTH 64
#define CHAR_API_MAX_MESSAGE_LENGTH 512
#define CHAR_API_MAX_ERROR_LENGTH 256

// Common point types (from game constants)
#ifndef POINT_LEVEL
#define POINT_LEVEL 0
#define POINT_EXP 1
#define POINT_GOLD 2
#define POINT_HP 3
#define POINT_MAX_HP 4
#define POINT_SP 5
#define POINT_MAX_SP 6
#define POINT_STAMINA 7
#define POINT_MAX_STAMINA 8
#define POINT_ST 9
#define POINT_HT 10
#define POINT_DX 11
#define POINT_IQ 12
#endif

// Chat types (from game constants)
#ifndef CHAT_TYPE_TALKING
#define CHAT_TYPE_TALKING 0
#define CHAT_TYPE_INFO 1
#define CHAT_TYPE_NOTICE 2
#define CHAT_TYPE_PARTY 3
#define CHAT_TYPE_GUILD 4
#define CHAT_TYPE_COMMAND 5
#define CHAT_TYPE_SHOUT 6
#define CHAT_TYPE_WHISPER 7
#endif

#ifdef __cplusplus
}
#endif

#endif // __INC_EXTERNAL_API_H__
