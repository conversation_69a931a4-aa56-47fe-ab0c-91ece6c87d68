#ifndef __INC_IFSM_H__
#define __INC_IFSM_H__

// Forward declarations
class CState;

/**
 * @brief Pure virtual interface for CFSM (Finite State Machine) class
 *
 * Provides ABI-stable access to finite state machine functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 *
 * This interface focuses on core FSM operations for state management.
 */
class IFSM
{
public:
    virtual ~IFSM() = default;

    // ============================================================================
    // CORE FSM OPERATIONS
    // ============================================================================

    // Main FSM update cycle
    virtual void Update() = 0;

    // ============================================================================
    // STATE MANAGEMENT
    // ============================================================================

    // State queries and transitions
    virtual bool IsState(CState& state) const = 0;
    virtual bool GotoState(CState& newState) = 0;

    // ============================================================================
    // INITIAL STATE HANDLERS
    // ============================================================================

    // Initial state lifecycle (can be overridden by derived classes)
    virtual void BeginStateInitial() = 0;
    virtual void StateInitial() = 0;
    virtual void EndStateInitial() = 0;

    // ============================================================================
    // STATE INFORMATION
    // ============================================================================

    // State access for debugging and monitoring
    virtual CState* GetCurrentState() const = 0;
    virtual CState* GetNewState() const = 0;
};

#endif // __INC_IFSM_H__
