#ifndef __GenerateRSAKey_1360809189__
#define __GenerateRSAKey_1360809189__

// created : Tue Feb 5 19:11:56 2013 (time_t = 1360809189)
#include <vector>

inline void CreatePublicKey(std::vector<unsigned char>& key)
{
	key.push_back(48);	// 0
	key.push_back(129);	// 1
	key.push_back(135);	// 2
	key.push_back(2);	// 3
	key.push_back(129);	// 4
	key.push_back(129);	// 5
	key.push_back(0);	// 6
	key.push_back(226);	// 7
	key.push_back(206);	// 8
	key.push_back(36);	// 9
	key.push_back(185);	// 10
	key.push_back(164);	// 11
	key.push_back(17);	// 12
	key.push_back(105);	// 13
	key.push_back(86);	// 14
	key.push_back(99);	// 15
	key.push_back(162);	// 16
	key.push_back(214);	// 17
	key.push_back(93);	// 18
	key.push_back(101);	// 19
	key.push_back(235);	// 20
	key.push_back(251);	// 21
	key.push_back(26);	// 22
	key.push_back(67);	// 23
	key.push_back(220);	// 24
	key.push_back(71);	// 25
	key.push_back(80);	// 26
	key.push_back(169);	// 27
	key.push_back(136);	// 28
	key.push_back(222);	// 29
	key.push_back(105);	// 30
	key.push_back(78);	// 31
	key.push_back(101);	// 32
	key.push_back(63);	// 33
	key.push_back(5);	// 34
	key.push_back(188);	// 35
	key.push_back(15);	// 36
	key.push_back(232);	// 37
	key.push_back(194);	// 38
	key.push_back(149);	// 39
	key.push_back(227);	// 40
	key.push_back(20);	// 41
	key.push_back(13);	// 42
	key.push_back(233);	// 43
	key.push_back(203);	// 44
	key.push_back(12);	// 45
	key.push_back(214);	// 46
	key.push_back(18);	// 47
	key.push_back(199);	// 48
	key.push_back(215);	// 49
	key.push_back(127);	// 50
	key.push_back(195);	// 51
	key.push_back(100);	// 52
	key.push_back(154);	// 53
	key.push_back(54);	// 54
	key.push_back(9);	// 55
	key.push_back(229);	// 56
	key.push_back(23);	// 57
	key.push_back(17);	// 58
	key.push_back(255);	// 59
	key.push_back(72);	// 60
	key.push_back(30);	// 61
	key.push_back(66);	// 62
	key.push_back(83);	// 63
	key.push_back(141);	// 64
	key.push_back(154);	// 65
	key.push_back(140);	// 66
	key.push_back(206);	// 67
	key.push_back(116);	// 68
	key.push_back(171);	// 69
	key.push_back(177);	// 70
	key.push_back(116);	// 71
	key.push_back(74);	// 72
	key.push_back(244);	// 73
	key.push_back(2);	// 74
	key.push_back(249);	// 75
	key.push_back(86);	// 76
	key.push_back(233);	// 77
	key.push_back(72);	// 78
	key.push_back(155);	// 79
	key.push_back(14);	// 80
	key.push_back(9);	// 81
	key.push_back(120);	// 82
	key.push_back(197);	// 83
	key.push_back(116);	// 84
	key.push_back(24);	// 85
	key.push_back(122);	// 86
	key.push_back(203);	// 87
	key.push_back(227);	// 88
	key.push_back(67);	// 89
	key.push_back(150);	// 90
	key.push_back(235);	// 91
	key.push_back(100);	// 92
	key.push_back(213);	// 93
	key.push_back(116);	// 94
	key.push_back(25);	// 95
	key.push_back(65);	// 96
	key.push_back(56);	// 97
	key.push_back(86);	// 98
	key.push_back(177);	// 99
	key.push_back(66);	// 100
	key.push_back(49);	// 101
	key.push_back(49);	// 102
	key.push_back(157);	// 103
	key.push_back(177);	// 104
	key.push_back(32);	// 105
	key.push_back(244);	// 106
	key.push_back(95);	// 107
	key.push_back(129);	// 108
	key.push_back(39);	// 109
	key.push_back(207);	// 110
	key.push_back(184);	// 111
	key.push_back(148);	// 112
	key.push_back(107);	// 113
	key.push_back(192);	// 114
	key.push_back(199);	// 115
	key.push_back(113);	// 116
	key.push_back(222);	// 117
	key.push_back(242);	// 118
	key.push_back(62);	// 119
	key.push_back(170);	// 120
	key.push_back(233);	// 121
	key.push_back(20);	// 122
	key.push_back(231);	// 123
	key.push_back(99);	// 124
	key.push_back(44);	// 125
	key.push_back(83);	// 126
	key.push_back(46);	// 127
	key.push_back(51);	// 128
	key.push_back(7);	// 129
	key.push_back(147);	// 130
	key.push_back(236);	// 131
	key.push_back(42);	// 132
	key.push_back(169);	// 133
	key.push_back(15);	// 134
	key.push_back(2);	// 135
	key.push_back(1);	// 136
	key.push_back(7);	// 137
}

#endif
