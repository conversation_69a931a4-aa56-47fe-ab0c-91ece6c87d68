#ifndef __INC_IITEM_H__
#define __INC_IITEM_H__

#include "../stl.h"
#include "../tables.h"
#include "../../game/src/typedef.h"
#include "IEntity.h" // Include IEntity for base class

/**
 * @brief Pure virtual interface for CItem class
 * 
 * Provides ABI-stable access to item functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on item properties and operations.
 */
class IITEM : virtual public IEntity
{
public:
	//IItem(DWORD dwVnum = -1) {}
    virtual ~IITEM() = default;
    
    // ============================================================================
    // BASIC ITEM PROPERTIES
    // ============================================================================
    
    // Item identification
    virtual DWORD GetID() = 0;
    virtual void SetID(DWORD id) = 0;
    virtual DWORD GetVnum() const = 0;
    virtual DWORD GetOriginalVnum() const = 0;

    // Item count and stack
    virtual int GetLevelLimit() = 0;
    virtual DWORD GetCount() = 0;
    virtual bool SetCount(DWORD count) = 0;
    virtual bool IsStackable() = 0;
    
    // ============================================================================
    // ITEM TABLE AND PROPERTIES
    // ============================================================================
    
    // Item table access
    virtual TItemTable const* GetProto() = 0;
    virtual const char* GetName() = 0;
    virtual const char* GetBaseName() = 0;
    virtual int GetType() const = 0;
    virtual BYTE GetSubType() const = 0;
    virtual BYTE GetSize() = 0;
    
    // Item flags
    virtual long GetFlag() = 0;
    virtual uint64_t GetAntiFlag() = 0;
    virtual DWORD GetWearFlag() = 0;
    virtual DWORD GetImmuneFlag() = 0;
    
    // ============================================================================
    // ITEM ATTRIBUTES
    // ============================================================================
    
    // Attribute management
    virtual POINT_TYPE GetAttributeType(BYTE bIndex) = 0;
    virtual POINT_VALUE GetAttributeValue(BYTE bIndex) = 0;
    virtual void SetAttribute(BYTE bIndex, POINT_TYPE wType, POINT_VALUE lValue) = 0;
    virtual void ClearAttribute() = 0;
    virtual void ClearAllAttribute() = 0;
    virtual BYTE GetAttributeCount() = 0;
    
    // Rare attributes
    virtual bool HasRareAttr(POINT_TYPE wApply) = 0;
    virtual bool AddRareAttribute() = 0;
    
    // ============================================================================
    // ITEM SOCKETS
    // ============================================================================
    
    // Socket management
    virtual long GetSocket(int index) = 0;
    virtual void SetSocket(int i, long v, bool bLog = true) = 0;
    virtual int GetSocketCount() = 0;
    
    // ============================================================================
    // ITEM OWNERSHIP
    // ============================================================================
    
    // Owner management
    virtual LPCHARACTER GetOwner() = 0;
    virtual bool IsOwnership(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // ITEM POSITION AND LOCATION
    // ============================================================================
    
    // Position management
    virtual WORD GetCell() = 0;
    virtual void SetCell(LPCHARACTER ch, WORD pos) = 0;
    virtual BYTE GetWindow() = 0;
    virtual void SetWindow(BYTE window) = 0;
    
    // Location queries
    virtual bool IsEquipped() const = 0;
    
    // ============================================================================
    // ITEM OPERATIONS
    // ============================================================================
    
    // Item usage
    virtual bool CanUsedBy(LPCHARACTER ch) = 0;
    virtual bool EquipTo(LPCHARACTER ch, BYTE bWearCell) = 0;
    
    // ============================================================================
    // ITEM ENHANCEMENT
    // ============================================================================
    
    // Refinement
    virtual  int GetRefineLevel() = 0;
    

    // ============================================================================
    // ITEM TIMING
    // ============================================================================
    
    // Time-based properties
    virtual long GetLimitValue(DWORD idx) const = 0;
    
    // ============================================================================
    // ITEM VALIDATION
    // ============================================================================
    
    // Item state checks
    virtual bool IsSealed() const = 0;
    virtual bool isLocked() const = 0;
    
    // Item type checks
    virtual bool IsWeapon() = 0;

    
    // ============================================================================
    // ITEM PERSISTENCE
    // ============================================================================
    
    // Save and load
    virtual void Save() = 0;
    
    // ============================================================================
    // ITEM EFFECTS
    // ============================================================================
    
    // Item effects and bonuses
    virtual bool HasAttr(POINT_TYPE wApply) = 0;
    virtual void ModifyPoints(bool bAdd) = 0;
    

    // ============================================================================
    // UTILITY FUNCTIONS
    // ============================================================================
    
    // Item information
    virtual int GetGold() = 0;
    virtual int GetShopBuyPrice() = 0;
    virtual int GetShopSellPrice() = 0;

};

#endif // __INC_IITEM_H__
