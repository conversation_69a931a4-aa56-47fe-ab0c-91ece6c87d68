# External C API for Character Management
# This CMakeLists.txt builds the external API as a static library
# that can be linked with plugins for cross-compiler compatibility

cmake_minimum_required(VERSION 3.10)

# Project name and version
project(ExternalCharacterAPI VERSION 1.0.0 LANGUAGES C CXX)

# Set C++ standard to match the main project
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set C standard for the C API
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# ============================================================================
# COMPILER FLAGS AND DEFINITIONS
# ============================================================================

# Add compiler-specific flags for cross-compiler compatibility
if(MSVC)
    # Visual Studio specific flags
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_options(/W3)
    
    # Ensure consistent calling convention
    add_compile_options(/Gd)  # Use __cdecl calling convention
    
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    # GCC specific flags
    add_compile_options(-Wall -Wextra)
    
    # Ensure consistent calling convention and ABI
    add_compile_options(-fno-strict-aliasing)
    
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    # Clang specific flags
    add_compile_options(-Wall -Wextra)
    
    # Ensure consistent calling convention and ABI
    add_compile_options(-fno-strict-aliasing)
endif()

# Add definitions for the external API
add_compile_definitions(
    CHAR_API_VERSION_MAJOR=1
    CHAR_API_VERSION_MINOR=0
    CHAR_API_VERSION_PATCH=0
)

# ============================================================================
# SOURCE FILES
# ============================================================================

# External API source files
set(EXTERNAL_API_SOURCES
    char_api.cpp
    char_manager_api.cpp
    external_api.cpp
)

# External API header files
set(EXTERNAL_API_HEADERS
    char_api.h
    char_manager_api.h
    external_api.h
)

# ============================================================================
# INCLUDE DIRECTORIES
# ============================================================================

# Include directories for the external API
set(EXTERNAL_API_INCLUDE_DIRS
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/..
    ${CMAKE_CURRENT_SOURCE_DIR}/../..
    ${CMAKE_CURRENT_SOURCE_DIR}/../../game/src
)

# ============================================================================
# STATIC LIBRARY TARGET
# ============================================================================

# Create static library for the external API
add_library(ExternalCharacterAPI STATIC ${EXTERNAL_API_SOURCES})

# Set target properties
set_target_properties(ExternalCharacterAPI PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    OUTPUT_NAME "char_external_api"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# Include directories for the library
target_include_directories(ExternalCharacterAPI
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
    PRIVATE
        ${EXTERNAL_API_INCLUDE_DIRS}
)

# Compiler definitions for the library
target_compile_definitions(ExternalCharacterAPI
    PRIVATE
        BUILDING_EXTERNAL_API=1
)

# ============================================================================
# EXAMPLE PLUGIN TARGET (OPTIONAL)
# ============================================================================

# Option to build the example plugin
option(BUILD_EXAMPLE_PLUGIN "Build the example plugin demonstrating the external API" OFF)

if(BUILD_EXAMPLE_PLUGIN)
    # Create shared library for the example plugin
    add_library(ExamplePlugin SHARED example_plugin.c)
    
    # Link with the external API
    target_link_libraries(ExamplePlugin PRIVATE ExternalCharacterAPI)
    
    # Include directories for the plugin
    target_include_directories(ExamplePlugin PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
    
    # Set plugin properties
    set_target_properties(ExamplePlugin PROPERTIES
        OUTPUT_NAME "example_plugin"
        LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/plugins"
        RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/plugins"
    )
    
    # Platform-specific plugin settings
    if(WIN32)
        set_target_properties(ExamplePlugin PROPERTIES
            SUFFIX ".dll"
        )
    else()
        set_target_properties(ExamplePlugin PROPERTIES
            SUFFIX ".so"
        )
    endif()
endif()

# ============================================================================
# INSTALLATION
# ============================================================================

# Install the static library
install(TARGETS ExternalCharacterAPI
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install header files
install(FILES ${EXTERNAL_API_HEADERS}
    DESTINATION include/external_api
)

# Install documentation
install(FILES README.md
    DESTINATION share/doc/external_api
)

# ============================================================================
# TESTING (OPTIONAL)
# ============================================================================

# Option to build tests
option(BUILD_EXTERNAL_API_TESTS "Build tests for the external API" OFF)

if(BUILD_EXTERNAL_API_TESTS)
    enable_testing()
    
    # Simple test executable
    add_executable(test_external_api test_external_api.c)
    target_link_libraries(test_external_api PRIVATE ExternalCharacterAPI)
    target_include_directories(test_external_api PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
    
    # Add test
    add_test(NAME external_api_basic_test COMMAND test_external_api)
endif()

# ============================================================================
# DOCUMENTATION GENERATION (OPTIONAL)
# ============================================================================

# Find Doxygen for documentation generation
find_package(Doxygen QUIET)

if(DOXYGEN_FOUND)
    # Option to build documentation
    option(BUILD_EXTERNAL_API_DOCS "Build documentation for the external API" OFF)
    
    if(BUILD_EXTERNAL_API_DOCS)
        # Configure Doxygen
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        # Create Doxyfile if it doesn't exist
        if(NOT EXISTS ${DOXYGEN_IN})
            file(WRITE ${DOXYGEN_IN} "
PROJECT_NAME = \"External Character API\"
PROJECT_VERSION = ${PROJECT_VERSION}
INPUT = ${CMAKE_CURRENT_SOURCE_DIR}
OUTPUT_DIRECTORY = ${CMAKE_CURRENT_BINARY_DIR}/docs
GENERATE_HTML = YES
GENERATE_LATEX = NO
EXTRACT_ALL = YES
EXTRACT_PRIVATE = NO
EXTRACT_STATIC = YES
")
        endif()
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        # Add documentation target
        add_custom_target(external_api_docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
        
        # Install documentation
        install(DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs/html
            DESTINATION share/doc/external_api
            OPTIONAL
        )
    endif()
endif()

# ============================================================================
# PACKAGING
# ============================================================================

# Set package information
set(CPACK_PACKAGE_NAME "ExternalCharacterAPI")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Cross-compiler C API for character management")
set(CPACK_PACKAGE_VENDOR "ProjectZ")

# Include CPack for packaging
include(CPack)

# ============================================================================
# SUMMARY
# ============================================================================

message(STATUS "External Character API Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  C Standard: ${CMAKE_C_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "  Build Example Plugin: ${BUILD_EXAMPLE_PLUGIN}")
message(STATUS "  Build Tests: ${BUILD_EXTERNAL_API_TESTS}")
message(STATUS "  Build Documentation: ${BUILD_EXTERNAL_API_DOCS}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
