# Plugin System API Reference

This document provides a comprehensive reference for the ProjectZ plugin system API.

## Table of Contents

1. [Core Interfaces](#core-interfaces)
2. [Game Plugin Interface](#game-plugin-interface)
3. [Database Plugin Interface](#database-plugin-interface)
4. [Plugin Manager](#plugin-manager)
5. [Configuration System](#configuration-system)
6. [Event System](#event-system)
7. [Utility Classes](#utility-classes)
8. [Data Structures](#data-structures)
9. [Constants and Enums](#constants-and-enums)
10. [Integration Macros](#integration-macros)

## Core Interfaces

### IPlugin

Base interface for all plugins.

```cpp
class IPlugin
{
public:
    virtual ~IPlugin() = default;
    
    // Lifecycle methods
    virtual bool Initialize() = 0;
    virtual bool Start() = 0;
    virtual void Stop() = 0;
    virtual void Shutdown() = 0;
    
    // Information methods
    virtual const PluginInfo& GetInfo() const = 0;
    virtual PluginState GetState() const = 0;
    
    // Configuration methods
    virtual bool LoadConfig(const std::string& configPath) = 0;
    virtual void SaveConfig(const std::string& configPath) = 0;
};
```

#### Methods

**Initialize()**
- Called when the plugin is first loaded
- Perform initialization tasks here
- Return `true` on success, `false` on failure

**Start()**
- Called after successful initialization
- Begin plugin operations here
- Return `true` on success, `false` on failure

**Stop()**
- Called to temporarily stop the plugin
- Plugin can be restarted after stopping
- Save any important state here

**Shutdown()**
- Called for final cleanup before unloading
- Perform all cleanup tasks here
- Plugin cannot be restarted after shutdown

**GetInfo()**
- Return plugin metadata
- See `PluginInfo` structure below

**GetState()**
- Return current plugin state
- See `PluginState` enum below

**LoadConfig(configPath)**
- Load configuration from file
- Return `true` on success

**SaveConfig(configPath)**
- Save current configuration to file

### PluginInfo Structure

```cpp
struct PluginInfo
{
    std::string name;                    // Plugin name
    std::string description;             // Plugin description
    std::string author;                  // Plugin author
    PluginVersion version;               // Plugin version
    PluginVersion requiredApiVersion;    // Required API version
    std::vector<std::string> dependencies; // Plugin dependencies
};
```

### PluginState Enum

```cpp
enum class PluginState
{
    UNLOADED,     // Plugin not loaded
    INITIALIZED,  // Plugin initialized but not started
    RUNNING,      // Plugin running normally
    STOPPED,      // Plugin stopped but can be restarted
    ERROR         // Plugin in error state
};
```

### PluginVersion Structure

```cpp
struct PluginVersion
{
    int major, minor, patch;
    
    PluginVersion(int maj = 0, int min = 0, int pat = 0);
    std::string ToString() const;
    bool operator<(const PluginVersion& other) const;
    bool operator==(const PluginVersion& other) const;
};
```

## Game Plugin Interface

### IGamePlugin

Interface for game server plugins.

```cpp
class IGamePlugin : public IPlugin
{
public:
    // Character events
    virtual void OnCharacterCreate(LPCHARACTER ch) {}
    virtual void OnCharacterDestroy(LPCHARACTER ch) {}
    virtual void OnCharacterLogin(LPCHARACTER ch) {}
    virtual void OnCharacterLogout(LPCHARACTER ch) {}
    virtual void OnCharacterLevelUp(LPCHARACTER ch, BYTE newLevel) {}
    virtual void OnCharacterDead(LPCHARACTER ch, LPCHARACTER killer) {}
    virtual void OnCharacterRevive(LPCHARACTER ch) {}
    
    // Item events
    virtual void OnItemCreate(LPITEM item) {}
    virtual void OnItemDestroy(LPITEM item) {}
    virtual void OnItemEquip(LPCHARACTER ch, LPITEM item) {}
    virtual void OnItemUnequip(LPCHARACTER ch, LPITEM item) {}
    virtual void OnItemUse(LPCHARACTER ch, LPITEM item) {}
    virtual void OnItemDrop(LPCHARACTER ch, LPITEM item) {}
    virtual void OnItemPickup(LPCHARACTER ch, LPITEM item) {}
    
    // Combat events
    virtual void OnAttack(LPCHARACTER attacker, LPCHARACTER victim, int damage) {}
    virtual void OnKill(LPCHARACTER killer, LPCHARACTER victim) {}
    virtual void OnDamage(LPCHARACTER victim, LPCHARACTER attacker, int damage) {}
    
    // Guild events
    virtual void OnGuildCreate(LPGUILD guild) {}
    virtual void OnGuildDestroy(LPGUILD guild) {}
    virtual void OnGuildJoin(LPCHARACTER ch, LPGUILD guild) {}
    virtual void OnGuildLeave(LPCHARACTER ch, LPGUILD guild) {}
    virtual void OnGuildWar(LPGUILD guild1, LPGUILD guild2) {}
    
    // Shop events
    virtual void OnShopBuy(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count) {}
    virtual void OnShopSell(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count) {}
    
    // Chat events
    virtual void OnChat(LPCHARACTER ch, const char* message, int type) {}
    virtual void OnWhisper(LPCHARACTER from, LPCHARACTER to, const char* message) {}
    virtual void OnShout(LPCHARACTER ch, const char* message) {}
    
    // Command events
    virtual bool OnCommand(LPCHARACTER ch, const char* command, const char* args) { return false; }
    
    // Map events
    virtual void OnMapEnter(LPCHARACTER ch, long mapIndex) {}
    virtual void OnMapLeave(LPCHARACTER ch, long mapIndex) {}
    
    // Quest events
    virtual void OnQuestStart(LPCHARACTER ch, int questIndex) {}
    virtual void OnQuestComplete(LPCHARACTER ch, int questIndex) {}
    virtual void OnQuestGiveUp(LPCHARACTER ch, int questIndex) {}
    
    // System events
    virtual void OnServerStart() {}
    virtual void OnServerShutdown() {}
    virtual void OnHeartbeat() {}
    virtual void OnMinuteUpdate() {}
    virtual void OnHourUpdate() {}
    virtual void OnDayUpdate() {}
    
    // Network events
    virtual bool OnPacketReceive(LPCHARACTER ch, BYTE header, const void* data, size_t size) { return false; }
    virtual bool OnPacketSend(LPCHARACTER ch, BYTE header, const void* data, size_t size) { return false; }
};
```

### Event Handler Details

#### Character Events

**OnCharacterCreate(ch)**
- Called when a character object is created
- `ch`: Character pointer

**OnCharacterLogin(ch)**
- Called when a player logs into the game
- `ch`: Character pointer
- Good place for welcome messages, login bonuses

**OnCharacterLevelUp(ch, newLevel)**
- Called when a character gains a level
- `ch`: Character pointer
- `newLevel`: New character level
- Good place for level-up rewards

#### Item Events

**OnItemEquip(ch, item)**
- Called when a character equips an item
- `ch`: Character pointer
- `item`: Item pointer

**OnItemUse(ch, item)**
- Called when a character uses an item
- `ch`: Character pointer
- `item`: Item pointer

#### Combat Events

**OnAttack(attacker, victim, damage)**
- Called when one character attacks another
- `attacker`: Attacking character
- `victim`: Target character
- `damage`: Damage dealt

**OnKill(killer, victim)**
- Called when one character kills another
- `killer`: Character who made the kill
- `victim`: Character who was killed

#### Command Events

**OnCommand(ch, command, args)**
- Called when a player uses a command
- `ch`: Character who used the command
- `command`: Command name (without prefix)
- `args`: Command arguments
- Return `true` if command was handled, `false` otherwise

#### Network Events

**OnPacketReceive(ch, header, data, size)**
- Called when a packet is received from a client
- `ch`: Character who sent the packet
- `header`: Packet header/type
- `data`: Packet data
- `size`: Data size
- Return `true` to block normal processing, `false` to allow

## Database Plugin Interface

### IDBPlugin

Interface for database server plugins.

```cpp
class IDBPlugin : public IPlugin
{
public:
    // Player data events
    virtual void OnPlayerLoad(DWORD playerID, TPlayerTable* playerTable) {}
    virtual void OnPlayerSave(DWORD playerID, TPlayerTable* playerTable) {}
    virtual void OnPlayerCreate(DWORD playerID, TPlayerTable* playerTable) {}
    virtual void OnPlayerDelete(DWORD playerID) {}
    
    // Item data events
    virtual void OnItemLoad(DWORD itemID, TPlayerItem* item) {}
    virtual void OnItemSave(DWORD itemID, TPlayerItem* item) {}
    virtual void OnItemCreate(DWORD itemID, TPlayerItem* item) {}
    virtual void OnItemDestroy(DWORD itemID) {}
    
    // Guild data events
    virtual void OnGuildLoad(DWORD guildID) {}
    virtual void OnGuildSave(DWORD guildID) {}
    virtual void OnGuildCreate(DWORD guildID, const char* guildName, DWORD masterPID) {}
    virtual void OnGuildDestroy(DWORD guildID) {}
    virtual void OnGuildMemberAdd(DWORD guildID, DWORD playerID, BYTE grade) {}
    virtual void OnGuildMemberRemove(DWORD guildID, DWORD playerID) {}
    
    // Login/logout events
    virtual void OnPlayerLogin(const char* account, DWORD playerID, const char* ip) {}
    virtual void OnPlayerLogout(const char* account, DWORD playerID) {}
    virtual void OnAccountLogin(const char* account, const char* ip) {}
    virtual void OnAccountLogout(const char* account) {}
    
    // Peer connection events
    virtual void OnPeerConnect(CPeer* peer) {}
    virtual void OnPeerDisconnect(CPeer* peer) {}
    virtual void OnPeerPacket(CPeer* peer, BYTE header, const void* data, size_t size) {}
    
    // Cache events
    virtual void OnCacheFlush(int cacheType, DWORD id) {}
    virtual void OnCacheLoad(int cacheType, DWORD id) {}
    virtual void OnCacheExpire(int cacheType, DWORD id) {}
    
    // Query events
    virtual void OnQueryExecute(const char* query, int queryType) {}
    virtual void OnQueryResult(SQLResult* result) {}
    virtual void OnQueryError(const char* query, const char* error) {}
    
    // System events
    virtual void OnServerStart() {}
    virtual void OnServerShutdown() {}
    virtual void OnHeartbeat() {}
    virtual void OnMinuteUpdate() {}
    virtual void OnHourUpdate() {}
    virtual void OnDayUpdate() {}
    virtual void OnBackupStart() {}
    virtual void OnBackupComplete() {}
    virtual void OnMaintenanceStart() {}
    virtual void OnMaintenanceComplete() {}
};
```

## Plugin Manager

### GamePluginManager

Manages game server plugins.

```cpp
class GamePluginManager
{
public:
    static GamePluginManager& instance();
    
    bool Initialize();
    void Shutdown();
    
    bool LoadPlugin(const std::string& path);
    bool UnloadPlugin(const std::string& name);
    bool ReloadPlugin(const std::string& name);
    
    IGamePlugin* GetPlugin(const std::string& name);
    std::vector<std::string> GetLoadedPlugins() const;
    
    // Event broadcasting methods
    void BroadcastCharacterLogin(LPCHARACTER ch);
    void BroadcastCharacterLogout(LPCHARACTER ch);
    // ... other broadcast methods
};
```

### DBPluginManager

Manages database server plugins.

```cpp
class DBPluginManager
{
public:
    static DBPluginManager& instance();
    
    bool Initialize();
    void Shutdown();
    
    bool LoadPlugin(const std::string& path);
    bool UnloadPlugin(const std::string& name);
    bool ReloadPlugin(const std::string& name);
    
    IDBPlugin* GetPlugin(const std::string& name);
    std::vector<std::string> GetLoadedPlugins() const;
    
    // Event broadcasting methods
    void BroadcastPlayerLoad(DWORD playerID, TPlayerTable* playerTable);
    void BroadcastPlayerSave(DWORD playerID, TPlayerTable* playerTable);
    // ... other broadcast methods
};
```

## Integration Macros

### Game Plugin Integration

```cpp
// Character events
GAME_PLUGIN_CALL_CHARACTER_LOGIN(ch)
GAME_PLUGIN_CALL_CHARACTER_LOGOUT(ch)
GAME_PLUGIN_CALL_CHARACTER_LEVEL_UP(ch, newLevel)

// Item events
GAME_PLUGIN_CALL_ITEM_EQUIP(ch, item)
GAME_PLUGIN_CALL_ITEM_USE(ch, item)

// Combat events
GAME_PLUGIN_CALL_ATTACK(attacker, victim, damage)
GAME_PLUGIN_CALL_KILL(killer, victim)

// System events
GAME_PLUGIN_CALL_SERVER_START()
GAME_PLUGIN_CALL_SERVER_SHUTDOWN()
```

### Database Plugin Integration

```cpp
// Player data events
DB_PLUGIN_CALL_PLAYER_LOAD(playerID, playerTable)
DB_PLUGIN_CALL_PLAYER_SAVE(playerID, playerTable)

// Query events
DB_PLUGIN_CALL_QUERY_EXECUTE(query, queryType)
DB_PLUGIN_CALL_QUERY_ERROR(query, error)

// System events
DB_PLUGIN_CALL_SERVER_START()
DB_PLUGIN_CALL_BACKUP_START()
```

## Factory Functions

Every plugin must implement these C-style factory functions:

```cpp
extern "C" {
    IPlugin* CreatePlugin();
    void DestroyPlugin(IPlugin* plugin);
}
```

## Plugin Registration

Use these macros to register your plugin:

```cpp
// For game plugins
REGISTER_GAME_PLUGIN("PluginName", PluginClass, capabilities)

// For database plugins
REGISTER_DB_PLUGIN("PluginName", PluginClass, capabilities)
```

## Capabilities

### Game Plugin Capabilities

```cpp
#define CAPABILITY_CHARACTER_EVENTS    0x0001
#define CAPABILITY_ITEM_EVENTS         0x0002
#define CAPABILITY_COMBAT_EVENTS       0x0004
#define CAPABILITY_GUILD_EVENTS        0x0008
#define CAPABILITY_SHOP_EVENTS         0x0010
#define CAPABILITY_CHAT_EVENTS         0x0020
#define CAPABILITY_COMMAND_HANDLING    0x0040
#define CAPABILITY_MAP_EVENTS          0x0080
#define CAPABILITY_QUEST_EVENTS        0x0100
#define CAPABILITY_SYSTEM_EVENTS       0x0200
#define CAPABILITY_NETWORK_EVENTS      0x0400
```

### Database Plugin Capabilities

```cpp
#define CAPABILITY_PLAYER_DATA         0x0001
#define CAPABILITY_ITEM_DATA           0x0002
#define CAPABILITY_GUILD_DATA          0x0004
#define CAPABILITY_LOGIN_LOGOUT        0x0008
#define CAPABILITY_PEER_EVENTS         0x0010
#define CAPABILITY_CACHE_EVENTS        0x0020
#define CAPABILITY_QUERY_EVENTS        0x0040
#define CAPABILITY_SYSTEM_EVENTS       0x0080
#define CAPABILITY_BACKUP_EVENTS       0x0100
#define CAPABILITY_MAINTENANCE_EVENTS  0x0200
```

## Best Practices

1. **Always validate parameters** - Check for null pointers
2. **Handle errors gracefully** - Don't crash the server
3. **Use appropriate log levels** - Don't spam logs
4. **Respect performance** - Keep event handlers lightweight
5. **Clean up resources** - Implement proper cleanup in Shutdown()
6. **Version compatibility** - Check API versions
7. **Thread safety** - Be aware of threading issues

## Error Handling

- Return `false` from Initialize() or Start() on failure
- Log errors using the server's logging system
- Set plugin state to ERROR on critical failures
- Implement graceful degradation when possible

## Memory Management

- Plugins are responsible for their own memory management
- Don't store pointers to game objects long-term
- Clean up all allocated memory in Shutdown()
- Be aware of object lifetimes

## Threading

- Most plugin events are called from the main server thread
- Some events may be called from worker threads
- Use appropriate synchronization when needed
- Don't block the main thread for long periods
