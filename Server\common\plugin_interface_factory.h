#ifndef __INC_PLUGIN_INTERFACE_FACTORY_H__
#define __INC_PLUGIN_INTERFACE_FACTORY_H__
#include "interfaces/IItemManager.h"
#include "interfaces/ICharManager.h"
#include "interfaces/IGuildManager.h"
#include "interfaces/IShopManager.h"
#include "interfaces/IDescManager.h"
#include "interfaces/ISectreeManager.h"
/**
 * @file plugin_interface_factory.h
 * @brief Interface factory system for providing ABI-stable access to game systems
 * 
 * This file defines the factory system that plugins use to access game objects
 * and managers through pure virtual interfaces. This ensures ABI stability
 * across different compilers and versions.
 */

// ============================================================================
// INTERFACE FACTORY SYSTEM
// ============================================================================

/**
 * @brief Main interface provider for plugins
 * 
 * This class provides plugins with access to all game systems through
 * ABI-stable pure virtual interfaces. The factory is provided to plugins
 * during initialization and remains valid throughout the plugin's lifetime.
 */
class IPluginInterfaceFactory
{
public:
    virtual ~IPluginInterfaceFactory() = default;
    
    // ========================================================================
    // SINGLETON MANAGER ACCESS
    // ========================================================================
    
    /**
     * @brief Get the item manager interface
     * @return Pointer to IItemManager interface (never null)
     */
    virtual IItemManager* GetItemManager() = 0;
    
    /**
     * @brief Get the character manager interface
     * @return Pointer to ICharacterManager interface (never null)
     */
    virtual ICharacterManager* GetCharacterManager() = 0;
    
    /**
     * @brief Get the guild manager interface
     * @return Pointer to IGuildManager interface (never null)
     */
    virtual IGuildManager* GetGuildManager() = 0;
    
    /**
     * @brief Get the shop manager interface
     * @return Pointer to ISHOP_MANAGER interface (never null)
     */
    virtual IShopManager* GetShopManager() = 0;
    
    /**
     * @brief Get the descriptor manager interface
     * @return Pointer to IDESC_MANAGER interface (never null)
     */
    virtual IDescManager* GetDescManager() = 0;
    
    /**
     * @brief Get the sectree manager interface
     * @return Pointer to ISECTREE_MANAGER interface (never null)
     */
    virtual ISectreeManager* GetSectreeManager() = 0;

    
    // ========================================================================
    // OBJECT VALIDATION AND CASTING
    // ========================================================================
    
    /**
     * @brief Validate that a character pointer is still valid
     * @param ch Character interface pointer to validate
     * @return true if the character is valid and safe to use
     */
    virtual bool IsValidCharacter(ICHARACTER* ch) = 0;
    
    /**
     * @brief Validate that an item pointer is still valid
     * @param item Item interface pointer to validate
     * @return true if the item is valid and safe to use
     */
    virtual bool IsValidItem(IITEM* item) = 0;
    
    /**
     * @brief Validate that a guild pointer is still valid
     * @param guild Guild interface pointer to validate
     * @return true if the guild is valid and safe to use
     */
    virtual bool IsValidGuild(IGUILD* guild) = 0;
    
    /**
     * @brief Validate that a shop pointer is still valid
     * @param shop Shop interface pointer to validate
     * @return true if the shop is valid and safe to use
     */
    virtual bool IsValidShop(ISHOP* shop) = 0;
    
    // ========================================================================
    // UTILITY FUNCTIONS
    // ========================================================================
    
    /**
     * @brief Get the current server time
     * @return Current server time as timestamp
     */
    virtual DWORD GetServerTime() = 0;
    
    /**
     * @brief Log a message from the plugin
     * @param level Log level (0=normal, 1=error, 2=warning, 3=debug)
     * @param pluginName Name of the plugin logging the message
     * @param format Printf-style format string
     */
    virtual void LogMessage(int level, const char* pluginName, const char* format, ...) = 0;
    
    /**
     * @brief Send a notice to all players
     * @param message Notice message to send
     * @param bigFont Whether to use big font (default: false)
     */
    virtual void SendGlobalNotice(const char* message, bool bigFont = false) = 0;
    
    /**
     * @brief Send a notice to all players in a specific map
     * @param message Notice message to send
     * @param mapIndex Map index to send notice to
     * @param bigFont Whether to use big font (default: false)
     */
    virtual void SendMapNotice(const char* message, long mapIndex, bool bigFont = false) = 0;
    
    /**
     * @brief Get a configuration value as integer
     * @param key Configuration key
     * @param defaultValue Default value if key not found
     * @return Configuration value or default
     */
    virtual int GetConfigInt(const char* key, int defaultValue = 0) = 0;
    
    /**
     * @brief Get a configuration value as string
     * @param key Configuration key
     * @param defaultValue Default value if key not found
     * @return Configuration value or default (pointer valid until next call)
     */
    virtual const char* GetConfigString(const char* key, const char* defaultValue = "") = 0;
    
    /**
     * @brief Get a localized text string
     * @param key Localization key
     * @return Localized text (pointer valid until next call)
     */
    virtual const char* GetLocalizedText(const char* key) = 0;
    
    // ========================================================================
    // ADVANCED OPERATIONS
    // ========================================================================
    
    /**
     * @brief Execute a delayed function call
     * @param delayMs Delay in milliseconds
     * @param callback Function to call after delay
     * @param userData User data to pass to callback
     * @return Event ID that can be used to cancel the delayed call
     */
    virtual DWORD ScheduleDelayedCall(DWORD delayMs, void (*callback)(void*), void* userData) = 0;
    
    /**
     * @brief Cancel a previously scheduled delayed call
     * @param eventId Event ID returned by ScheduleDelayedCall
     * @return true if the event was successfully cancelled
     */
    virtual bool CancelDelayedCall(DWORD eventId) = 0;
    
    /**
     * @brief Schedule a repeating function call
     * @param intervalMs Interval in milliseconds
     * @param callback Function to call at each interval
     * @param userData User data to pass to callback
     * @return Event ID that can be used to cancel the repeating call
     */
    virtual DWORD ScheduleRepeatingCall(DWORD intervalMs, void (*callback)(void*), void* userData) = 0;
    
    /**
     * @brief Cancel a previously scheduled repeating call
     * @param eventId Event ID returned by ScheduleRepeatingCall
     * @return true if the event was successfully cancelled
     */
    virtual bool CancelRepeatingCall(DWORD eventId) = 0;
    
    // ========================================================================
    // PLUGIN COMMUNICATION
    // ========================================================================
    
    /**
     * @brief Send a message to another plugin
     * @param targetPlugin Name of the target plugin
     * @param messageType Message type identifier
     * @param data Message data
     * @param dataSize Size of message data
     * @return true if the message was sent successfully
     */
    virtual bool SendPluginMessage(const char* targetPlugin, DWORD messageType, const void* data, DWORD dataSize) = 0;
    
    /**
     * @brief Register to receive messages of a specific type
     * @param messageType Message type to listen for
     * @param callback Function to call when message is received
     * @param userData User data to pass to callback
     * @return true if registration was successful
     */
    virtual bool RegisterMessageHandler(DWORD messageType, void (*callback)(const char* senderPlugin, DWORD messageType, const void* data, DWORD dataSize, void* userData), void* userData) = 0;
    
    /**
     * @brief Unregister a message handler
     * @param messageType Message type to stop listening for
     * @return true if unregistration was successful
     */
    virtual bool UnregisterMessageHandler(DWORD messageType) = 0;
};

// ============================================================================
// FACTORY ACCESS FUNCTIONS (C-style for ABI stability)
// ============================================================================

extern "C" {
    /**
     * @brief Get the plugin interface factory
     * 
     * This function is called by plugins to get access to the interface factory.
     * The factory provides access to all game systems through ABI-stable interfaces.
     * 
     * @return Pointer to IPluginInterfaceFactory (never null)
     * 
     * @note This function is exported by the game server and imported by plugins.
     *       The returned pointer remains valid throughout the plugin's lifetime.
     */
    IPluginInterfaceFactory* GetPluginInterfaceFactory();
    
    /**
     * @brief Plugin initialization function signature
     * 
     * Every plugin must export a function with this signature named "InitializePlugin".
     * This function is called when the plugin is loaded and provides the plugin
     * with access to the interface factory.
     * 
     * @param factory Pointer to the interface factory
     * @return true if initialization was successful, false otherwise
     */
    typedef bool (*PluginInitializeFunc)(IPluginInterfaceFactory* factory);
    
    /**
     * @brief Plugin shutdown function signature
     * 
     * Every plugin must export a function with this signature named "ShutdownPlugin".
     * This function is called when the plugin is being unloaded.
     */
    typedef void (*PluginShutdownFunc)();
}

// ============================================================================
// PLUGIN HELPER MACROS
// ============================================================================

/**
 * @brief Macro to define the plugin initialization function
 * 
 * Usage:
 * PLUGIN_INITIALIZE(MyPlugin)
 * {
 *     // Plugin initialization code here
 *     // factory is available as parameter
 *     return true; // or false on failure
 * }
 */
#define PLUGIN_INITIALIZE(PluginClass) \
    static IPluginInterfaceFactory* g_pluginFactory = nullptr; \
    static PluginClass* g_pluginInstance = nullptr; \
    extern "C" bool InitializePlugin(IPluginInterfaceFactory* factory) { \
        g_pluginFactory = factory; \
        g_pluginInstance = new PluginClass(factory);

/**
 * @brief Macro to define the plugin shutdown function
 * 
 * Usage:
 * PLUGIN_SHUTDOWN()
 * {
 *     // Plugin cleanup code here
 * }
 */
#define PLUGIN_SHUTDOWN() \
        return g_pluginInstance != nullptr; \
    } \
    extern "C" void ShutdownPlugin() { \
        if (g_pluginInstance) { \
            delete g_pluginInstance; \
            g_pluginInstance = nullptr; \
        } \
        g_pluginFactory = nullptr;\
       }
/**
 * @brief Macro to get the interface factory from within plugin code
 */
#define GET_FACTORY() (g_pluginFactory)

/**
 * @brief Macro to get the plugin instance from within plugin code
 */
#define GET_PLUGIN() (g_pluginInstance)

#endif // __INC_PLUGIN_INTERFACE_FACTORY_H__
