# Plugin Manager Configuration
# This file controls the global plugin system behavior

#==============================================================================
# GENERAL PLUGIN MANAGER SETTINGS
#==============================================================================

# Enable automatic plugin discovery and loading
auto_load_plugins=true

# Enable automatic plugin startup after loading
auto_start_plugins=true

# Maximum time to wait for plugin loading (seconds)
plugin_load_timeout=30

# Maximum time to wait for plugin initialization (seconds)
plugin_init_timeout=10

# Maximum number of plugins that can be loaded
max_plugins=50

#==============================================================================
# DIRECTORY SETTINGS
#==============================================================================

# Base plugin directory (relative to server executable)
plugin_directory=plugins

# Plugin configuration directory
config_directory=plugins/config

# Plugin log directory
log_directory=plugins/logs

# Plugin data directory
data_directory=plugins/data

# Plugin temporary directory
temp_directory=plugins/temp

#==============================================================================
# LOGGING SETTINGS
#==============================================================================

# Plugin manager log level (DEBUG, INFO, WARNING, ERROR)
log_level=INFO

# Plugin manager log file
log_file=plugins/logs/plugin_manager.log

# Maximum log file size in MB
max_log_size=10

# Enable log file rotation
enable_log_rotation=true

# Number of rotated log files to keep
log_rotation_count=5

# Log timestamp format (strftime format)
log_timestamp_format=%Y-%m-%d %H:%M:%S

#==============================================================================
# PERFORMANCE SETTINGS
#==============================================================================

# Plugin system heartbeat interval (milliseconds)
heartbeat_interval=1000

# Statistics collection interval (milliseconds)
stats_collection_interval=60000

# Plugin cleanup interval (milliseconds)
cleanup_interval=300000

# Enable performance monitoring
enable_performance_monitoring=true

# Performance monitoring interval (seconds)
performance_monitoring_interval=300

#==============================================================================
# SECURITY SETTINGS
#==============================================================================

# Allow loading of unsigned plugins
allow_unsigned_plugins=true

# Enable plugin sandboxing (if supported by platform)
enable_plugin_sandbox=false

# Verify plugin digital signatures
verify_plugin_signatures=false

# Allowed plugin file extensions (comma-separated)
allowed_extensions=.dll,.so

# Blocked plugin names (comma-separated)
blocked_plugins=

#==============================================================================
# DATABASE INTEGRATION
#==============================================================================

# Enable database integration for plugins
enable_db_integration=true

# Plugin database query timeout (seconds)
db_query_timeout=30

# Maximum database connections per plugin
max_db_connections_per_plugin=5

# Plugin QID (Query ID) range start
plugin_qid_range_start=10000

# Plugin QID (Query ID) range end
plugin_qid_range_end=19999

# Enable automatic QID assignment
enable_auto_qid_assignment=true

#==============================================================================
# NETWORK INTEGRATION
#==============================================================================

# Enable network packet handling for plugins
enable_packet_handling=true

# Allow plugins to register custom packet types
allow_custom_packets=true

# Enable packet validation for plugin packets
enable_packet_validation=true

# Maximum packet size for plugin packets (bytes)
max_plugin_packet_size=8192

#==============================================================================
# COMMAND INTEGRATION
#==============================================================================

# Enable custom commands from plugins
enable_custom_commands=true

# Default command prefix for plugin commands
default_command_prefix=/

# Restrict plugin commands to GMs only
restrict_commands_to_gms=false

# Maximum number of commands per plugin
max_commands_per_plugin=20

#==============================================================================
# EVENT SYSTEM
#==============================================================================

# Enable event filtering for performance
enable_event_filtering=true

# Filter heartbeat events (reduces spam)
filter_heartbeat_events=true

# Filter debug events in production
filter_debug_events=true

# Filter movement events (high frequency)
filter_movement_events=false

# Maximum events per second per plugin
max_events_per_second=1000

# Event queue size per plugin
event_queue_size=10000

#==============================================================================
# ERROR HANDLING
#==============================================================================

# Action when plugin fails to load (ignore, warn, error)
plugin_load_failure_action=warn

# Action when plugin crashes (restart, disable, shutdown)
plugin_crash_action=disable

# Maximum plugin restart attempts
max_restart_attempts=3

# Plugin restart cooldown (seconds)
restart_cooldown=60

# Enable plugin crash dumps
enable_crash_dumps=true

# Crash dump directory
crash_dump_directory=plugins/crashes

#==============================================================================
# DEVELOPMENT SETTINGS
#==============================================================================

# Enable development mode (more verbose logging, relaxed security)
development_mode=false

# Enable plugin hot reloading
enable_hot_reload=true

# Monitor plugin files for changes
monitor_plugin_files=false

# File monitoring interval (seconds)
file_monitoring_interval=5

# Enable plugin debugging support
enable_plugin_debugging=false

#==============================================================================
# RESOURCE LIMITS
#==============================================================================

# Maximum memory usage per plugin (MB, 0 = unlimited)
max_memory_per_plugin=0

# Maximum CPU time per plugin per second (milliseconds, 0 = unlimited)
max_cpu_time_per_plugin=0

# Maximum file handles per plugin (0 = unlimited)
max_file_handles_per_plugin=0

# Maximum network connections per plugin (0 = unlimited)
max_network_connections_per_plugin=0

#==============================================================================
# PLUGIN DISCOVERY
#==============================================================================

# Plugin discovery method (auto, manual, config)
discovery_method=auto

# Scan subdirectories for plugins
scan_subdirectories=false

# Plugin file name patterns (comma-separated, supports wildcards)
plugin_patterns=*.dll,*.so

# Exclude patterns (comma-separated, supports wildcards)
exclude_patterns=*_test.*,*_debug.*

#==============================================================================
# SPECIFIC PLUGIN SETTINGS
#==============================================================================

# Plugins to load explicitly (comma-separated)
# Leave empty for auto-discovery
explicit_plugin_list=

# Plugins to exclude from loading (comma-separated)
excluded_plugins=

# Plugin load order (comma-separated, others loaded after)
plugin_load_order=

#==============================================================================
# BACKUP AND RECOVERY
#==============================================================================

# Enable automatic plugin data backup
enable_auto_backup=true

# Backup interval (seconds)
backup_interval=3600

# Backup directory
backup_directory=plugins/backups

# Number of backups to keep
backup_retention_count=24

# Enable plugin state recovery on restart
enable_state_recovery=true

#==============================================================================
# NOTES
#==============================================================================

# This configuration file controls the global plugin system behavior
# Individual plugins have their own configuration files in the config directory
# Changes to this file require a server restart to take effect
# For plugin-specific settings, edit the individual plugin configuration files

# For more information, see:
# - CONFIGURATION_GUIDE.md
# - IMPLEMENTATION_GUIDE.md
# - TROUBLESHOOTING.md
