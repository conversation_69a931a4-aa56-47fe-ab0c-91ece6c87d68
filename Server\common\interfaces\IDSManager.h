#ifndef __INC_IDSMANAGER_H__
#define __INC_IDSMANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
struct TItemPos;

/**
 * @brief Pure virtual interface for DSManager singleton
 * 
 * Provides ABI-stable access to Dragon Soul management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on Dragon Soul system management.
 */
class IDSManager
{
public:
    virtual ~IDSManager() = default;
    
    // ============================================================================
    // INITIALIZATION AND CONFIGURATION
    // ============================================================================
    
    // Table loading
    virtual bool ReadDragonSoulTableFile(const char* c_pszFileName) = 0;
    
    // ============================================================================
    // DRAGON SOUL INFORMATION
    // ============================================================================
    
    // Dragon Soul properties
    virtual void GetDragonSoulInfo(DWORD dwVnum, OUT BYTE& bType, OUT BYTE& bGrade, OUT BYTE& bStep, OUT BYTE& bRefine) const = 0;
    virtual WORD GetBasePosition(const LPITEM pItem) const = 0;
    virtual bool IsValidCellForThisItem(const LPITEM pItem, const TItemPos& Cell) const = 0;
    virtual int GetDuration(const LPITEM pItem) const = 0;
    
    // ============================================================================
    // DRAGON SOUL OPERATIONS
    // ============================================================================
    
    // Dragon Soul extraction and manipulation
    virtual bool ExtractDragonHeart(LPCHARACTER ch, LPITEM pItem, LPITEM pExtractor = NULL) = 0;
    virtual bool PullOut(LPCHARACTER ch, TItemPos DestCell, IN OUT LPITEM& pItem, LPITEM pExtractor = NULL) = 0;
    
    // ============================================================================
    // DRAGON SOUL REFINEMENT
    // ============================================================================
    
    // Refinement operations
    virtual bool DoRefineGrade(LPCHARACTER ch, TItemPos(&aItemPoses)[DRAGON_SOUL_REFINE_GRID_SIZE]) = 0;
    virtual bool DoRefineStep(LPCHARACTER ch, TItemPos(&aItemPoses)[DRAGON_SOUL_REFINE_GRID_SIZE]) = 0;
    virtual bool DoRefineStrength(LPCHARACTER ch, TItemPos(&aItemPoses)[DRAGON_SOUL_REFINE_GRID_SIZE]) = 0;
    
#if defined(__DS_CHANGE_ATTR__)
    // Attribute change
    virtual bool DoChangeAttribute(LPCHARACTER lpCh, TItemPos(&arItemPos)[DRAGON_SOUL_REFINE_GRID_SIZE]) = 0;
#endif
    
    // ============================================================================
    // DRAGON SOUL ACTIVATION
    // ============================================================================
    
    // Activation and deactivation
    virtual bool DragonSoulItemInitialize(LPITEM pItem) = 0;
    virtual bool HasActivedAllSlotsByPage(const LPCHARACTER ch, const BYTE bPageIndex = DRAGON_SOUL_DECK_0) const = 0;
    virtual bool IsTimeLeftDragonSoul(LPITEM pItem) const = 0;
    virtual int LeftTime(LPITEM pItem) const = 0;
    virtual bool ActivateDragonSoul(LPITEM pItem) = 0;
    virtual bool DeactivateDragonSoul(LPITEM pItem, bool bSkipRefreshOwnerActiveState = false) = 0;
    virtual bool IsActiveDragonSoul(LPITEM pItem) const = 0;
    
#if defined(__DS_SET__)
    // ============================================================================
    // DRAGON SOUL SET SYSTEM
    // ============================================================================
    
    // Set system operations
    virtual float GetWeight(DWORD dwVnum) = 0;
    virtual int GetApplyCount(DWORD dwVnum) = 0;
    virtual int GetBasicApplyValue(DWORD dwVnum, int iType, bool bAttr = false) = 0;
    virtual int GetAdditionalApplyValue(DWORD dwVnum, int iType, bool bAttr = false) = 0;
#endif
};

#endif // __INC_IDSMANAGER_H__
