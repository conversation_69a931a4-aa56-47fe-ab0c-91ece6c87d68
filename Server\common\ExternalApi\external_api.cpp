#include "external_api.h"
#include <cstdio>
#include <cstdarg>
#include <cstring>
#include <cstdlib>

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// GLOBAL STATE
// ============================================================================

static char g_last_error[CHAR_API_MAX_ERROR_LENGTH] = {0};
static int g_debug_mode = 0;
static int g_initialized = 0;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const char* char_api_result_to_string(CharApiResult result)
{
    switch (result) {
        case CHAR_API_SUCCESS:
            return "Success";
        case CHAR_API_ERROR_NULL_HANDLE:
            return "Null handle error";
        case CHAR_API_ERROR_INVALID_PARAM:
            return "Invalid parameter error";
        case CHAR_API_ERROR_BUFFER_TOO_SMALL:
            return "Buffer too small error";
        case CHAR_API_ERROR_NOT_FOUND:
            return "Not found error";
        case CHAR_API_ERROR_PERMISSION_DENIED:
            return "Permission denied error";
        case CHAR_API_ERROR_INTERNAL:
            return "Internal error";
        default:
            return "Unknown error";
    }
}

const char* char_api_get_last_error(void)
{
    return g_last_error[0] != '\0' ? g_last_error : NULL;
}

void char_api_clear_last_error(void)
{
    g_last_error[0] = '\0';
}

// ============================================================================
// INTERNAL ERROR HANDLING
// ============================================================================

static void set_last_error(const char* format, ...)
{
    va_list args;
    va_start(args, format);
    vsnprintf(g_last_error, sizeof(g_last_error), format, args);
    va_end(args);
    
    // Ensure null termination
    g_last_error[sizeof(g_last_error) - 1] = '\0';
}

// ============================================================================
// VERSION INFORMATION
// ============================================================================

void char_api_get_version(int* major, int* minor, int* patch)
{
    if (major) *major = CHAR_API_VERSION_MAJOR;
    if (minor) *minor = CHAR_API_VERSION_MINOR;
    if (patch) *patch = CHAR_API_VERSION_PATCH;
}

int char_api_is_compatible(int major, int minor, int patch)
{
    // Compatible if major version matches and minor version is >= required
    if (major != CHAR_API_VERSION_MAJOR)
        return 0;
    
    if (CHAR_API_VERSION_MINOR < minor)
        return 0;
    
    // Patch version doesn't affect compatibility
    return 1;
}

// ============================================================================
// INITIALIZATION AND CLEANUP
// ============================================================================

CharApiResult char_api_initialize(void)
{
    if (g_initialized) {
        set_last_error("API already initialized");
        return CHAR_API_ERROR_INTERNAL;
    }
    
    // Clear any previous error state
    char_api_clear_last_error();
    
    // Initialize debug mode from environment variable if available
    const char* debug_env = getenv("CHAR_API_DEBUG");
    if (debug_env && (strcmp(debug_env, "1") == 0 || strcmp(debug_env, "true") == 0)) {
        g_debug_mode = 1;
    }
    
    g_initialized = 1;
    
    char_api_debug_log("Character API initialized (version %d.%d.%d)",
                       CHAR_API_VERSION_MAJOR,
                       CHAR_API_VERSION_MINOR,
                       CHAR_API_VERSION_PATCH);
    
    return CHAR_API_SUCCESS;
}

void char_api_cleanup(void)
{
    if (!g_initialized) {
        return;
    }
    
    char_api_debug_log("Character API cleanup");
    
    // Clear error state
    char_api_clear_last_error();
    
    // Reset debug mode
    g_debug_mode = 0;
    
    g_initialized = 0;
}

// ============================================================================
// DEBUGGING AND LOGGING
// ============================================================================

void char_api_set_debug_mode(int enable)
{
    g_debug_mode = enable ? 1 : 0;
    char_api_debug_log("Debug mode %s", g_debug_mode ? "enabled" : "disabled");
}

int char_api_is_debug_mode(void)
{
    return g_debug_mode;
}

void char_api_debug_log(const char* format, ...)
{
    if (!g_debug_mode) {
        return;
    }
    
    // Print to stderr with timestamp and prefix
    fprintf(stderr, "[CHAR_API_DEBUG] ");
    
    va_list args;
    va_start(args, format);
    vfprintf(stderr, format, args);
    va_end(args);
    
    fprintf(stderr, "\n");
    fflush(stderr);
}

// ============================================================================
// ENHANCED ERROR HANDLING WRAPPERS
// ============================================================================

/**
 * @brief Enhanced wrapper for character API functions with error logging
 */
CharApiResult char_api_call_with_error_handling(
    const char* function_name,
    CharApiResult (*func)(void),
    const char* description)
{
    if (!g_initialized) {
        set_last_error("API not initialized");
        return CHAR_API_ERROR_INTERNAL;
    }
    
    char_api_debug_log("Calling %s: %s", function_name, description);
    
    CharApiResult result = func();
    
    if (result != CHAR_API_SUCCESS) {
        set_last_error("%s failed: %s", function_name, char_api_result_to_string(result));
        char_api_debug_log("ERROR: %s", g_last_error);
    } else {
        char_api_debug_log("%s succeeded", function_name);
    }
    
    return result;
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * @brief Validate a character handle and set error if invalid
 */
int char_api_validate_character_handle(CharacterHandle_t handle, const char* function_name)
{
    if (CHAR_API_HANDLE_INVALID(handle)) {
        set_last_error("%s: Invalid character handle", function_name);
        char_api_debug_log("ERROR: %s called with invalid character handle", function_name);
        return 0;
    }
    return 1;
}

/**
 * @brief Validate a character manager handle and set error if invalid
 */
int char_api_validate_manager_handle(CharacterManagerHandle_t handle, const char* function_name)
{
    if (CHAR_API_HANDLE_INVALID(handle)) {
        set_last_error("%s: Invalid character manager handle", function_name);
        char_api_debug_log("ERROR: %s called with invalid manager handle", function_name);
        return 0;
    }
    return 1;
}

/**
 * @brief Validate a string parameter and set error if invalid
 */
int char_api_validate_string(const char* str, const char* param_name, const char* function_name)
{
    if (!str) {
        set_last_error("%s: %s parameter is NULL", function_name, param_name);
        char_api_debug_log("ERROR: %s called with NULL %s", function_name, param_name);
        return 0;
    }
    return 1;
}

/**
 * @brief Validate a buffer parameter and set error if invalid
 */
int char_api_validate_buffer(void* buffer, int size, const char* param_name, const char* function_name)
{
    if (!buffer) {
        set_last_error("%s: %s buffer is NULL", function_name, param_name);
        char_api_debug_log("ERROR: %s called with NULL %s buffer", function_name, param_name);
        return 0;
    }
    
    if (size <= 0) {
        set_last_error("%s: %s buffer size is invalid (%d)", function_name, param_name, size);
        char_api_debug_log("ERROR: %s called with invalid %s buffer size: %d", function_name, param_name, size);
        return 0;
    }
    
    return 1;
}

// ============================================================================
// STATISTICS AND MONITORING
// ============================================================================

static struct {
    unsigned long total_calls;
    unsigned long successful_calls;
    unsigned long failed_calls;
} g_api_stats = {0, 0, 0};

/**
 * @brief Get API usage statistics
 */
void char_api_get_statistics(unsigned long* total_calls, unsigned long* successful_calls, unsigned long* failed_calls)
{
    if (total_calls) *total_calls = g_api_stats.total_calls;
    if (successful_calls) *successful_calls = g_api_stats.successful_calls;
    if (failed_calls) *failed_calls = g_api_stats.failed_calls;
}

/**
 * @brief Reset API usage statistics
 */
void char_api_reset_statistics(void)
{
    g_api_stats.total_calls = 0;
    g_api_stats.successful_calls = 0;
    g_api_stats.failed_calls = 0;
    char_api_debug_log("API statistics reset");
}

/**
 * @brief Record an API call result for statistics
 */
void char_api_record_call_result(CharApiResult result)
{
    g_api_stats.total_calls++;
    
    if (result == CHAR_API_SUCCESS) {
        g_api_stats.successful_calls++;
    } else {
        g_api_stats.failed_calls++;
    }
}

#ifdef __cplusplus
}
#endif
