#define MAXVALUE 0
#define NONE MAXVALUE
#define ROOT MAXVALUE + 1
#define MUL MAXVALUE + 2
#define PLU MAXVALUE + 3
#define POW MAXVALUE + 4
#define MIN MAXVALUE + 5
#define DIV MAXVALUE + 6
#define OPEN MAXVALUE + 7
#define CLOSE MAXVALUE + 8
#define NUM MAXVALUE + 9
#define ID MAXVALUE + 10
#define EOS MAXVALUE + 11
#define COS MAXVALUE + 12
#define SIN MAXVALUE + 13
#define TAN MAXVALUE + 14
#define COSEC MAXVALUE + 15
#define CSC COSEC
#define SEC MAXVALUE + 16
#define COT MAXVALUE + 17
#define PI ID
#define EXP ID
#define LOG MAXVALUE + 18
#define LN MAXVALUE + 19
#define LOG10 MAXVALUE + 20

#define ABS MAXVALUE + 21
#define MINF MAXVALUE + 22
#define MAXF MAXVALUE + 23
#define IRAND MAXVALUE + 24
#define FRAND MAXVALUE + 25
#define MOD MAXVALUE + 26
#define FLOOR MAXVALUE + 27
#define SIGN MAXVALUE + 28

#define MAXSTACK 100
