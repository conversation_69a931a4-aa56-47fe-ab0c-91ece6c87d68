# libserverkey Library
cmake_minimum_required(VERSION 3.16)

file(GLOB_RECURSE SOURCES "*.cpp" "*.c")
file(GLOB_RECURSE HEADERS "*.h")

create_server_library(libserverkey
    SOURCES ${SOURCES}
    HEADERS ${HEADERS}
    DEPENDENCIES
        Server::libthecore
        OpenSSL::SSL
        OpenSSL::Crypto
)

# Set output directory to match original structure
set_target_properties(libserverkey PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    ARCHIVE_OUTPUT_NAME serverkey
)
