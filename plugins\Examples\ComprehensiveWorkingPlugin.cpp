/**
 * @file ComprehensiveWorkingPlugin.cpp
 * @brief Comprehensive working plugin example demonstrating all plugin system features
 * 
 * This plugin demonstrates:
 * - Both Game and DB plugin interfaces
 * - All major event handlers
 * - Configuration management
 * - Logging and debugging
 * - Custom commands
 * - Database interactions
 * - Statistics tracking
 * 
 * <AUTHOR> System Team
 * @version 2.0.0
 * @date 2024
 */

// Minimal includes to avoid compilation issues
#include <windows.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <fstream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <algorithm>

// Forward declarations to avoid include issues
typedef void* LPCHARACTER;
typedef void* LPITEM;
typedef void* LPGUILD;
typedef void* LPSHOP;
typedef void* LPPEER;
typedef unsigned char BYTE;
typedef unsigned long DWORD;
typedef unsigned short WORD;

// Plugin interface definitions (simplified)
enum class PluginState
{
    PLUGIN_UNLOADED,
    PLUGIN_LOADING,
    PLUGIN_LOADED,
    PLUGIN_INITIALIZING,
    PLUGIN_INITIALIZED,
    PLUGIN_RUNNING,
    <PERSON><PERSON><PERSON>GI<PERSON>_STOPPING,
    PLUGIN_STOPPED,
    PL<PERSON>GI<PERSON>_UNLOADING,
    PLUGIN_ERROR
};

enum class PluginError
{
    NONE,
    FILE_NOT_FOUND,
    INVALID_FORMAT,
    MISSING_SYMBOLS,
    INITIALIZATION_FAILED,
    DEPENDENCY_MISSING,
    VERSION_MISMATCH,
    CONFIGURATION_ERROR,
    RUNTIME_ERROR
};

struct PluginVersion
{
    int major;
    int minor;
    int patch;

    PluginVersion(int maj = 1, int min = 0, int p = 0)
        : major(maj), minor(min), patch(p) {}

    std::string ToString() const
    {
        return std::to_string(major) + "." + std::to_string(minor) + "." + std::to_string(patch);
    }
};

struct PluginInfo
{
    std::string name;
    std::string description;
    std::string author;
    std::string filePath;
    std::string configPath;
    PluginVersion version;
    PluginVersion requiredApiVersion;
    std::vector<std::string> dependencies;
    PluginState state;
    PluginError lastError;
    std::string errorMessage;

    PluginInfo() : version(1, 0, 0), requiredApiVersion(1, 0, 0),
                   state(PluginState::PLUGIN_UNLOADED), lastError(PluginError::NONE) {}
};

class IPlugin
{
public:
    virtual ~IPlugin() = default;
    virtual bool Initialize() = 0;
    virtual bool Start() = 0;
    virtual void Stop() = 0;
    virtual void Shutdown() = 0;
    virtual const PluginInfo& GetInfo() const = 0;
    virtual PluginState GetState() const = 0;
    virtual void SetState(PluginState state) { m_state = state; }
    virtual bool LoadConfig(const std::string& configPath) { return true; }
    virtual void SaveConfig(const std::string& configPath) {}

protected:
    PluginState m_state = PluginState::PLUGIN_UNLOADED;
};

class IGamePlugin : public IPlugin
{
public:
    virtual ~IGamePlugin() = default;

    // Game event hooks (simplified signatures)
    virtual void OnCharacterCreate(LPCHARACTER ch) {}
    virtual void OnCharacterDestroy(LPCHARACTER ch) {}
    virtual void OnCharacterLogin(LPCHARACTER ch) {}
    virtual void OnCharacterLogout(LPCHARACTER ch) {}
    virtual void OnCharacterLevelUp(LPCHARACTER ch, BYTE newLevel) {}
    virtual void OnCharacterDead(LPCHARACTER ch, LPCHARACTER killer) {}
    virtual void OnCharacterRevive(LPCHARACTER ch) {}

    virtual void OnItemCreate(LPITEM item) {}
    virtual void OnItemDestroy(LPITEM item) {}
    virtual void OnItemEquip(LPCHARACTER ch, LPITEM item) {}
    virtual void OnItemUnequip(LPCHARACTER ch, LPITEM item) {}
    virtual void OnItemUse(LPCHARACTER ch, LPITEM item) {}
    virtual void OnItemDrop(LPCHARACTER ch, LPITEM item) {}
    virtual void OnItemPickup(LPCHARACTER ch, LPITEM item) {}

    virtual void OnAttack(LPCHARACTER attacker, LPCHARACTER victim, int damage) {}
    virtual void OnKill(LPCHARACTER killer, LPCHARACTER victim) {}
    virtual void OnDamage(LPCHARACTER victim, LPCHARACTER attacker, int damage) {}

    virtual void OnGuildCreate(LPGUILD guild) {}
    virtual void OnGuildDestroy(LPGUILD guild) {}
    virtual void OnGuildJoin(LPCHARACTER ch, LPGUILD guild) {}
    virtual void OnGuildLeave(LPCHARACTER ch, LPGUILD guild) {}
    virtual void OnGuildWar(LPGUILD guild1, LPGUILD guild2) {}

    virtual void OnShopBuy(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count) {}
    virtual void OnShopSell(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count) {}

    virtual void OnChat(LPCHARACTER ch, const char* message, int type) {}
    virtual void OnWhisper(LPCHARACTER from, LPCHARACTER to, const char* message) {}
    virtual void OnShout(LPCHARACTER ch, const char* message) {}

    virtual bool OnCommand(LPCHARACTER ch, const char* command, const char* args) { return false; }

    virtual void OnMapEnter(LPCHARACTER ch, long mapIndex) {}
    virtual void OnMapLeave(LPCHARACTER ch, long mapIndex) {}

    virtual void OnQuestStart(LPCHARACTER ch, int questIndex) {}
    virtual void OnQuestComplete(LPCHARACTER ch, int questIndex) {}
    virtual void OnQuestGiveUp(LPCHARACTER ch, int questIndex) {}

    virtual void OnServerStart() {}
    virtual void OnServerShutdown() {}
    virtual void OnHeartbeat() {}
    virtual void OnMinuteUpdate() {}
    virtual void OnHourUpdate() {}
    virtual void OnDayUpdate() {}
};

// Plugin configuration structure
struct PluginSettings
{
    bool enableWelcomeMessage = true;
    bool enableLevelUpRewards = true;
    bool enableChatFilter = false;
    bool enableStatistics = true;
    int maxLoginReward = 1000;
    std::vector<std::string> bannedWords;
    std::map<int, int> levelRewards; // level -> reward_vnum
};

// Statistics tracking
struct PluginStatistics
{
    int totalLogins = 0;
    int totalLogouts = 0;
    int totalLevelUps = 0;
    int totalDeaths = 0;
    int totalItemsCreated = 0;
    int totalChatMessages = 0;
    std::chrono::system_clock::time_point startTime;
    
    PluginStatistics() : startTime(std::chrono::system_clock::now()) {}
};

/**
 * @class ComprehensiveWorkingPlugin
 * @brief A comprehensive plugin that implements both Game and DB interfaces
 * 
 * This plugin serves as a complete example of how to create a plugin that
 * works with both the game server and database server.
 */
class ComprehensiveWorkingPlugin : public IGamePlugin
{
public:
    ComprehensiveWorkingPlugin();
    virtual ~ComprehensiveWorkingPlugin();
    
    // =================================================================
    // IPlugin Interface Implementation
    // =================================================================
    
    virtual bool Initialize() override;
    virtual bool Start() override;
    virtual void Stop() override;
    virtual void Shutdown() override;
    virtual const PluginInfo& GetInfo() const override;
    virtual PluginState GetState() const override;
    virtual void SetState(PluginState state) override;
    
    // Configuration management
    virtual bool LoadConfig(const std::string& configPath) override;
    virtual void SaveConfig(const std::string& configPath) override;
    
    // =================================================================
    // IGamePlugin Interface Implementation
    // =================================================================
    
    // Character events
    virtual void OnCharacterCreate(LPCHARACTER ch) override;
    virtual void OnCharacterDestroy(LPCHARACTER ch) override;
    virtual void OnCharacterLogin(LPCHARACTER ch) override;
    virtual void OnCharacterLogout(LPCHARACTER ch) override;
    virtual void OnCharacterLevelUp(LPCHARACTER ch, BYTE newLevel) override;
    virtual void OnCharacterDead(LPCHARACTER ch, LPCHARACTER killer) override;
    virtual void OnCharacterRevive(LPCHARACTER ch) override;
    
    // Item events
    virtual void OnItemCreate(LPITEM item) override;
    virtual void OnItemDestroy(LPITEM item) override;
    virtual void OnItemEquip(LPCHARACTER ch, LPITEM item) override;
    virtual void OnItemUnequip(LPCHARACTER ch, LPITEM item) override;
    virtual void OnItemUse(LPCHARACTER ch, LPITEM item) override;
    virtual void OnItemDrop(LPCHARACTER ch, LPITEM item) override;
    virtual void OnItemPickup(LPCHARACTER ch, LPITEM item) override;
    
    // Combat events
    virtual void OnAttack(LPCHARACTER attacker, LPCHARACTER victim, int damage) override;
    virtual void OnKill(LPCHARACTER killer, LPCHARACTER victim) override;
    virtual void OnDamage(LPCHARACTER victim, LPCHARACTER attacker, int damage) override;
    
    // Guild events
    virtual void OnGuildCreate(LPGUILD guild) override;
    virtual void OnGuildDestroy(LPGUILD guild) override;
    virtual void OnGuildJoin(LPCHARACTER ch, LPGUILD guild) override;
    virtual void OnGuildLeave(LPCHARACTER ch, LPGUILD guild) override;
    virtual void OnGuildWar(LPGUILD guild1, LPGUILD guild2) override;
    
    // Shop events
    virtual void OnShopBuy(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count) override;
    virtual void OnShopSell(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count) override;
    
    // Chat events
    virtual void OnChat(LPCHARACTER ch, const char* message, int type) override;
    virtual void OnWhisper(LPCHARACTER from, LPCHARACTER to, const char* message) override;
    virtual void OnShout(LPCHARACTER ch, const char* message) override;
    
    // Command events
    virtual bool OnCommand(LPCHARACTER ch, const char* command, const char* args) override;
    
    // Map events
    virtual void OnMapEnter(LPCHARACTER ch, long mapIndex) override;
    virtual void OnMapLeave(LPCHARACTER ch, long mapIndex) override;
    
    // Quest events
    virtual void OnQuestStart(LPCHARACTER ch, int questIndex) override;
    virtual void OnQuestComplete(LPCHARACTER ch, int questIndex) override;
    virtual void OnQuestGiveUp(LPCHARACTER ch, int questIndex) override;
    
    // System events
    virtual void OnServerStart() override;
    virtual void OnServerShutdown() override;
    virtual void OnHeartbeat() override;
    virtual void OnMinuteUpdate() override;
    virtual void OnHourUpdate() override;
    virtual void OnDayUpdate() override;
    
private:
    // Plugin information
    PluginInfo m_info;
    PluginState m_state;
    
    // Plugin data
    PluginSettings m_settings;
    PluginStatistics m_statistics;
    std::string m_configPath;
    std::string m_logPath;
    
    // Helper methods
    void LogMessage(const std::string& level, const std::string& message);
    void LoadDefaultSettings();
    void SaveStatistics();
    void LoadStatistics();
    bool IsWordBanned(const std::string& word);
    void GiveLevelUpReward(LPCHARACTER ch, BYTE level);
    void SendWelcomeMessage(LPCHARACTER ch);
    void ShowStatistics(LPCHARACTER ch);
    void ResetStatistics();
    std::string FormatUptime();
    
    // Command handlers
    bool HandlePluginCommand(LPCHARACTER ch, const std::string& cmd, const std::string& args);
    bool HandleStatsCommand(LPCHARACTER ch, const std::string& args);
    bool HandleConfigCommand(LPCHARACTER ch, const std::string& args);
    bool HandleReloadCommand(LPCHARACTER ch, const std::string& args);
};

// =================================================================
// Plugin Factory Functions
// =================================================================

extern "C" {
    __declspec(dllexport) IPlugin* CreatePlugin()
    {
        return new ComprehensiveWorkingPlugin();
    }
    
    __declspec(dllexport) void DestroyPlugin(IPlugin* plugin)
    {
        delete plugin;
    }
    
    __declspec(dllexport) PluginInfo GetPluginInfo()
    {
        PluginInfo info;
        info.name = "ComprehensiveWorkingPlugin";
        info.description = "A comprehensive working plugin example demonstrating all features";
        info.author = "Plugin System Team";
        info.version = PluginVersion(2, 0, 0);
        info.requiredApiVersion = PluginVersion(1, 0, 0);
        return info;
    }
}

// =================================================================
// Implementation
// =================================================================

ComprehensiveWorkingPlugin::ComprehensiveWorkingPlugin()
    : m_state(PluginState::PLUGIN_UNLOADED)
{
    // Initialize plugin info
    m_info.name = "ComprehensiveWorkingPlugin";
    m_info.description = "A comprehensive working plugin example demonstrating all features";
    m_info.author = "Plugin System Team";
    m_info.version = PluginVersion(2, 0, 0);
    m_info.requiredApiVersion = PluginVersion(1, 0, 0);
    m_info.state = PluginState::PLUGIN_UNLOADED;
    m_info.lastError = PluginError::NONE;
    
    LoadDefaultSettings();
}

ComprehensiveWorkingPlugin::~ComprehensiveWorkingPlugin()
{
    if (m_state != PluginState::PLUGIN_UNLOADED)
    {
        Shutdown();
    }
}

bool ComprehensiveWorkingPlugin::Initialize()
{
    SetState(PluginState::PLUGIN_INITIALIZING);
    
    try
    {
        // Set up paths
        m_configPath = "plugins/config/comprehensive_plugin.conf";
        m_logPath = "plugins/logs/comprehensive_plugin.log";
        
        // Load configuration
        LoadConfig(m_configPath);
        
        // Load statistics
        LoadStatistics();
        
        LogMessage("INFO", "Plugin initialized successfully");
        SetState(PluginState::PLUGIN_INITIALIZED);
        return true;
    }
    catch (const std::exception& e)
    {
        LogMessage("ERROR", std::string("Failed to initialize: ") + e.what());
        SetState(PluginState::PLUGIN_ERROR);
        return false;
    }
}

bool ComprehensiveWorkingPlugin::Start()
{
    if (m_state != PluginState::PLUGIN_INITIALIZED)
    {
        LogMessage("ERROR", "Cannot start plugin - not initialized");
        return false;
    }
    
    SetState(PluginState::PLUGIN_RUNNING);
    LogMessage("INFO", "Plugin started successfully");
    return true;
}

void ComprehensiveWorkingPlugin::Stop()
{
    if (m_state == PluginState::PLUGIN_RUNNING)
    {
        SetState(PluginState::PLUGIN_STOPPING);
        
        // Save current statistics
        SaveStatistics();
        
        LogMessage("INFO", "Plugin stopped");
        SetState(PluginState::PLUGIN_STOPPED);
    }
}

void ComprehensiveWorkingPlugin::Shutdown()
{
    SetState(PluginState::PLUGIN_UNLOADING);

    // Save configuration and statistics
    SaveConfig(m_configPath);
    SaveStatistics();

    LogMessage("INFO", "Plugin shutdown complete");
    SetState(PluginState::PLUGIN_UNLOADED);
}

const PluginInfo& ComprehensiveWorkingPlugin::GetInfo() const
{
    return m_info;
}

PluginState ComprehensiveWorkingPlugin::GetState() const
{
    return m_state;
}

void ComprehensiveWorkingPlugin::SetState(PluginState state)
{
    m_state = state;
    m_info.state = state;
}

// =================================================================
// Configuration Management
// =================================================================

bool ComprehensiveWorkingPlugin::LoadConfig(const std::string& configPath)
{
    try
    {
        std::ifstream file(configPath);
        if (!file.is_open())
        {
            LogMessage("WARNING", "Config file not found, using defaults: " + configPath);
            return true; // Use defaults
        }

        std::string line;
        while (std::getline(file, line))
        {
            if (line.empty() || line[0] == '#') continue;

            size_t pos = line.find('=');
            if (pos == std::string::npos) continue;

            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            // Parse configuration values
            if (key == "enable_welcome_message")
                m_settings.enableWelcomeMessage = (value == "true" || value == "1");
            else if (key == "enable_levelup_rewards")
                m_settings.enableLevelUpRewards = (value == "true" || value == "1");
            else if (key == "enable_chat_filter")
                m_settings.enableChatFilter = (value == "true" || value == "1");
            else if (key == "enable_statistics")
                m_settings.enableStatistics = (value == "true" || value == "1");
            else if (key == "max_login_reward")
                m_settings.maxLoginReward = std::stoi(value);
            else if (key == "banned_words")
            {
                // Parse comma-separated list
                std::stringstream ss(value);
                std::string word;
                m_settings.bannedWords.clear();
                while (std::getline(ss, word, ','))
                {
                    word.erase(0, word.find_first_not_of(" \t"));
                    word.erase(word.find_last_not_of(" \t") + 1);
                    if (!word.empty())
                        m_settings.bannedWords.push_back(word);
                }
            }
            else if (key.substr(0, 12) == "level_reward")
            {
                // Parse level_reward_X=Y format
                size_t underscorePos = key.find_last_of('_');
                if (underscorePos != std::string::npos)
                {
                    int level = std::stoi(key.substr(underscorePos + 1));
                    int reward = std::stoi(value);
                    m_settings.levelRewards[level] = reward;
                }
            }
        }

        LogMessage("INFO", "Configuration loaded successfully");
        return true;
    }
    catch (const std::exception& e)
    {
        LogMessage("ERROR", std::string("Failed to load config: ") + e.what());
        return false;
    }
}

void ComprehensiveWorkingPlugin::SaveConfig(const std::string& configPath)
{
    try
    {
        std::ofstream file(configPath);
        if (!file.is_open())
        {
            LogMessage("ERROR", "Cannot save config file: " + configPath);
            return;
        }

        file << "# Comprehensive Working Plugin Configuration\n";
        file << "# Generated automatically - edit with care\n\n";

        file << "# General Settings\n";
        file << "enable_welcome_message=" << (m_settings.enableWelcomeMessage ? "true" : "false") << "\n";
        file << "enable_levelup_rewards=" << (m_settings.enableLevelUpRewards ? "true" : "false") << "\n";
        file << "enable_chat_filter=" << (m_settings.enableChatFilter ? "true" : "false") << "\n";
        file << "enable_statistics=" << (m_settings.enableStatistics ? "true" : "false") << "\n";
        file << "max_login_reward=" << m_settings.maxLoginReward << "\n\n";

        file << "# Chat Filter\n";
        file << "banned_words=";
        for (size_t i = 0; i < m_settings.bannedWords.size(); ++i)
        {
            if (i > 0) file << ",";
            file << m_settings.bannedWords[i];
        }
        file << "\n\n";

        file << "# Level Rewards\n";
        for (const auto& pair : m_settings.levelRewards)
        {
            file << "level_reward_" << pair.first << "=" << pair.second << "\n";
        }

        LogMessage("INFO", "Configuration saved successfully");
    }
    catch (const std::exception& e)
    {
        LogMessage("ERROR", std::string("Failed to save config: ") + e.what());
    }
}

// =================================================================
// Game Event Handlers
// =================================================================

void ComprehensiveWorkingPlugin::OnCharacterCreate(LPCHARACTER ch)
{
    if (!ch) return;

    LogMessage("INFO", std::string("Character created: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));

    if (m_settings.enableStatistics)
    {
        // Note: This would be tracked in DB plugin for persistence
    }
}

void ComprehensiveWorkingPlugin::OnCharacterDestroy(LPCHARACTER ch)
{
    if (!ch) return;

    LogMessage("INFO", std::string("Character destroyed: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));
}

void ComprehensiveWorkingPlugin::OnCharacterLogin(LPCHARACTER ch)
{
    if (!ch) return;

    LogMessage("INFO", std::string("Character login: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));

    if (m_settings.enableStatistics)
    {
        m_statistics.totalLogins++;
    }

    if (m_settings.enableWelcomeMessage)
    {
        SendWelcomeMessage(ch);
    }
}

void ComprehensiveWorkingPlugin::OnCharacterLogout(LPCHARACTER ch)
{
    if (!ch) return;

    LogMessage("INFO", std::string("Character logout: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));

    if (m_settings.enableStatistics)
    {
        m_statistics.totalLogouts++;
    }
}

void ComprehensiveWorkingPlugin::OnCharacterLevelUp(LPCHARACTER ch, BYTE newLevel)
{
    if (!ch) return;

    LogMessage("INFO", std::string("Character level up: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " -> Level " + std::to_string(newLevel));

    if (m_settings.enableStatistics)
    {
        m_statistics.totalLevelUps++;
    }

    if (m_settings.enableLevelUpRewards)
    {
        GiveLevelUpReward(ch, newLevel);
    }
}

void ComprehensiveWorkingPlugin::OnCharacterDead(LPCHARACTER ch, LPCHARACTER killer)
{
    if (!ch) return;

    std::string killerName = killer ? ("Player" + std::to_string(reinterpret_cast<uintptr_t>(killer))) : "Unknown";
    LogMessage("INFO", std::string("Character death: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " killed by " + killerName);

    if (m_settings.enableStatistics)
    {
        m_statistics.totalDeaths++;
    }
}

void ComprehensiveWorkingPlugin::OnCharacterRevive(LPCHARACTER ch)
{
    if (!ch) return;
    LogMessage("INFO", std::string("Character revived: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));
}

void ComprehensiveWorkingPlugin::OnItemCreate(LPITEM item)
{
    if (!item) return;

    LogMessage("DEBUG", std::string("Item created: ") + "Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));

    if (m_settings.enableStatistics)
    {
        m_statistics.totalItemsCreated++;
    }
}

void ComprehensiveWorkingPlugin::OnItemDestroy(LPITEM item)
{
    if (!item) return;
    LogMessage("DEBUG", std::string("Item destroyed: ") + "Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));
}

void ComprehensiveWorkingPlugin::OnItemEquip(LPCHARACTER ch, LPITEM item)
{
    if (!ch || !item) return;
    LogMessage("DEBUG", std::string("Item equipped: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " equipped Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));
}

void ComprehensiveWorkingPlugin::OnItemUnequip(LPCHARACTER ch, LPITEM item)
{
    if (!ch || !item) return;
    LogMessage("DEBUG", std::string("Item unequipped: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " unequipped Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));
}

void ComprehensiveWorkingPlugin::OnItemUse(LPCHARACTER ch, LPITEM item)
{
    if (!ch || !item) return;
    LogMessage("DEBUG", std::string("Item used: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " used Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));
}

void ComprehensiveWorkingPlugin::OnItemDrop(LPCHARACTER ch, LPITEM item)
{
    if (!ch || !item) return;
    LogMessage("DEBUG", std::string("Item dropped: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " dropped Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));
}

void ComprehensiveWorkingPlugin::OnItemPickup(LPCHARACTER ch, LPITEM item)
{
    if (!ch || !item) return;
    LogMessage("DEBUG", std::string("Item picked up: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " picked up Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));
}

void ComprehensiveWorkingPlugin::OnAttack(LPCHARACTER attacker, LPCHARACTER victim, int damage)
{
    if (!attacker || !victim) return;
    LogMessage("DEBUG", std::string("Attack: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(attacker)) + " -> " + "Player" + std::to_string(reinterpret_cast<uintptr_t>(victim)) + " (" + std::to_string(damage) + " damage)");
}

void ComprehensiveWorkingPlugin::OnKill(LPCHARACTER killer, LPCHARACTER victim)
{
    if (!killer || !victim) return;
    LogMessage("INFO", std::string("Kill: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(killer)) + " killed " + "Player" + std::to_string(reinterpret_cast<uintptr_t>(victim)));
}

void ComprehensiveWorkingPlugin::OnDamage(LPCHARACTER victim, LPCHARACTER attacker, int damage)
{
    if (!victim || !attacker) return;
    LogMessage("DEBUG", std::string("Damage: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(victim)) + " took " + std::to_string(damage) + " damage from " + "Player" + std::to_string(reinterpret_cast<uintptr_t>(attacker)));
}

void ComprehensiveWorkingPlugin::OnGuildCreate(LPGUILD guild)
{
    if (!guild) return;
    LogMessage("INFO", std::string("Guild created: ") + "Guild" + std::to_string(reinterpret_cast<uintptr_t>(guild)));
}

void ComprehensiveWorkingPlugin::OnGuildDestroy(LPGUILD guild)
{
    if (!guild) return;
    LogMessage("INFO", std::string("Guild destroyed: ") + "Guild" + std::to_string(reinterpret_cast<uintptr_t>(guild)));
}

void ComprehensiveWorkingPlugin::OnGuildJoin(LPCHARACTER ch, LPGUILD guild)
{
    if (!ch || !guild) return;
    LogMessage("INFO", std::string("Guild join: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " joined " + "Guild" + std::to_string(reinterpret_cast<uintptr_t>(guild)));
}

void ComprehensiveWorkingPlugin::OnGuildLeave(LPCHARACTER ch, LPGUILD guild)
{
    if (!ch || !guild) return;
    LogMessage("INFO", std::string("Guild leave: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " left " + "Guild" + std::to_string(reinterpret_cast<uintptr_t>(guild)));
}

void ComprehensiveWorkingPlugin::OnGuildWar(LPGUILD guild1, LPGUILD guild2)
{
    if (!guild1 || !guild2) return;
    LogMessage("INFO", std::string("Guild war: ") + "Guild" + std::to_string(reinterpret_cast<uintptr_t>(guild1)) + " vs " + "Guild" + std::to_string(reinterpret_cast<uintptr_t>(guild2)));
}

void ComprehensiveWorkingPlugin::OnShopBuy(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count)
{
    if (!ch || !item) return;
    LogMessage("INFO", std::string("Shop buy: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " bought " + std::to_string(count) + "x " + "Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));
}

void ComprehensiveWorkingPlugin::OnShopSell(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count)
{
    if (!ch || !item) return;
    LogMessage("INFO", std::string("Shop sell: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " sold " + std::to_string(count) + "x " + "Item" + std::to_string(reinterpret_cast<uintptr_t>(item)));
}

void ComprehensiveWorkingPlugin::OnChat(LPCHARACTER ch, const char* message, int type)
{
    if (!ch || !message) return;

    if (m_settings.enableStatistics)
    {
        m_statistics.totalChatMessages++;
    }

    if (m_settings.enableChatFilter)
    {
        std::string msg(message);
        if (IsWordBanned(msg))
        {
            LogMessage("WARNING", std::string("Banned word detected from ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + ": " + msg);
            // In a real implementation, you might block the message or take action
        }
    }

    LogMessage("DEBUG", std::string("Chat: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " said: " + message);
}

void ComprehensiveWorkingPlugin::OnWhisper(LPCHARACTER from, LPCHARACTER to, const char* message)
{
    if (!from || !to || !message) return;
    LogMessage("DEBUG", std::string("Whisper: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(from)) + " -> " + "Player" + std::to_string(reinterpret_cast<uintptr_t>(to)) + ": " + message);
}

void ComprehensiveWorkingPlugin::OnShout(LPCHARACTER ch, const char* message)
{
    if (!ch || !message) return;
    LogMessage("DEBUG", std::string("Shout: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " shouted: " + message);
}

bool ComprehensiveWorkingPlugin::OnCommand(LPCHARACTER ch, const char* command, const char* args)
{
    if (!ch || !command) return false;

    std::string cmd(command);
    std::string arguments = args ? args : "";

    // Handle plugin-specific commands
    if (cmd == "plugin" || cmd == "plg")
    {
        return HandlePluginCommand(ch, cmd, arguments);
    }

    return false; // Command not handled by this plugin
}

void ComprehensiveWorkingPlugin::OnMapEnter(LPCHARACTER ch, long mapIndex)
{
    if (!ch) return;
    LogMessage("DEBUG", std::string("Map enter: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " entered map " + std::to_string(mapIndex));
}

void ComprehensiveWorkingPlugin::OnMapLeave(LPCHARACTER ch, long mapIndex)
{
    if (!ch) return;
    LogMessage("DEBUG", std::string("Map leave: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " left map " + std::to_string(mapIndex));
}

void ComprehensiveWorkingPlugin::OnQuestStart(LPCHARACTER ch, int questIndex)
{
    if (!ch) return;
    LogMessage("INFO", std::string("Quest start: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " started quest " + std::to_string(questIndex));
}

void ComprehensiveWorkingPlugin::OnQuestComplete(LPCHARACTER ch, int questIndex)
{
    if (!ch) return;
    LogMessage("INFO", std::string("Quest complete: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " completed quest " + std::to_string(questIndex));
}

void ComprehensiveWorkingPlugin::OnQuestGiveUp(LPCHARACTER ch, int questIndex)
{
    if (!ch) return;
    LogMessage("INFO", std::string("Quest give up: ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) + " gave up quest " + std::to_string(questIndex));
}

void ComprehensiveWorkingPlugin::OnServerStart()
{
    LogMessage("INFO", "Server started - plugin is active");
}

void ComprehensiveWorkingPlugin::OnServerShutdown()
{
    LogMessage("INFO", "Server shutting down - saving plugin data");
    SaveStatistics();
    SaveConfig(m_configPath);
}

void ComprehensiveWorkingPlugin::OnHeartbeat()
{
    // Called every heartbeat - use sparingly
    // LogMessage("DEBUG", "Heartbeat");
}

void ComprehensiveWorkingPlugin::OnMinuteUpdate()
{
    LogMessage("DEBUG", "Minute update - saving statistics");
    SaveStatistics();
}

void ComprehensiveWorkingPlugin::OnHourUpdate()
{
    LogMessage("INFO", "Hour update - " + FormatUptime());
}

void ComprehensiveWorkingPlugin::OnDayUpdate()
{
    LogMessage("INFO", "Day update - resetting daily statistics");
    // Could reset daily stats here
}

// =================================================================
// Helper Methods
// =================================================================

void ComprehensiveWorkingPlugin::LogMessage(const std::string& level, const std::string& message)
{
    try
    {
        std::ofstream logFile(m_logPath, std::ios::app);
        if (logFile.is_open())
        {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto tm = *std::localtime(&time_t);

            logFile << "[" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "] "
                   << "[" << level << "] " << message << std::endl;
        }
    }
    catch (const std::exception& e)
    {
        // Fallback - could use sys_log here
    }
}

void ComprehensiveWorkingPlugin::LoadDefaultSettings()
{
    m_settings.enableWelcomeMessage = true;
    m_settings.enableLevelUpRewards = true;
    m_settings.enableChatFilter = false;
    m_settings.enableStatistics = true;
    m_settings.maxLoginReward = 1000;

    // Default banned words
    m_settings.bannedWords = {"spam", "hack", "cheat", "bot"};

    // Default level rewards
    m_settings.levelRewards[10] = 27001; // Example: Red Potion at level 10
    m_settings.levelRewards[20] = 27002; // Example: Blue Potion at level 20
    m_settings.levelRewards[30] = 27003; // Example: Yellow Potion at level 30
}

void ComprehensiveWorkingPlugin::SaveStatistics()
{
    try
    {
        std::string statsPath = "plugins/data/comprehensive_plugin_stats.dat";
        std::ofstream file(statsPath, std::ios::binary);
        if (file.is_open())
        {
            file.write(reinterpret_cast<const char*>(&m_statistics), sizeof(m_statistics));
            LogMessage("DEBUG", "Statistics saved");
        }
    }
    catch (const std::exception& e)
    {
        LogMessage("ERROR", std::string("Failed to save statistics: ") + e.what());
    }
}

void ComprehensiveWorkingPlugin::LoadStatistics()
{
    try
    {
        std::string statsPath = "plugins/data/comprehensive_plugin_stats.dat";
        std::ifstream file(statsPath, std::ios::binary);
        if (file.is_open())
        {
            file.read(reinterpret_cast<char*>(&m_statistics), sizeof(m_statistics));
            LogMessage("DEBUG", "Statistics loaded");
        }
        else
        {
            // Initialize new statistics
            m_statistics = PluginStatistics();
            LogMessage("INFO", "New statistics initialized");
        }
    }
    catch (const std::exception& e)
    {
        LogMessage("ERROR", std::string("Failed to load statistics: ") + e.what());
        m_statistics = PluginStatistics(); // Reset to defaults
    }
}

bool ComprehensiveWorkingPlugin::IsWordBanned(const std::string& word)
{
    std::string lowerWord = word;
    std::transform(lowerWord.begin(), lowerWord.end(), lowerWord.begin(), ::tolower);

    for (const auto& bannedWord : m_settings.bannedWords)
    {
        std::string lowerBanned = bannedWord;
        std::transform(lowerBanned.begin(), lowerBanned.end(), lowerBanned.begin(), ::tolower);

        if (lowerWord.find(lowerBanned) != std::string::npos)
        {
            return true;
        }
    }

    return false;
}

void ComprehensiveWorkingPlugin::GiveLevelUpReward(LPCHARACTER ch, BYTE level)
{
    if (!ch) return;

    auto it = m_settings.levelRewards.find(level);
    if (it != m_settings.levelRewards.end())
    {
        // In a real implementation, you would give the item here
        // ch->AutoGiveItem(it->second, 1);

        LogMessage("INFO", std::string("Level up reward given to ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)) +
                  " for reaching level " + std::to_string(level) +
                  " (item: " + std::to_string(it->second) + ")");

        // Send message to player
        // ch->ChatPacket(CHAT_TYPE_INFO, "Congratulations! You received a level up reward!");
    }
}

void ComprehensiveWorkingPlugin::SendWelcomeMessage(LPCHARACTER ch)
{
    if (!ch) return;

    // In a real implementation, you would send chat packets here
    // ch->ChatPacket(CHAT_TYPE_INFO, "Welcome to the server!");
    // ch->ChatPacket(CHAT_TYPE_INFO, "This server is enhanced with the Comprehensive Plugin!");

    LogMessage("INFO", std::string("Welcome message sent to ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));
}

void ComprehensiveWorkingPlugin::ShowStatistics(LPCHARACTER ch)
{
    if (!ch) return;

    // In a real implementation, you would send formatted statistics
    // ch->ChatPacket(CHAT_TYPE_INFO, "=== Plugin Statistics ===");
    // ch->ChatPacket(CHAT_TYPE_INFO, "Total Logins: %d", m_statistics.totalLogins);
    // ch->ChatPacket(CHAT_TYPE_INFO, "Total Logouts: %d", m_statistics.totalLogouts);
    // ch->ChatPacket(CHAT_TYPE_INFO, "Total Level Ups: %d", m_statistics.totalLevelUps);
    // ch->ChatPacket(CHAT_TYPE_INFO, "Total Deaths: %d", m_statistics.totalDeaths);
    // ch->ChatPacket(CHAT_TYPE_INFO, "Total Items Created: %d", m_statistics.totalItemsCreated);
    // ch->ChatPacket(CHAT_TYPE_INFO, "Total Chat Messages: %d", m_statistics.totalChatMessages);
    // ch->ChatPacket(CHAT_TYPE_INFO, "Uptime: %s", FormatUptime().c_str());

    LogMessage("INFO", std::string("Statistics shown to ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));
}

void ComprehensiveWorkingPlugin::ResetStatistics()
{
    m_statistics = PluginStatistics();
    LogMessage("INFO", "Statistics reset");
}

std::string ComprehensiveWorkingPlugin::FormatUptime()
{
    auto now = std::chrono::system_clock::now();
    auto duration = now - m_statistics.startTime;
    auto hours = std::chrono::duration_cast<std::chrono::hours>(duration);
    auto minutes = std::chrono::duration_cast<std::chrono::minutes>(duration - hours);
    auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration - hours - minutes);

    return std::to_string(hours.count()) + "h " +
           std::to_string(minutes.count()) + "m " +
           std::to_string(seconds.count()) + "s";
}

// =================================================================
// Command Handlers
// =================================================================

bool ComprehensiveWorkingPlugin::HandlePluginCommand(LPCHARACTER ch, const std::string& cmd, const std::string& args)
{
    if (!ch) return false;

    if (args.empty())
    {
        // Show help
        // ch->ChatPacket(CHAT_TYPE_INFO, "=== Comprehensive Plugin Commands ===");
        // ch->ChatPacket(CHAT_TYPE_INFO, "/plugin stats - Show plugin statistics");
        // ch->ChatPacket(CHAT_TYPE_INFO, "/plugin config - Show configuration");
        // ch->ChatPacket(CHAT_TYPE_INFO, "/plugin reload - Reload configuration (GM only)");
        // ch->ChatPacket(CHAT_TYPE_INFO, "/plugin reset - Reset statistics (GM only)");
        return true;
    }

    std::stringstream ss(args);
    std::string subCommand;
    ss >> subCommand;

    std::string remainingArgs;
    std::getline(ss, remainingArgs);
    if (!remainingArgs.empty() && remainingArgs[0] == ' ')
        remainingArgs = remainingArgs.substr(1);

    if (subCommand == "stats")
    {
        return HandleStatsCommand(ch, remainingArgs);
    }
    else if (subCommand == "config")
    {
        return HandleConfigCommand(ch, remainingArgs);
    }
    else if (subCommand == "reload")
    {
        return HandleReloadCommand(ch, remainingArgs);
    }
    else if (subCommand == "reset")
    {
        // Check if player is GM (in real implementation)
        // if (!ch->IsGM()) return false;

        ResetStatistics();
        // ch->ChatPacket(CHAT_TYPE_INFO, "Plugin statistics have been reset.");
        return true;
    }

    return false;
}

bool ComprehensiveWorkingPlugin::HandleStatsCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch) return false;

    ShowStatistics(ch);
    return true;
}

bool ComprehensiveWorkingPlugin::HandleConfigCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch) return false;

    // Show current configuration
    // ch->ChatPacket(CHAT_TYPE_INFO, "=== Plugin Configuration ===");
    // ch->ChatPacket(CHAT_TYPE_INFO, "Welcome Message: %s", m_settings.enableWelcomeMessage ? "Enabled" : "Disabled");
    // ch->ChatPacket(CHAT_TYPE_INFO, "Level Up Rewards: %s", m_settings.enableLevelUpRewards ? "Enabled" : "Disabled");
    // ch->ChatPacket(CHAT_TYPE_INFO, "Chat Filter: %s", m_settings.enableChatFilter ? "Enabled" : "Disabled");
    // ch->ChatPacket(CHAT_TYPE_INFO, "Statistics: %s", m_settings.enableStatistics ? "Enabled" : "Disabled");
    // ch->ChatPacket(CHAT_TYPE_INFO, "Max Login Reward: %d", m_settings.maxLoginReward);
    // ch->ChatPacket(CHAT_TYPE_INFO, "Banned Words: %d configured", (int)m_settings.bannedWords.size());
    // ch->ChatPacket(CHAT_TYPE_INFO, "Level Rewards: %d configured", (int)m_settings.levelRewards.size());

    LogMessage("INFO", std::string("Configuration shown to ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));
    return true;
}

bool ComprehensiveWorkingPlugin::HandleReloadCommand(LPCHARACTER ch, const std::string& args)
{
    if (!ch) return false;

    // Check if player is GM (in real implementation)
    // if (!ch->IsGM()) return false;

    if (LoadConfig(m_configPath))
    {
        // ch->ChatPacket(CHAT_TYPE_INFO, "Plugin configuration reloaded successfully.");
        LogMessage("INFO", std::string("Configuration reloaded by ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));
    }
    else
    {
        // ch->ChatPacket(CHAT_TYPE_INFO, "Failed to reload plugin configuration.");
        LogMessage("ERROR", std::string("Configuration reload failed, requested by ") + "Player" + std::to_string(reinterpret_cast<uintptr_t>(ch)));
    }

    return true;
}
