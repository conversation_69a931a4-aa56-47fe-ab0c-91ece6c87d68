#include "char_api.h"

// Include the actual CHARACTER class header
#include "../../game/src/char.h"
#include "../../game/src/constants.h"
#include "../../game/src/utils.h"
#include "../../game/src/typedef.h"
#include "../../game/src/packet.h"
#include <cstring>

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// INTERNAL HELPER FUNCTIONS
// ============================================================================

/**
 * @brief Convert CHARACTER pointer to opaque handle
 */
static inline CharacterHandle_t to_handle(CHARACTER* ch)
{
    return reinterpret_cast<CharacterHandle_t>(ch);
}

/**
 * @brief Convert opaque handle to CHARACTER pointer
 */
static inline CHARACTER* from_handle(CharacterHandle_t handle)
{
    return reinterpret_cast<CHARACTER*>(handle);
}

/**
 * @brief Validate character handle
 */
static inline bool is_valid_handle(CharacterHandle_t handle)
{
    return handle != nullptr && from_handle(handle) != nullptr;
}

// ============================================================================
// BASIC CHARACTER INFORMATION
// ============================================================================

CharApiResult char_api_get_name(CharacterHandle_t handle, char* buffer, int buffer_size)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!buffer || buffer_size <= 0)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    const char* name = ch->GetName();
    
    if (!name)
        return CHAR_API_ERROR_NOT_FOUND;
    
    int name_len = strlen(name);
    if (name_len >= buffer_size)
        return CHAR_API_ERROR_BUFFER_TOO_SMALL;
    
    strcpy(buffer, name);
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_vid(CharacterHandle_t handle, DWORD* vid)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!vid)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *vid = ch->GetVID();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_player_id(CharacterHandle_t handle, DWORD* pid)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!pid)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *pid = ch->GetPlayerID();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_race_num(CharacterHandle_t handle, WORD* race)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!race)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *race = ch->GetRaceNum();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_job(CharacterHandle_t handle, BYTE* job)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!job)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *job = ch->GetJob();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_is_pc(CharacterHandle_t handle, int* is_pc)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!is_pc)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *is_pc = ch->IsPC() ? 1 : 0;
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_is_npc(CharacterHandle_t handle, int* is_npc)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!is_npc)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *is_npc = ch->IsNPC() ? 1 : 0;
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_is_monster(CharacterHandle_t handle, int* is_monster)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!is_monster)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *is_monster = ch->IsMonster() ? 1 : 0;
    return CHAR_API_SUCCESS;
}

// ============================================================================
// POSITION AND MOVEMENT
// ============================================================================

CharApiResult char_api_get_position(CharacterHandle_t handle, CharPosition* position)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!position)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    position->x = ch->GetX();
    position->y = ch->GetY();
    position->z = ch->GetZ();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_map_index(CharacterHandle_t handle, long* map_index)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!map_index)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *map_index = ch->GetMapIndex();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_warp(CharacterHandle_t handle, long map_index, long x, long y)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    CHARACTER* ch = from_handle(handle);
    
    // Only allow warping PCs
    if (!ch->IsPC())
        return CHAR_API_ERROR_PERMISSION_DENIED;
    
    ch->WarpSet(x, y, map_index);
    return CHAR_API_SUCCESS;
}

// ============================================================================
// CHARACTER STATS AND POINTS
// ============================================================================

CharApiResult char_api_get_stats(CharacterHandle_t handle, CharStats* stats)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!stats)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    
    stats->hp = ch->GetHP();
    stats->max_hp = ch->GetMaxHP();
    stats->sp = ch->GetSP();
    stats->max_sp = ch->GetMaxSP();
    stats->stamina = ch->GetStamina();
    stats->level = ch->GetLevel();
    stats->exp = ch->GetExp();
    stats->gold = ch->GetGold();
    stats->job = ch->GetJob();
    
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_point(CharacterHandle_t handle, BYTE point_type, POINT_VALUE* value)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!value)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *value = ch->GetPoint(point_type);
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_set_point(CharacterHandle_t handle, BYTE point_type, POINT_VALUE value)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    CHARACTER* ch = from_handle(handle);
    ch->SetPoint(point_type, value);
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_level(CharacterHandle_t handle, BYTE* level)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!level)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *level = ch->GetLevel();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_exp(CharacterHandle_t handle, DWORD* exp)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!exp)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *exp = ch->GetExp();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_gold(CharacterHandle_t handle, int* gold)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!gold)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *gold = ch->GetGold();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_set_gold(CharacterHandle_t handle, int gold)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    CHARACTER* ch = from_handle(handle);
    ch->SetGold(gold);
    return CHAR_API_SUCCESS;
}

// ============================================================================
// HEALTH AND STATUS
// ============================================================================

CharApiResult char_api_get_hp(CharacterHandle_t handle, int* hp)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!hp)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *hp = ch->GetHP();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_max_hp(CharacterHandle_t handle, int* max_hp)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!max_hp)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *max_hp = ch->GetMaxHP();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_set_hp(CharacterHandle_t handle, int hp)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    CHARACTER* ch = from_handle(handle);
    ch->SetHP(hp);
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_get_sp(CharacterHandle_t handle, int* sp)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!sp)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *sp = ch->GetSP();
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_set_sp(CharacterHandle_t handle, int sp)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    CHARACTER* ch = from_handle(handle);
    ch->SetSP(sp);
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_is_dead(CharacterHandle_t handle, int* is_dead)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!is_dead)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    *is_dead = ch->IsDead() ? 1 : 0;
    return CHAR_API_SUCCESS;
}

// ============================================================================
// COMMUNICATION
// ============================================================================

CharApiResult char_api_chat_packet(CharacterHandle_t handle, const char* message)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!message)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    
    // Only send to PCs
    if (!ch->IsPC())
        return CHAR_API_ERROR_PERMISSION_DENIED;
    
    ch->ChatPacket(CHAT_TYPE_INFO, "%s", message);
    return CHAR_API_SUCCESS;
}

CharApiResult char_api_notice(CharacterHandle_t handle, const char* message)
{
    if (!is_valid_handle(handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!message)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER* ch = from_handle(handle);
    
    // Only send to PCs
    if (!ch->IsPC())
        return CHAR_API_ERROR_PERMISSION_DENIED;
    
    ch->ChatPacket(CHAT_TYPE_NOTICE, "%s", message);
    return CHAR_API_SUCCESS;
}

#ifdef __cplusplus
}
#endif
