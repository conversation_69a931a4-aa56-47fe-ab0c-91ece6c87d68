#ifndef __INC_CROSS_COMPILER_GAME_PLUGIN_H__
#define __INC_CROSS_COMPILER_GAME_PLUGIN_H__

#include "../../Server/common/plugin_interface.h"
#include "../../Server/common/plugin_game_interfaces.h"
#include "../../Server/common/plugin_manager_interfaces.h"
#include "../../Server/common/plugin_interface_factory.h"
#include "../../Server/game/src/game_plugin_manager.h"

/**
 * @brief Cross-Compiler Compatible Game Plugin
 * 
 * This plugin demonstrates the pure virtual interface approach that ensures
 * ABI compatibility across different compilers (Clang, GCC, MSVC).
 * 
 * Key Features:
 * - Uses only pure virtual interfaces (ICHARACTER, IITEM, etc.)
 * - No direct access to game class implementations
 * - ABI-stable across compiler boundaries
 * - Safe for game server compiled with Clang and plugin with GCC
 * - Demonstrates full game object access through interfaces
 * 
 * Interface Usage Examples:
 * - ICHARACTER* ch - Access character data and methods
 * - IITEM* item - Access item properties and operations
 * - IITEM_MANAGER* - Create/destroy items safely
 * - ICHARACTER_MANAGER* - Find and manage characters
 * - IGUILD* guild - Access guild information and operations
 * - ISHOP* shop - Handle shop transactions
 * 
 * This approach ensures that:
 * 1. Plugin compiled with GCC works with Clang-compiled game server
 * 2. Different compiler versions don't break compatibility
 * 3. Future game updates don't break existing plugins
 * 4. Clean separation between plugin and game internals
 */
class CrossCompilerGamePlugin : public IGamePlugin
{
public:
    CrossCompilerGamePlugin();
    virtual ~CrossCompilerGamePlugin();
    
    // IPlugin interface implementation
    virtual bool Initialize() override;
    virtual bool Start() override;
    virtual void Stop() override;
    virtual void Shutdown() override;
    virtual const PluginInfo& GetInfo() const override;
    virtual PluginState GetState() const override;
    virtual void SetState(PluginState state) override;

    // IGamePlugin interface - Character events (ABI-stable)
    virtual void OnCharacterCreate(ICHARACTER* ch) override;
    virtual void OnCharacterDestroy(ICHARACTER* ch) override;
    virtual void OnCharacterLogin(ICHARACTER* ch) override;
    virtual void OnCharacterLogout(ICHARACTER* ch) override;
    virtual void OnCharacterLevelUp(ICHARACTER* ch, PLUGIN_BYTE newLevel) override;
    virtual void OnCharacterDead(ICHARACTER* ch, ICHARACTER* killer) override;
    virtual void OnCharacterRevive(ICHARACTER* ch) override;
    
    // IGamePlugin interface - Item events (ABI-stable)
    virtual void OnItemCreate(IITEM* item) override;
    virtual void OnItemDestroy(IITEM* item) override;
    virtual void OnItemEquip(ICHARACTER* ch, IITEM* item) override;
    virtual void OnItemUnequip(ICHARACTER* ch, IITEM* item) override;
    virtual void OnItemUse(ICHARACTER* ch, IITEM* item) override;
    virtual void OnItemDrop(ICHARACTER* ch, IITEM* item) override;
    virtual void OnItemPickup(ICHARACTER* ch, IITEM* item) override;
    
    // IGamePlugin interface - Combat events (ABI-stable)
    virtual void OnAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage) override;
    virtual void OnKill(ICHARACTER* killer, ICHARACTER* victim) override;
    virtual void OnDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage) override;
    
    // IGamePlugin interface - Guild events (ABI-stable)
    virtual void OnGuildCreate(IGUILD* guild) override;
    virtual void OnGuildDestroy(IGUILD* guild) override;
    virtual void OnGuildJoin(ICHARACTER* ch, IGUILD* guild) override;
    virtual void OnGuildLeave(ICHARACTER* ch, IGUILD* guild) override;
    virtual void OnGuildWar(IGUILD* guild1, IGUILD* guild2) override;
    
    // IGamePlugin interface - Shop events (ABI-stable)
    virtual void OnShopBuy(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count) override;
    virtual void OnShopSell(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count) override;
    
    // IGamePlugin interface - Quest events (ABI-stable)
    virtual void OnQuestStart(ICHARACTER* ch, int questIndex) override;
    virtual void OnQuestComplete(ICHARACTER* ch, int questIndex) override;
    virtual void OnQuestGiveUp(ICHARACTER* ch, int questIndex) override;
    
    // IGamePlugin interface - Chat events (ABI-stable)
    virtual void OnChat(ICHARACTER* ch, const char* message, int type) override;
    virtual void OnWhisper(ICHARACTER* from, ICHARACTER* to, const char* message) override;
    virtual void OnShout(ICHARACTER* ch, const char* message) override;
    
    // IGamePlugin interface - Command handling (ABI-stable)
    virtual bool OnCommand(ICHARACTER* ch, const char* command, const char* args) override;
    
    // IGamePlugin interface - Map events (ABI-stable)
    virtual void OnMapEnter(ICHARACTER* ch, PLUGIN_LONG mapIndex) override;
    virtual void OnMapLeave(ICHARACTER* ch, PLUGIN_LONG mapIndex) override;
    
    // IGamePlugin interface - System events
    virtual void OnServerStart() override;
    virtual void OnServerShutdown() override;
    virtual void OnHeartbeat() override;
    virtual void OnMinuteUpdate() override;
    virtual void OnHourUpdate() override;
    virtual void OnDayUpdate() override;

private:
    // Plugin state
    bool m_initialized;
    bool m_running;
    bool m_debugMode;
    PluginInfo m_info;
    PluginState m_state;

    
    // Cached interface pointers (for performance)
    IITEM_MANAGER* m_itemManager;
    ICHARACTER_MANAGER* m_characterManager;
    IGUILD_MANAGER* m_guildManager;
    ISHOP_MANAGER* m_shopManager;
    IDESC_MANAGER* m_descManager;
    ISECTREE_MANAGER* m_sectreeManager;
    IGAME_UTILS* m_gameUtils;
    
    // Statistics tracking
    struct Statistics
    {
        int charactersCreated;
        int itemsCreated;
        int combatEvents;
        int questsCompleted;
        int commandsProcessed;
        PLUGIN_DWORD totalPlayTime;
        PLUGIN_DWORD itemsGiven;
        PLUGIN_DWORD goldGiven;
    } m_stats;
    
    // Helper methods for demonstrating interface usage
    void DemonstrateCharacterAccess(ICHARACTER* ch);
    void DemonstrateItemAccess(IITEM* item);
    void DemonstrateGuildAccess(IGUILD* guild);
    void DemonstrateShopAccess(ISHOP* shop);
    void DemonstrateManagerAccess();
    void DemonstrateUtilityAccess();
    
    // Command handlers
    bool HandleInfoCommand(ICHARACTER* ch, const char* args);
    bool HandleStatsCommand(ICHARACTER* ch, const char* args);
    bool HandleTestCommand(ICHARACTER* ch, const char* args);
    bool HandleItemCommand(ICHARACTER* ch, const char* args);
    bool HandleGuildCommand(ICHARACTER* ch, const char* args);
    bool HandleRewardCommand(ICHARACTER* ch, const char* args);
    bool HandleDebugCommand(ICHARACTER* ch, const char* args);
    
    // Utility methods
    void SendMessage(ICHARACTER* ch, const char* message);
    void LogEvent(const char* event, const char* details = "");
    const char* GetCharacterInfo(ICHARACTER* ch);
    const char* GetItemInfo(IITEM* item);
    const char* GetGuildInfo(IGUILD* guild);
   
    
    // Configuration and persistence
    void LoadConfiguration();
    void SaveStatistics();
    void InitializeInterfaces();
    
    // Event scheduling examples
    void ScheduleDelayedReward(ICHARACTER* ch, PLUGIN_DWORD itemVnum, PLUGIN_DWORD delayMs);
    void SchedulePeriodicAnnouncement();
    
    // Plugin communication examples
    void SendMessageToOtherPlugin(const char* targetPlugin, const char* message);
    void RegisterForPluginMessages();
    
    // Static callback functions for delayed events
    static void DelayedRewardCallback(void* userData);
    static void PeriodicAnnouncementCallback(void* userData);
    static void PluginMessageCallback(const char* senderPlugin, PLUGIN_DWORD messageType, const void* data, PLUGIN_DWORD dataSize, void* userData);
};

// Plugin data structure for delayed callbacks
struct DelayedRewardData
{
    CrossCompilerGamePlugin* plugin;
    PLUGIN_DWORD characterPID;
    PLUGIN_DWORD itemVnum;
    PLUGIN_DWORD count;
};

struct PluginMessageData
{
    CrossCompilerGamePlugin* plugin;
};

#endif // __INC_CROSS_COMPILER_GAME_PLUGIN_H__
