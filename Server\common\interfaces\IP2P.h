#ifndef __INC_IP2P_H__
#define __INC_IP2P_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
class FDWATCH;
typedef FDWATCH* LPFDWATCH;

/**
 * @brief Pure virtual interface for DESC_P2P class
 * 
 * Provides ABI-stable access to P2P descriptor functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on P2P connection management.
 */
class IP2P
{
public:
    virtual ~IP2P() = default;
    
    // ============================================================================
    // P2P CONNECTION MANAGEMENT
    // ============================================================================
    
    // Connection setup
    virtual bool Setup(LPFDWATCH fdw, socket_t fd, const char* host, WORD wPort) = 0;
    
    // Connection lifecycle
    virtual void Destroy() = 0;
    virtual void SetPhase(int iPhase) = 0;
};

#endif // __INC_IP2P_H__
