#ifndef __INC_SECTREE_MANAGER_H__
#define __INC_SECTREE_MANAGER_H__

#include "../../common/interfaces/ISectreeManager.h" // Temporarily disabled for compilation
#include "sectree.h"

struct TMapRegion
{
	int index;
	int sx, sy, ex, ey;
	PIXEL_POSITION posSpawn;

	bool bEmpireSpawnDifferent;
	PIXEL_POSITION posEmpire[3];

	std::string strMapName;
};

struct TAreaInfo
{
	int sx, sy, ex, ey, dir;
	TAreaInfo(int sx, int sy, int ex, int ey, int dir)
		: sx(sx), sy(sy), ex(ex), ey(ey), dir(dir)
	{}
};

struct npc_info
{
	BYTE bType;
	const char* name;
	long x, y;
	npc_info(BYTE bType, const char* name, long x, long y)
		: bType(bType), name(name), x(x), y(y)
	{}
};

typedef std::map<std::string, TAreaInfo> TAreaMap;

struct TMapSetting
{
	int iIndex;
	int iCellScale;
	int iBaseX;
	int iBaseY;
	int iWidth;
	int iHeight;

	PIXEL_POSITION posSpawn;
};

class SECTREE_MAP
{
public:
	typedef std::map<DWORD, LPSECTREE> MapType;

	SECTREE_MAP();
	SECTREE_MAP(SECTREE_MAP& r);
	virtual ~SECTREE_MAP();

	bool Add(DWORD key, LPSECTREE sectree)
	{
		return map_.insert(MapType::value_type(key, sectree)).second;
	}

	LPSECTREE Find(DWORD dwPackage);
	LPSECTREE Find(DWORD x, DWORD y);
	void Build();

	TMapSetting	m_setting;

	template< typename Func >
	void for_each(Func& rfunc)
	{
		// <Factor> Using snapshot copy to avoid side-effects
		FCollectEntity collector;
		std::map<DWORD, LPSECTREE>::iterator it = map_.begin();
		for (; it != map_.end(); ++it)
		{
			LPSECTREE sectree = it->second;
			sectree->for_each_entity(collector);
		}
		collector.ForEach(rfunc);
		/*
		std::map<DWORD, LPSECTREE>::iterator i = map_.begin();
		for (; i != map_.end(); ++i)
		{
			LPSECTREE pSec = i->second;
			pSec->for_each_entity(rfunc);
		}
		*/
	}

	void DumpAllToSysErr()
	{
		SECTREE_MAP::MapType::iterator i;
		for (i = map_.begin(); i != map_.end(); ++i)
		{
			sys_err("SECTREE %x(%u, %u)", i->first, i->first & 0xffff, i->first >> 16);
		}
	}

private:
	MapType map_;
};



typedef std::map<int, TAreaMap> pkAreaMap;
typedef std::vector<npc_info> NPCInfoVector;
typedef std::map<DWORD, NPCInfoVector> NPCPositionMap;

class SECTREE_MANAGER : public singleton<SECTREE_MANAGER> , virtual public ISectreeManager
{
public:
	SECTREE_MANAGER();
	virtual ~SECTREE_MANAGER();

	LPSECTREE_MAP GetMap(long lMapIndex);
	LPSECTREE Get(DWORD dwIndex, DWORD package);
	LPSECTREE Get(DWORD dwIndex, DWORD x, DWORD y);

	template< typename Func >
	void for_each(int iMapIndex, Func& rfunc)
	{
		LPSECTREE_MAP pSecMap = SECTREE_MANAGER::instance().GetMap(iMapIndex);
		if (pSecMap)
		{
			pSecMap->for_each(rfunc);
		}
	}

	int LoadSettingFile(long lIndex, const char* c_pszSettingFileName, TMapSetting& r_setting);
	bool LoadMapRegion(const char* c_pszFileName, TMapSetting& r_Setting, const char* c_pszMapName);
	int Build(const char* c_pszListFileName, const char* c_pszBasePath);
	LPSECTREE_MAP BuildSectreeFromSetting(TMapSetting& r_setting);
	bool LoadAttribute(LPSECTREE_MAP pkMapSectree, const char* c_pszFileName, TMapSetting& r_setting);
	void LoadDungeon(int iIndex, const char* c_pszFileName);
	bool GetValidLocation(long lMapIndex, long x, long y, long& r_lValidMapIndex, PIXEL_POSITION& r_pos, BYTE empire = 0);
	bool GetSpawnPosition(long x, long y, PIXEL_POSITION& r_pos);
	bool GetSpawnPositionByMapIndex(long lMapIndex, PIXEL_POSITION& r_pos);
	bool GetRecallPositionByEmpire(int iMapIndex, BYTE bEmpire, PIXEL_POSITION& r_pos);

	const TMapRegion* GetMapRegion(long lMapIndex);
	int GetMapIndex(long x, long y);
	const TMapRegion* FindRegionByPartialName(const char* szMapName);

	bool GetMapBasePosition(long x, long y, PIXEL_POSITION& r_pos);
	bool GetMapBasePositionByMapIndex(long lMapIndex, PIXEL_POSITION& r_pos);
	bool GetMovablePosition(long lMapIndex, long x, long y, PIXEL_POSITION& pos);
	bool IsMovablePosition(long lMapIndex, long x, long y);
	bool GetCenterPositionOfMap(long lMapIndex, PIXEL_POSITION& r_pos);
	bool GetRandomLocation(long lMapIndex, PIXEL_POSITION& r_pos, DWORD dwCurrentX = 0, DWORD dwCurrentY = 0, int iMaxDistance = 0);

	long CreatePrivateMap(long lMapIndex); // returns new private map index, returns 0 when fail
	void DestroyPrivateMap(long lMapIndex);

	TAreaMap& GetDungeonArea(long lMapIndex);
	void SendNPCPosition(LPCHARACTER ch);
	void InsertNPCPosition(long lMapIndex, BYTE bType, const char* szName, long x, long y);

	BYTE GetEmpireFromMapIndex(long lMapIndex);

	void PurgeMonstersInMap(long lMapIndex);
	void PurgeStonesInMap(long lMapIndex);
	void PurgeNPCsInMap(long lMapIndex);
	size_t GetMonsterCountInMap(long lMapIndex);
	size_t GetMonsterCountInMap(long lMapIndex, DWORD dwVnum);

	/// ������ ���� Sectree �� Attribute �� ���� Ư���� ó���� �����Ѵ�.
	/**
	* @param [in] lMapIndex ������ Map index
	* @param [in] lStartX �簢�� ������ ���� ���� ��ǥ
	* @param [in] lStartY �簢�� ������ ���� ���� ��ǥ
	* @param [in] lEndX �簢�� ������ ���� ������ ��ǥ
	* @param [in] lEndY �簢�� ������ ���� �Ʒ��� ��ǥ
	* @param [in] lRotate ������ ���� ȸ���� ��
	* @param [in] dwAttr ������ Attribute
	* @param [in] mode Attribute �� ���� ó���� type
	**/
	bool ForAttrRegion(long lMapIndex, long lStartX, long lStartY, long lEndX, long lEndY, long lRotate, DWORD dwAttr, EAttrRegionMode mode);

	bool SaveAttributeToImage(int lMapIndex, const char* c_pszFileName, LPSECTREE_MAP pMapSrc = NULL);

	void GetRestartCityPos(int iMapIndex, int iEmpire, int& iTargetX, int& iTargetY, int& iTargetZ);
	void AddRestartCityPos(int iMapIndex = 0, int iEmpire = 0, int iX = 0, int iY = 0, int iZ = 0);


#if defined(__CONQUEROR_LEVEL__)
public:
	struct SSungMaWill { int iSungmaStr, iSungmaHp, iSungmaMove, iSungmaImmune, iHitPct; };

	bool LoadNewWorldMapIndexFile(const char* c_pszListFileName);

	bool IsNewWorldMapIndex(int iMapIndex) const;
	BYTE GetNewWorldSungMa(int iMapIndex, POINT_TYPE wPointType) const;

private:
	using NewWorldMapIndex = std::unordered_map<int, SSungMaWill>;
	NewWorldMapIndex m_map_sNewWorldMap;
#endif

public:
	enum EMapIndexBlockFilterType
	{
		PET_BLOCK_MAP_INDEX,
		MOUNT_BLOCK_MAP_INDEX
	};

	bool LoadBlockFilterMapIndexFile(const BYTE bType);
	bool IsBlockFilterMapIndex(const BYTE bType, int iMapIndex);

private:
	std::unordered_map<int, std::vector<std::pair<int, int>>> m_map_vBlockFilterMapIndex;

private:

	/// ������ �簢�� ������ ���� Sectree �� Attribute �� ���� Ư���� ó���� �����Ѵ�.
	/**
	* @param [in] lMapIndex ������ Map index
	* @param [in] lCX �簢�� ������ ���� ���� Cell �� ��ǥ
	* @param [in] lCY �簢�� ������ ���� ���� Cell �� ��ǥ
	* @param [in] lCW �簢�� ������ Cell ���� ��
	* @param [in] lCH �簢�� ������ Cell ���� ����
	* @param [in] lRotate ȸ���� ��(����)
	* @param [in] dwAttr ������ Attribute
	* @param [in] mode Attribute �� ���� ó���� type
	**/
	bool ForAttrRegionRightAngle(long lMapIndex, long lCX, long lCY, long lCW, long lCH, long lRotate, DWORD dwAttr, EAttrRegionMode mode);

	/// ���� �̿��� �簢�� ������ ���� Sectree �� Attribute �� ���� Ư���� ó���� �����Ѵ�.
	/**
	* @param [in] lMapIndex ������ Map index
	* @param [in] lCX �簢�� ������ ���� ���� Cell �� ��ǥ
	* @param [in] lCY �簢�� ������ ���� ���� Cell �� ��ǥ
	* @param [in] lCW �簢�� ������ Cell ���� ��
	* @param [in] lCH �簢�� ������ Cell ���� ����
	* @param [in] lRotate ȸ���� ��(���� �̿��� ��)
	* @param [in] dwAttr ������ Attribute
	* @param [in] mode Attribute �� ���� ó���� type
	**/
	bool ForAttrRegionFreeAngle(long lMapIndex, long lCX, long lCY, long lCW, long lCH, long lRotate, DWORD dwAttr, EAttrRegionMode mode);

	/// �� Cell �� Attribute �� ���� Ư���� ó���� �����Ѵ�.
	/**
	* @param [in] lMapIndex ������ Map index
	* @param [in] lCX ������ Cell �� X ��ǥ
	* @param [in] lCY ������ Cell �� Y ��ǥ
	* @param [in] dwAttr ������ Attribute
	* @param [in] mode Attribute �� ���� ó���� type
	**/
	bool ForAttrRegionCell(long lMapIndex, long lCX, long lCY, DWORD dwAttr, EAttrRegionMode mode);

	static WORD current_sectree_version;
	std::map<DWORD, LPSECTREE_MAP> m_map_pkSectree;
	pkAreaMap m_map_pkArea;
	std::vector<TMapRegion> m_vec_mapRegion;
	NPCPositionMap m_mapNPCPosition;

	// <Factor> Circular private map indexing
	typedef std::unordered_map<long, int> PrivateIndexMapType;
	PrivateIndexMapType next_private_index_map_;
};

#endif // __INC_SECTREE_MANAGER_H__
