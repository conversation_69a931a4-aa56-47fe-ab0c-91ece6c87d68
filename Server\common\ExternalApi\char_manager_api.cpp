#include "char_manager_api.h"

// Include the actual CHARACTER_MANAGER class header
#include "../../game/src/char_manager.h"
#include "../../game/src/char.h"
#include "../../game/src/typedef.h"
#include <stdexcept>

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// INTERNAL HELPER FUNCTIONS
// ============================================================================

/**
 * @brief Convert CHARACTER_MANAGER pointer to opaque handle
 */
static inline CharacterManagerHandle_t to_manager_handle(CHARACTER_MANAGER* mgr)
{
    return reinterpret_cast<CharacterManagerHandle_t>(mgr);
}

/**
 * @brief Convert opaque handle to CHARACTER_MANAGER pointer
 */
static inline CHARACTER_MANAGER* from_manager_handle(CharacterManagerHandle_t handle)
{
    return reinterpret_cast<CHARACTER_MANAGER*>(handle);
}

/**
 * @brief Convert CHARACTER pointer to opaque handle
 */
static inline CharacterHandle_t to_char_handle(CHARACTER* ch)
{
    return reinterpret_cast<CharacterHandle_t>(ch);
}

/**
 * @brief Convert opaque handle to CHARACTER pointer
 */
static inline CHARACTER* from_char_handle(CharacterHandle_t handle)
{
    return reinterpret_cast<CHARACTER*>(handle);
}

/**
 * @brief Validate manager handle
 */
static inline bool is_valid_manager_handle(CharacterManagerHandle_t handle)
{
    return handle != nullptr && from_manager_handle(handle) != nullptr;
}

/**
 * @brief Validate character handle
 */
static inline bool is_valid_char_handle(CharacterHandle_t handle)
{
    return handle != nullptr && from_char_handle(handle) != nullptr;
}

// ============================================================================
// MANAGER ACCESS
// ============================================================================

CharacterManagerHandle_t char_manager_api_get_instance(void)
{
    try {
        CHARACTER_MANAGER& mgr = CHARACTER_MANAGER::instance();
        return to_manager_handle(&mgr);
    }
    catch (...) {
        return nullptr;
    }
}

// ============================================================================
// CHARACTER CREATION AND DESTRUCTION
// ============================================================================

CharApiResult char_manager_api_create_character(
    CharacterManagerHandle_t manager_handle,
    const char* name,
    DWORD player_id,
    CharacterHandle_t* character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!name || !character_handle)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        LPCHARACTER ch = mgr->CreateCharacter(name, player_id);
        if (!ch) {
            *character_handle = nullptr;
            return CHAR_API_ERROR_INTERNAL;
        }
        
        *character_handle = to_char_handle(ch);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *character_handle = nullptr;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_destroy_character(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!is_valid_char_handle(character_handle))
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);
    
    try {
        mgr->DestroyCharacter(ch);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        return CHAR_API_ERROR_INTERNAL;
    }
}

// ============================================================================
// CHARACTER LOOKUP
// ============================================================================

CharApiResult char_manager_api_find_by_vid(
    CharacterManagerHandle_t manager_handle,
    DWORD vid,
    CharacterHandle_t* character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!character_handle)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        LPCHARACTER ch = mgr->Find(vid);
        *character_handle = ch ? to_char_handle(ch) : nullptr;
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *character_handle = nullptr;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_find_pc_by_name(
    CharacterManagerHandle_t manager_handle,
    const char* name,
    CharacterHandle_t* character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!name || !character_handle)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        LPCHARACTER ch = mgr->FindPC(name);
        *character_handle = ch ? to_char_handle(ch) : nullptr;
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *character_handle = nullptr;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_find_by_pid(
    CharacterManagerHandle_t manager_handle,
    DWORD player_id,
    CharacterHandle_t* character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!character_handle)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        LPCHARACTER ch = mgr->FindByPID(player_id);
        *character_handle = ch ? to_char_handle(ch) : nullptr;
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *character_handle = nullptr;
        return CHAR_API_ERROR_INTERNAL;
    }
}

// ============================================================================
// MOB SPAWNING
// ============================================================================

CharApiResult char_manager_api_spawn_mob(
    CharacterManagerHandle_t manager_handle,
    const SpawnParams* params,
    CharacterHandle_t* character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!params || !character_handle)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        LPCHARACTER ch = mgr->SpawnMob(
            params->vnum,
            params->map_index,
            params->x,
            params->y,
            params->z,
            params->spawn_motion != 0,
            params->rotation,
            params->show != 0
#if defined(__WJ_SHOW_MOB_INFO__)
            , params->aggressive != 0
#endif
        );
        
        *character_handle = ch ? to_char_handle(ch) : nullptr;
        return ch ? CHAR_API_SUCCESS : CHAR_API_ERROR_INTERNAL;
    }
    catch (...) {
        *character_handle = nullptr;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_spawn_mob_range(
    CharacterManagerHandle_t manager_handle,
    DWORD vnum,
    long map_index,
    int start_x,
    int start_y,
    int end_x,
    int end_y,
    int is_exception,
    int spawn_motion,
    int aggressive,
    CharacterHandle_t* character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!character_handle)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        LPCHARACTER ch = mgr->SpawnMobRange(
            vnum,
            map_index,
            start_x,
            start_y,
            end_x,
            end_y,
            is_exception != 0,
            spawn_motion != 0,
            aggressive != 0
        );
        
        *character_handle = ch ? to_char_handle(ch) : nullptr;
        return ch ? CHAR_API_SUCCESS : CHAR_API_ERROR_INTERNAL;
    }
    catch (...) {
        *character_handle = nullptr;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_spawn_mob_random_position(
    CharacterManagerHandle_t manager_handle,
    DWORD vnum,
    long map_index,
    CharacterHandle_t* character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!character_handle)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        LPCHARACTER ch = mgr->SpawnMobRandomPosition(vnum, map_index);
        *character_handle = ch ? to_char_handle(ch) : nullptr;
        return ch ? CHAR_API_SUCCESS : CHAR_API_ERROR_INTERNAL;
    }
    catch (...) {
        *character_handle = nullptr;
        return CHAR_API_ERROR_INTERNAL;
    }
}

// ============================================================================
// CHARACTER ITERATION
// ============================================================================

// Helper class for C callback wrapper
class CallbackWrapper
{
public:
    CharacterCallback callback;
    void* user_data;
    
    CallbackWrapper(CharacterCallback cb, void* data) : callback(cb), user_data(data) {}
    
    void operator()(LPCHARACTER ch)
    {
        if (callback && ch) {
            callback(to_char_handle(ch), user_data);
        }
    }
};

CharApiResult char_manager_api_for_each_pc(
    CharacterManagerHandle_t manager_handle,
    CharacterCallback callback,
    void* user_data)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!callback)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        CallbackWrapper wrapper(callback, user_data);
        mgr->for_each_pc(wrapper);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_get_characters_by_race(
    CharacterManagerHandle_t manager_handle,
    DWORD race_num,
    CharacterCallback callback,
    void* user_data)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!callback)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        CharacterVectorInteractor interactor;
        if (mgr->GetCharactersByRaceNum(race_num, interactor)) {
            for (auto it = interactor.begin(); it != interactor.end(); ++it) {
                if (*it) {
                    callback(to_char_handle(*it), user_data);
                }
            }
        }
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        return CHAR_API_ERROR_INTERNAL;
    }
}

// ============================================================================
// DELAYED SAVE OPERATIONS
// ============================================================================

CharApiResult char_manager_api_delayed_save(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!is_valid_char_handle(character_handle))
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);
    
    try {
        mgr->DelayedSave(ch);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_flush_delayed_save(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* was_saved)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    if (!is_valid_char_handle(character_handle) || !was_saved)
        return CHAR_API_ERROR_INVALID_PARAM;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);
    
    try {
        bool result = mgr->FlushDelayedSave(ch);
        *was_saved = result ? 1 : 0;
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *was_saved = 0;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_process_delayed_save(
    CharacterManagerHandle_t manager_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;
    
    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    
    try {
        mgr->ProcessDelayedSave();
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        return CHAR_API_ERROR_INTERNAL;
    }
}

// ============================================================================
// STATE MANAGEMENT
// ============================================================================

CharApiResult char_manager_api_add_to_state_list(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* was_added)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;

    if (!is_valid_char_handle(character_handle) || !was_added)
        return CHAR_API_ERROR_INVALID_PARAM;

    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);

    try {
        bool result = mgr->AddToStateList(ch);
        *was_added = result ? 1 : 0;
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *was_added = 0;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_remove_from_state_list(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;

    if (!is_valid_char_handle(character_handle))
        return CHAR_API_ERROR_INVALID_PARAM;

    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);

    try {
        mgr->RemoveFromStateList(ch);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        return CHAR_API_ERROR_INTERNAL;
    }
}

// ============================================================================
// RATE MANAGEMENT
// ============================================================================

CharApiResult char_manager_api_get_mob_item_rate(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* rate)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;

    if (!is_valid_char_handle(character_handle) || !rate)
        return CHAR_API_ERROR_INVALID_PARAM;

    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);

    try {
        *rate = mgr->GetMobItemRate(ch);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *rate = 0;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_get_mob_damage_rate(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* rate)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;

    if (!is_valid_char_handle(character_handle) || !rate)
        return CHAR_API_ERROR_INVALID_PARAM;

    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);

    try {
        *rate = mgr->GetMobDamageRate(ch);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *rate = 0;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_get_mob_gold_amount_rate(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* rate)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;

    if (!is_valid_char_handle(character_handle) || !rate)
        return CHAR_API_ERROR_INVALID_PARAM;

    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);

    try {
        *rate = mgr->GetMobGoldAmountRate(ch);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *rate = 0;
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_get_mob_exp_rate(
    CharacterManagerHandle_t manager_handle,
    CharacterHandle_t character_handle,
    int* rate)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;

    if (!is_valid_char_handle(character_handle) || !rate)
        return CHAR_API_ERROR_INVALID_PARAM;

    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);
    CHARACTER* ch = from_char_handle(character_handle);

    try {
        *rate = mgr->GetMobExpRate(ch);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        *rate = 0;
        return CHAR_API_ERROR_INTERNAL;
    }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

CharApiResult char_manager_api_update(
    CharacterManagerHandle_t manager_handle,
    int pulse)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;

    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);

    try {
        mgr->Update(pulse);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        return CHAR_API_ERROR_INTERNAL;
    }
}

CharApiResult char_manager_api_register_race_num(
    CharacterManagerHandle_t manager_handle,
    DWORD race_num)
{
    if (!is_valid_manager_handle(manager_handle))
        return CHAR_API_ERROR_NULL_HANDLE;

    CHARACTER_MANAGER* mgr = from_manager_handle(manager_handle);

    try {
        mgr->RegisterRaceNum(race_num);
        return CHAR_API_SUCCESS;
    }
    catch (...) {
        return CHAR_API_ERROR_INTERNAL;
    }
}

#ifdef __cplusplus
}
#endif
