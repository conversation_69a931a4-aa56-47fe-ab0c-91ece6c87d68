#ifndef __INC_LIBTHECORE_DIRENT_H__
#define __INC_LIBTHECORE_DIRENT_H__

/*
* Declaration of POSIX directory browsing functions and types for Win32.
*
* Author: <PERSON><PERSON><PERSON> (<EMAIL>, <EMAIL>)
* History: Created March 1997. Updated June 2003.
* Rights: See end of file.
*/

#ifdef __cplusplus
extern "C"
{
#endif

	typedef struct DIR DIR;

	struct dirent
	{
		char* d_name;
	};

	DIR* opendir(const char*);
	int closedir(DIR*);
	struct dirent* readdir(DIR*);
	void rewinddir(DIR*);

	/*
	* Copyright Ke<PERSON><PERSON>, 1997, 2003. All rights reserved.
	*
	* Permission to use, copy, modify, and distribute this software and its
	* documentation for any purpose is hereby granted without fee, provided
	* that this copyright and permissions notice appear in all copies and
	* derivatives.
	*
	* This software is supplied "as is" without express or implied warranty.
	*
	* But that said, if there are any problems please get in touch.
	*/

#ifdef __cplusplus
}
#endif

#endif // __INC_LIBTHECORE_DIRENT_H__
