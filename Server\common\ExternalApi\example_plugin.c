/**
 * @file example_plugin.c
 * @brief Example plugin demonstrating the External C API usage
 * 
 * This file shows how to use the external C API for character management
 * in a plugin that can be compiled with different compilers (gcc, clang, vs)
 * while maintaining compatibility with the game server.
 * 
 * Compile this as a shared library (.dll on Windows, .so on Linux) and
 * load it through the plugin system.
 */

#include "external_api.h"
#include <stdio.h>
#include <string.h>

// ============================================================================
// PLUGIN CALLBACK FUNCTIONS
// ============================================================================

/**
 * @brief Callback function for iterating over characters
 */
void character_info_callback(CharacterHandle_t character, void* user_data)
{
    const char* prefix = (const char*)user_data;
    
    // Get character information
    char name[CHAR_API_MAX_NAME_LENGTH];
    BYTE level;
    int gold;
    DWORD vid;
    CharPosition pos;
    
    // Retrieve character data
    if (char_api_get_name(character, name, sizeof(name)) == CHAR_API_SUCCESS &&
        char_api_get_level(character, &level) == CHAR_API_SUCCESS &&
        char_api_get_gold(character, &gold) == CHAR_API_SUCCESS &&
        char_api_get_vid(character, &vid) == CHAR_API_SUCCESS &&
        char_api_get_position(character, &pos) == CHAR_API_SUCCESS) {
        
        printf("%s Character: %s (VID: %u, Level: %d, Gold: %d, Pos: %ld,%ld,%ld)\n",
               prefix, name, vid, level, gold, pos.x, pos.y, pos.z);
    }
}

/**
 * @brief Callback function for finding high-level characters
 */
void high_level_character_callback(CharacterHandle_t character, void* user_data)
{
    int* min_level = (int*)user_data;
    
    BYTE level;
    if (char_api_get_level(character, &level) == CHAR_API_SUCCESS) {
        if (level >= *min_level) {
            char name[CHAR_API_MAX_NAME_LENGTH];
            if (char_api_get_name(character, name, sizeof(name)) == CHAR_API_SUCCESS) {
                printf("High-level character found: %s (Level %d)\n", name, level);
                
                // Send a congratulatory message
                char message[CHAR_API_MAX_MESSAGE_LENGTH];
                snprintf(message, sizeof(message), 
                        "Congratulations on reaching level %d!", level);
                char_api_chat_packet(character, message);
            }
        }
    }
}

// ============================================================================
// PLUGIN MAIN FUNCTIONS
// ============================================================================

/**
 * @brief Demonstrate basic character lookup and manipulation
 */
void demo_character_operations(void)
{
    printf("\n=== Character Operations Demo ===\n");
    
    // Get character manager instance
    CharacterManagerHandle_t mgr = char_manager_api_get_instance();
    if (!mgr) {
        printf("ERROR: Failed to get character manager instance\n");
        return;
    }
    
    // Example 1: Find a character by name (replace with actual player name)
    CharacterHandle_t character = NULL;
    CharApiResult result = char_manager_api_find_pc_by_name(mgr, "TestPlayer", &character);
    
    if (result == CHAR_API_SUCCESS && character) {
        printf("Found character TestPlayer\n");
        
        // Get and display character stats
        CharStats stats;
        if (char_api_get_stats(character, &stats) == CHAR_API_SUCCESS) {
            printf("  Level: %d, HP: %d/%d, SP: %d/%d, Gold: %d\n",
                   stats.level, stats.hp, stats.max_hp, 
                   stats.sp, stats.max_sp, stats.gold);
        }
        
        // Give the character some gold
        int current_gold;
        if (char_api_get_gold(character, &current_gold) == CHAR_API_SUCCESS) {
            char_api_set_gold(character, current_gold + 1000);
            printf("  Gave 1000 gold to character (new total: %d)\n", current_gold + 1000);
        }
        
        // Send a welcome message
        char_api_chat_packet(character, "Welcome! You received 1000 gold from the example plugin!");
    } else {
        printf("Character 'TestPlayer' not found or error occurred\n");
    }
    
    // Example 2: Create a test NPC
    CharacterHandle_t npc = NULL;
    result = char_manager_api_create_character(mgr, "TestNPC", 0, &npc);
    
    if (result == CHAR_API_SUCCESS && npc) {
        printf("Created test NPC\n");
        
        // Set some basic stats for the NPC
        char_api_set_hp(npc, 1000);
        char_api_set_point(npc, POINT_LEVEL, 50);
        
        printf("  Set NPC HP to 1000 and level to 50\n");
        
        // Clean up - destroy the test NPC
        char_manager_api_destroy_character(mgr, npc);
        printf("  Destroyed test NPC\n");
    } else {
        printf("Failed to create test NPC\n");
    }
}

/**
 * @brief Demonstrate character iteration
 */
void demo_character_iteration(void)
{
    printf("\n=== Character Iteration Demo ===\n");
    
    CharacterManagerHandle_t mgr = char_manager_api_get_instance();
    if (!mgr) {
        printf("ERROR: Failed to get character manager instance\n");
        return;
    }
    
    // Iterate over all PC characters
    printf("All PC characters:\n");
    const char* prefix = "  ";
    char_manager_api_for_each_pc(mgr, character_info_callback, (void*)prefix);
    
    // Find high-level characters (level 80+)
    printf("\nHigh-level characters (80+):\n");
    int min_level = 80;
    char_manager_api_for_each_pc(mgr, high_level_character_callback, &min_level);
}

/**
 * @brief Demonstrate mob spawning
 */
void demo_mob_spawning(void)
{
    printf("\n=== Mob Spawning Demo ===\n");
    
    CharacterManagerHandle_t mgr = char_manager_api_get_instance();
    if (!mgr) {
        printf("ERROR: Failed to get character manager instance\n");
        return;
    }
    
    // Spawn a mob at specific coordinates
    SpawnParams params = {0};
    params.vnum = 101;  // Example mob vnum (replace with actual)
    params.map_index = 1;  // Example map index
    params.x = 100000;
    params.y = 100000;
    params.z = 0;
    params.rotation = -1;
    params.spawn_motion = 0;
    params.show = 1;
    params.aggressive = 0;
    
    CharacterHandle_t mob = NULL;
    CharApiResult result = char_manager_api_spawn_mob(mgr, &params, &mob);
    
    if (result == CHAR_API_SUCCESS && mob) {
        printf("Successfully spawned mob (vnum: %u) at position %ld,%ld\n", 
               params.vnum, params.x, params.y);
        
        // Get mob information
        WORD race_num;
        if (char_api_get_race_num(mob, &race_num) == CHAR_API_SUCCESS) {
            printf("  Mob race number: %u\n", race_num);
        }
        
        // Check if it's a monster
        int is_monster;
        if (char_api_is_monster(mob, &is_monster) == CHAR_API_SUCCESS) {
            printf("  Is monster: %s\n", is_monster ? "Yes" : "No");
        }
    } else {
        printf("Failed to spawn mob (error: %s)\n", char_api_result_to_string(result));
    }
    
    // Spawn a mob at random position
    CharacterHandle_t random_mob = NULL;
    result = char_manager_api_spawn_mob_random_position(mgr, 101, 1, &random_mob);
    
    if (result == CHAR_API_SUCCESS && random_mob) {
        CharPosition pos;
        if (char_api_get_position(random_mob, &pos) == CHAR_API_SUCCESS) {
            printf("Spawned mob at random position: %ld,%ld,%ld\n", pos.x, pos.y, pos.z);
        }
    }
}

/**
 * @brief Demonstrate error handling
 */
void demo_error_handling(void)
{
    printf("\n=== Error Handling Demo ===\n");
    
    // Try to use invalid handles
    CharApiResult result = char_api_get_name(NULL, NULL, 0);
    printf("Calling char_api_get_name with NULL handle: %s\n", 
           char_api_result_to_string(result));
    
    // Try to find non-existent character
    CharacterManagerHandle_t mgr = char_manager_api_get_instance();
    if (mgr) {
        CharacterHandle_t character = NULL;
        result = char_manager_api_find_pc_by_name(mgr, "NonExistentPlayer", &character);
        printf("Looking for non-existent player: %s (handle: %p)\n", 
               char_api_result_to_string(result), character);
    }
    
    // Check last error
    const char* last_error = char_api_get_last_error();
    if (last_error) {
        printf("Last error message: %s\n", last_error);
        char_api_clear_last_error();
    }
}

/**
 * @brief Main plugin entry point
 */
int plugin_main(void)
{
    printf("=== External C API Example Plugin ===\n");
    
    // Initialize the API
    CharApiResult result = char_api_initialize();
    if (result != CHAR_API_SUCCESS) {
        printf("ERROR: Failed to initialize API: %s\n", char_api_result_to_string(result));
        return -1;
    }
    
    // Enable debug mode for demonstration
    char_api_set_debug_mode(1);
    
    // Get and display API version
    int major, minor, patch;
    char_api_get_version(&major, &minor, &patch);
    printf("API Version: %d.%d.%d\n", major, minor, patch);
    
    // Check compatibility
    if (char_api_is_compatible(1, 0, 0)) {
        printf("API is compatible with version 1.0.0\n");
    }
    
    // Run demonstrations
    demo_character_operations();
    demo_character_iteration();
    demo_mob_spawning();
    demo_error_handling();
    
    // Get API statistics
    unsigned long total_calls, successful_calls, failed_calls;
    char_api_get_statistics(&total_calls, &successful_calls, &failed_calls);
    printf("\nAPI Statistics:\n");
    printf("  Total calls: %lu\n", total_calls);
    printf("  Successful calls: %lu\n", successful_calls);
    printf("  Failed calls: %lu\n", failed_calls);
    
    // Cleanup
    char_api_cleanup();
    printf("\nPlugin execution completed.\n");
    
    return 0;
}

// ============================================================================
// PLUGIN EXPORT FUNCTIONS (for dynamic loading)
// ============================================================================

#ifdef _WIN32
__declspec(dllexport)
#endif
int plugin_initialize(void)
{
    return plugin_main();
}

#ifdef _WIN32
__declspec(dllexport)
#endif
void plugin_cleanup(void)
{
    char_api_cleanup();
}

#ifdef _WIN32
__declspec(dllexport)
#endif
const char* plugin_get_name(void)
{
    return "External API Example Plugin";
}

#ifdef _WIN32
__declspec(dllexport)
#endif
const char* plugin_get_version(void)
{
    return "1.0.0";
}
