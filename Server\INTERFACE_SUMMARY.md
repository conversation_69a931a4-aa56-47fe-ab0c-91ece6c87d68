# Plugin System Interface Summary

This document provides a comprehensive overview of all the pure virtual interfaces created for the plugin system. These interfaces provide ABI-stable access to game systems without exposing internal implementation details.

## Core Entity Interfaces

### IEntity (Server/common/interfaces/IEntity.h)
- Base interface for all game entities
- Provides VID management, position tracking, and basic entity operations
- Used by characters, items, and other game objects

### ICFSM (Server/common/interfaces/ICFSM.h)
- Interface for Finite State Machine functionality
- Handles state transitions and updates for AI and character behavior

### IHorse (Server/common/interfaces/IHorse.h)
- Interface for horse/mount system
- Manages horse stats, skills, and rider interactions

## Character System Interfaces

### ICHARACTER (Server/common/interfaces/IChar.h)
- Core character interface for players and NPCs
- Comprehensive character management including:
  - Basic properties (level, HP, SP, stats)
  - Position and movement
  - Items and inventory
  - Skills and combat
  - Party and guild membership
  - Quest and affect systems

### ICharacterManager (Server/common/interfaces/ICharManager.h)
- Manager interface for character operations
- Character lookup, creation, and lifecycle management
- Player statistics and character enumeration

## Item System Interfaces

### IITEM (Server/common/interfaces/IItem.h)
- Interface for all game items
- Item properties, attributes, sockets, and operations
- Item usage, enhancement, and validation

### IItemManager (Server/common/interfaces/IItemManager.h)
- Manager interface for item operations
- Item creation, lookup, and lifecycle management
- Item table access and statistics

## Guild System Interfaces

### IGUILD (Server/common/interfaces/IGuild.h)
- Interface for guild entities
- Guild management including:
  - Member management and leadership
  - Guild communication and messaging
  - Guild skills and finances
  - Guild wars and land management
  - Guild marks and statistics

### IGuildManager (Server/common/interfaces/IGuildManager.h)
- Manager interface for guild operations
- Guild creation, lookup, and management
- Guild war coordination and statistics

## Shop System Interfaces

### ISHOP (Server/common/interfaces/IShop.h)
- Interface for shop entities (NPC and player shops)
- Shop operations including:
  - Item management and pricing
  - Buy/sell operations and validation
  - Shop configuration and restrictions
  - Shop communication and updates

### IShopManager (Server/common/interfaces/IShopManager.h)
- Manager interface for shop operations
- Shop creation, lookup, and management
- Shopping session coordination

## Party System Interfaces

### IPARTY (Server/common/interfaces/IParty.h)
- Interface for party entities
- Party management including:
  - Member management and leadership
  - Party communication and settings
  - Experience and item distribution
  - Dungeon support and positioning

### IPartyManager (Server/common/interfaces/IPartyManager.h)
- Manager interface for party operations
- Party creation, lookup, and management
- Party invitation and coordination

## System Manager Interfaces

### IDescManager (Server/common/interfaces/IDescManager.h)
- Interface for connection/descriptor management
- Client connection tracking and communication
- Global messaging and disconnection handling

### IBufferManager (Server/common/interfaces/IBufferManager.h)
- Interface for memory buffer management
- Buffer allocation, pools, and optimization
- Memory statistics and leak detection

### ISectreeManager (Server/common/interfaces/ISectreeManager.h)
- Interface for spatial partitioning system
- Map management and position validation
- Entity tracking and area operations
- Pathfinding and spawn position support

### IMobManager (Server/common/interfaces/IMobManager.h)
- Interface for mob/monster data management
- Mob table access and statistics
- Mob behavior and validation
- Regen tracking and group management

### ISkillManager (Server/common/interfaces/ISkillManager.h)
- Interface for skill system management
- Skill table access and properties
- Skill usage, learning, and validation
- Skill effects and damage calculation

## Class Inheritance Updates

The following classes have been updated to inherit from their respective interfaces:

### Game Server Classes
- `CHARACTER_MANAGER` → inherits from `ICharacterManager`
- `ITEM_MANAGER` → inherits from `IItemManager`
- `DESC_MANAGER` → inherits from `IDescManager`
- `CShopManager` → inherits from `IShopManager`
- `CGuildManager` → inherits from `IGuildManager`
- `CPartyManager` → inherits from `IPartyManager`
- `SECTREE_MANAGER` → inherits from `ISectreeManager`
- `CMobManager` → inherits from `IMobManager`

### Entity Classes
- `CItem` → inherits from `IITEM`
- `CGuild` → inherits from `IGUILD`
- `CShop` → inherits from `ISHOP`
- `CParty` → inherits from `IPARTY`

## Plugin Integration

These interfaces are designed to be used by the plugin system to provide:

1. **ABI Stability**: Pure virtual interfaces ensure stable vtable layout across different compilers
2. **Cross-Compiler Compatibility**: Game server (clang) and plugins (gcc) can safely interact
3. **Encapsulation**: Internal implementation details are hidden from plugins
4. **Extensibility**: New functionality can be added without breaking existing plugins

## Usage in Plugins

Plugins can access these interfaces through the plugin manager:

```cpp
// Example plugin usage
ICharacterManager* charMgr = GetCharacterManager();
ICHARACTER* player = charMgr->FindPC("PlayerName");
if (player) {
    player->ChatPacket(CHAT_TYPE_INFO, "Hello from plugin!");
}
```

## Compilation Status

All interfaces and updated classes compile successfully without errors. The interface system is ready for plugin integration and provides comprehensive access to all major game systems.
