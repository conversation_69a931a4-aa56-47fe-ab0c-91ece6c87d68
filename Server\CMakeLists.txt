# Server CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# Server is primarily for Unix/Linux, but can be built on Windows with appropriate tools
if(WIN32)
    message(STATUS "Building server on Windows - ensure you have appropriate Unix-like tools")
endif()

# Server-specific compiler settings
if(NOT WIN32)
    # Unix/Linux settings
    set(CMAKE_C_COMPILER clang)
    set(CMAKE_CXX_COMPILER clang++)
    
    add_compile_options(-m32 -Wall -O2 -pipe -g)
    add_compile_options(-Wno-invalid-source-encoding -Wno-deprecated)
    set(CMAKE_CXX_STANDARD 17)
    set(CMAKE_C_STANDARD 11)
else()
    # Windows settings for server (if building with MSYS2/MinGW)
    set(CMAKE_CXX_STANDARD 17)
    set(CMAKE_C_STANDARD 11)
endif()

# Common function to create server library
function(create_server_library target_name)
    cmake_parse_arguments(LIB "" "TYPE" "SOURCES;HEADERS;DEPENDENCIES;INCLUDE_DIRS" ${ARGN})
    
    if(NOT LIB_TYPE)
        set(LIB_TYPE STATIC)
    endif()
    
    # Create library
    add_library(${target_name} ${LIB_TYPE} ${LIB_SOURCES} ${LIB_HEADERS})
    
    # Set target properties
    set_target_properties(${target_name} PROPERTIES
        CXX_STANDARD 17
        C_STANDARD 11
        FOLDER "Server/Libraries"
    )
    
    # Include directories
    target_include_directories(${target_name} 
        PUBLIC 
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_CURRENT_SOURCE_DIR}/include
            ${LIB_INCLUDE_DIRS}
        PRIVATE
            ${CMAKE_SOURCE_DIR}/Server/common
    )
    
    # Link with dependencies
    if(LIB_DEPENDENCIES)
        target_link_libraries(${target_name} PUBLIC ${LIB_DEPENDENCIES})
    endif()
    
    # Apply server-specific compiler settings
    if(NOT WIN32)
        target_compile_options(${target_name} PRIVATE
            -m32 -Wall -O2 -pipe -g
            -Wno-invalid-source-encoding -Wno-deprecated
        )
        target_link_options(${target_name} PRIVATE -m32)

        target_compile_definitions(${target_name} PRIVATE
            __UNIX__
            _GNU_SOURCE
        )
    else()
        # Apply ProjectZ compiler settings for Windows builds
        apply_projectz_compiler_settings(${target_name})

        # Additional server-specific Windows definitions
        target_compile_definitions(${target_name} PRIVATE
            _WINSOCK_DEPRECATED_NO_WARNINGS
            _USE_32BIT_TIME_T
        )
    endif()
    
    # Create alias
    add_library(Server::${target_name} ALIAS ${target_name})
endfunction()

# Find required packages for server
find_package(Threads REQUIRED)

# MySQL/MariaDB
if(NOT WIN32)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(MYSQL REQUIRED mysqlclient)
else()
    # On Windows, use the bundled MySQL libraries
    set(MYSQL_INCLUDE_DIRS ${CMAKE_SOURCE_DIR}/External/MySQL/6.0.2/win32/include)
    set(MYSQL_LIBRARY_DIRS ${CMAKE_SOURCE_DIR}/External/MySQL/6.0.2/win32/lib)
    set(MYSQL_LIBRARIES mysqlclient)
endif()

# OpenSSL
find_package(OpenSSL REQUIRED)

# Build order based on dependencies (matching Makefile)
add_subdirectory(libthecore)
add_subdirectory(libpoly)
add_subdirectory(libgame)
add_subdirectory(liblua)
add_subdirectory(libsql)
add_subdirectory(libserverkey)

# Server executables
add_subdirectory(db)
add_subdirectory(game)

# Create convenience targets matching the Makefile
add_custom_target(ServerLibraries
    DEPENDS 
        libthecore
        libpoly
        libgame
        liblua
        libsql
        libserverkey
)

add_custom_target(ServerExecutables
    DEPENDS 
        db
        game
)

add_custom_target(Server
    DEPENDS 
        ServerLibraries
        ServerExecutables
)
