#include "stdafx.h"
#include "game_plugin_manager.h"
#include "plugin_loader.h"
#include <algorithm>
#include <arena.h>
#include <char_manager.h>
#include <db.h>
#include <desc_manager.h>
#include <item_manager.h>
#include <party.h>
#include <pvp.h>
#include <regen.h>
#include <shop_manager.h>
#include <guild_manager.h>

GamePluginManager::GamePluginManager()
	     : m_bInitialized(true)
{
    printf("[GamePluginManager] Constructor called\n");
    SetPluginDirectory("plugins/game/");
    printf("[GamePluginManager] SetPluginDirectory completed\n");
    sys_log(0, "GamePluginManager initialized");
    printf("[GamePluginManager] Constructor completed\n");
}

GamePluginManager::~GamePluginManager()
{
    UnloadAllPlugins();
}

bool GamePluginManager::LoadPluginsFromDirectory(const std::string& directory)
{
    sys_log(0, "Loading Game plugins from directory: %s", directory.c_str());

    // Use the base class implementation to scan and load plugins
    bool success = PluginManager::LoadPluginsFromDirectory(directory);

    if (success)
    {
        sys_log(0, "Successfully loaded Game plugins from directory: %s", directory.c_str());
    }
    else
    {
        sys_err("Failed to load some Game plugins from directory: %s", directory.c_str());
    }

    return success;
}

bool GamePluginManager::LoadPluginLibrary(const std::string& filePath, PluginHandle& handle)
{
#ifdef _WIN32
    // Windows implementation
    handle.libraryHandle = static_cast<PluginHandle_t>(::LoadLibraryA(filePath.c_str()));
    if (!handle.libraryHandle)
    {
        sys_err("Failed to load library: %s", filePath.c_str());
        return false;
    }

    // Look for game-specific factory functions
    typedef IGamePlugin* (*CreateGamePluginFunc)();
    typedef void (*DestroyGamePluginFunc)(IGamePlugin*);

    CreateGamePluginFunc createGameFunc = (CreateGamePluginFunc)::GetProcAddress((HMODULE)handle.libraryHandle, "CreateGamePlugin");
    DestroyGamePluginFunc destroyGameFunc = (DestroyGamePluginFunc)::GetProcAddress((HMODULE)handle.libraryHandle, "DestroyGamePlugin");

    if (!createGameFunc || !destroyGameFunc)
    {
        sys_err("Plugin %s does not export required game plugin functions", filePath.c_str());
        ::FreeLibrary((HMODULE)handle.libraryHandle);
        handle.libraryHandle = nullptr;
        return false;
    }

    // Create the game plugin instance
    IGamePlugin* gamePlugin = createGameFunc();
    if (!gamePlugin)
    {
        sys_err("Failed to create game plugin instance from %s", filePath.c_str());
        ::FreeLibrary((HMODULE)handle.libraryHandle);
        handle.libraryHandle = nullptr;
        return false;
    }

    // Cast to IPlugin for the base class
    handle.plugin = std::shared_ptr<IPlugin>(static_cast<IPlugin*>(gamePlugin));

    // Store the factory functions (cast to generic types for compatibility)
    handle.createFunc = (CreatePluginFunc)createGameFunc;
    handle.destroyFunc = (DestroyPluginFunc)destroyGameFunc;

#else
    // Unix/Linux implementation
    handle.libraryHandle = dlopen(filePath.c_str(), RTLD_LAZY);
    if (!handle.libraryHandle)
    {
        sys_err("Failed to load library: %s - %s", filePath.c_str(), dlerror());
        return false;
    }

    // Look for game-specific factory functions
    typedef IGamePlugin* (*CreateGamePluginFunc)();
    typedef void (*DestroyGamePluginFunc)(IGamePlugin*);

    CreateGamePluginFunc createGameFunc = (CreateGamePluginFunc)dlsym(handle.libraryHandle, "CreateGamePlugin");
    DestroyGamePluginFunc destroyGameFunc = (DestroyGamePluginFunc)dlsym(handle.libraryHandle, "DestroyGamePlugin");

    if (!createGameFunc || !destroyGameFunc)
    {
        sys_err("Plugin %s does not export required game plugin functions", filePath.c_str());
        dlclose(handle.libraryHandle);
        handle.libraryHandle = nullptr;
        return false;
    }

    // Create the game plugin instance
    IGamePlugin* gamePlugin = createGameFunc();
    if (!gamePlugin)
    {
        sys_err("Failed to create game plugin instance from %s", filePath.c_str());
        dlclose(handle.libraryHandle);
        handle.libraryHandle = nullptr;
        return false;
    }

    // Cast to IPlugin for the base class
    handle.plugin = std::shared_ptr<IPlugin>(static_cast<IPlugin*>(gamePlugin));

    // Store the factory functions (cast to generic types for compatibility)
    handle.createFunc = (CreatePluginFunc)createGameFunc;
    handle.destroyFunc = (DestroyPluginFunc)destroyGameFunc;
#endif

    if (handle.info) {
        handle.info->filePath = filePath;
    }
    handle.isLoaded = true;

    sys_log(0, "Successfully loaded game plugin: %s", filePath.c_str());
    return true;
}

bool GamePluginManager::RegisterPlugin(IPlugin* plugin)
{
    if (!plugin)
        return false;

    // Try to cast to IGamePlugin
    IGamePlugin* gamePlugin = dynamic_cast<IGamePlugin*>(plugin);
    if (!gamePlugin)
        return false;  // Not a game plugin

    // Check if plugin is already registered

    auto it = std::find(m_gamePlugins.begin(), m_gamePlugins.end(), gamePlugin);
    if (it != m_gamePlugins.end())
    {
        sys_log(0, "[GamePluginManager] Plugin already registered: %s", gamePlugin->GetInfo().name.c_str());
        return false;
    }

    // Add to our list of registered game plugins
    m_gamePlugins.push_back(gamePlugin);
    sys_log(0, "[GamePluginManager] Successfully registered game plugin: %s", gamePlugin->GetInfo().name.c_str());

    return true;
}

bool GamePluginManager::UnregisterPlugin(IPlugin* plugin)
{
    if (!plugin)
        return false;

    // Try to cast to IGamePlugin
    IGamePlugin* gamePlugin = dynamic_cast<IGamePlugin*>(plugin);
    if (!gamePlugin)
        return false;  // Not a game plugin

    // Find and remove from our list of registered game plugins
    auto it = std::find(m_gamePlugins.begin(), m_gamePlugins.end(), gamePlugin);
    if (it == m_gamePlugins.end())
    {
        sys_log(0, "[GamePluginManager] Plugin not found for unregistration: %s", gamePlugin->GetInfo().name.c_str());
        return false;
    }

    // Remove from the list
    m_gamePlugins.erase(it);
    sys_log(0, "[GamePluginManager] Successfully unregistered game plugin: %s", gamePlugin->GetInfo().name.c_str());

    return true;
}

bool GamePluginManager::LoadGamePlugin(const std::string& filePath)
{
    if (!LoadPlugin(filePath))
    {
        return false;
    }
    
    // Get the plugin name from the file path
    std::string pluginName = filePath.substr(filePath.find_last_of("/\\") + 1);
    if (pluginName.find('.') != std::string::npos)
    {
        pluginName = pluginName.substr(0, pluginName.find_last_of('.'));
    }
    
    auto basePluginPtr = GetPlugin(pluginName);
    if (!basePluginPtr)
    {
        return false;
    }
    
    // Try to cast to game plugin
    IGamePlugin* gamePlugin = dynamic_cast<IGamePlugin*>(basePluginPtr.get());
    if (!gamePlugin)
    {
        sys_err("Plugin %s is not a game plugin", pluginName.c_str());
        UnloadPlugin(pluginName);
        return false;
    }
    
    if (!ValidateGamePlugin(gamePlugin))
    {
        sys_err("Game plugin %s failed validation", pluginName.c_str());
        UnloadPlugin(pluginName);
        return false;
    }
    
    // Add to game plugins list
    m_gamePlugins.push_back(gamePlugin);
    
    sys_log(0, "Game plugin %s loaded successfully", pluginName.c_str());
    return true;
}

std::shared_ptr<IPlugin> GamePluginManager::GetGamePlugin(const std::string& pluginName)
{
    auto basePluginPtr = GetPlugin(pluginName);
    if (basePluginPtr)
    {
        return basePluginPtr;
    }
    return nullptr;
}

std::vector<IGamePlugin*> GamePluginManager::GetAllGamePlugins()
{
    return m_gamePlugins;
}
std::vector<std::string> GamePluginManager::GetAllPluginsNames()
{
    return GetLoadedPluginNames();
}

void GamePluginManager::BroadcastCharacterCreate(ICHARACTER* ch)
{
    BroadcastToGamePlugins([ch](IGamePlugin* plugin) {
        plugin->OnCharacterCreate(ch);
    });
}

void GamePluginManager::BroadcastCharacterDestroy(ICHARACTER* ch)
{
    BroadcastToGamePlugins([ch](IGamePlugin* plugin) {
        plugin->OnCharacterDestroy(ch);
    });
}

void GamePluginManager::BroadcastCharacterLogin(ICHARACTER* ch)
{
    BroadcastToGamePlugins([ch](IGamePlugin* plugin) {
        plugin->OnCharacterLogin(ch);
    });
}

void GamePluginManager::BroadcastCharacterLogout(ICHARACTER* ch)
{
    BroadcastToGamePlugins([ch](IGamePlugin* plugin) {
        plugin->OnCharacterLogout(ch);
    });
}

void GamePluginManager::BroadcastCharacterLevelUp(ICHARACTER* ch, BYTE newLevel)
{
    BroadcastToGamePlugins([ch, newLevel](IGamePlugin* plugin) {
        plugin->OnCharacterLevelUp(ch, newLevel);
    });
}

void GamePluginManager::BroadcastCharacterDead(ICHARACTER* ch, ICHARACTER* killer)
{
    BroadcastToGamePlugins([ch, killer](IGamePlugin* plugin) {
        plugin->OnCharacterDead(ch, killer);
    });
}

void GamePluginManager::BroadcastCharacterRevive(ICHARACTER* ch)
{
    BroadcastToGamePlugins([ch](IGamePlugin* plugin) {
        plugin->OnCharacterRevive(ch);
    });
}

void GamePluginManager::BroadcastItemCreate(IITEM* item)
{
    BroadcastToGamePlugins([item](IGamePlugin* plugin) {
        plugin->OnItemCreate(item);
    });
}

void GamePluginManager::BroadcastItemDestroy(IITEM* item)
{
    BroadcastToGamePlugins([item](IGamePlugin* plugin) {
        plugin->OnItemDestroy(item);
    });
}

void GamePluginManager::BroadcastItemEquip(ICHARACTER* ch, IITEM* item)
{
    BroadcastToGamePlugins([ch, item](IGamePlugin* plugin) {
        plugin->OnItemEquip(ch, item);
    });
}

void GamePluginManager::BroadcastItemUnequip(ICHARACTER* ch, IITEM* item)
{
    BroadcastToGamePlugins([ch, item](IGamePlugin* plugin) {
        plugin->OnItemUnequip(ch, item);
    });
}

void GamePluginManager::BroadcastItemUse(ICHARACTER* ch, IITEM* item)
{
    BroadcastToGamePlugins([ch, item](IGamePlugin* plugin) {
        plugin->OnItemUse(ch, item);
    });
}

void GamePluginManager::BroadcastItemDrop(ICHARACTER* ch, IITEM* item)
{
    BroadcastToGamePlugins([ch, item](IGamePlugin* plugin) {
        plugin->OnItemDrop(ch, item);
    });
}

void GamePluginManager::BroadcastItemPickup(ICHARACTER* ch, IITEM* item)
{
    BroadcastToGamePlugins([ch, item](IGamePlugin* plugin) {
        plugin->OnItemPickup(ch, item);
    });
}

void GamePluginManager::BroadcastAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage)
{
    BroadcastToGamePlugins([attacker, victim, damage](IGamePlugin* plugin) {
        plugin->OnAttack(attacker, victim, damage);
    });
}

void GamePluginManager::BroadcastKill(ICHARACTER* killer, ICHARACTER* victim)
{
    BroadcastToGamePlugins([killer, victim](IGamePlugin* plugin) {
        plugin->OnKill(killer, victim);
    });
}

void GamePluginManager::BroadcastDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage)
{
    BroadcastToGamePlugins([victim, attacker, damage](IGamePlugin* plugin) {
        plugin->OnDamage(victim, attacker, damage);
    });
}

// Combat event aliases (for compatibility)
void GamePluginManager::BroadcastCombatAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage)
{
    BroadcastAttack(attacker, victim, damage);
}

void GamePluginManager::BroadcastCombatDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage)
{
    BroadcastDamage(victim, attacker, damage);
}

void GamePluginManager::BroadcastCombatKill(ICHARACTER* killer, ICHARACTER* victim)
{
    BroadcastKill(killer, victim);
}

void GamePluginManager::BroadcastGuildCreate(IGUILD* guild)
{
    BroadcastToGamePlugins([guild](IGamePlugin* plugin) {
        plugin->OnGuildCreate(guild);
    });
}

void GamePluginManager::BroadcastGuildDestroy(IGUILD* guild)
{
    BroadcastToGamePlugins([guild](IGamePlugin* plugin) {
        plugin->OnGuildDestroy(guild);
    });
}

void GamePluginManager::BroadcastGuildJoin(ICHARACTER* ch, IGUILD* guild)
{
    BroadcastToGamePlugins([ch, guild](IGamePlugin* plugin) {
        plugin->OnGuildJoin(ch, guild);
    });
}

void GamePluginManager::BroadcastGuildLeave(ICHARACTER* ch, IGUILD* guild)
{
    BroadcastToGamePlugins([ch, guild](IGamePlugin* plugin) {
        plugin->OnGuildLeave(ch, guild);
    });
}

void GamePluginManager::BroadcastGuildWar(IGUILD* guild1, IGUILD* guild2)
{
    BroadcastToGamePlugins([guild1, guild2](IGamePlugin* plugin) {
        plugin->OnGuildWar(guild1, guild2);
    });
}

void GamePluginManager::BroadcastShopBuy(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count)
{
    BroadcastToGamePlugins([ch, shop, item, count](IGamePlugin* plugin) {
        plugin->OnShopBuy(ch, shop, item, count);
    });
}

void GamePluginManager::BroadcastShopSell(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count)
{
    BroadcastToGamePlugins([ch, shop, item, count](IGamePlugin* plugin) {
        plugin->OnShopSell(ch, shop, item, count);
    });
}

void GamePluginManager::BroadcastChat(ICHARACTER* ch, const char* message, int type)
{
    BroadcastToGamePlugins([ch, message, type](IGamePlugin* plugin) {
        plugin->OnChat(ch, message, type);
    });
}

void GamePluginManager::BroadcastWhisper(ICHARACTER* from, ICHARACTER* to, const char* message)
{
    BroadcastToGamePlugins([from, to, message](IGamePlugin* plugin) {
        plugin->OnWhisper(from, to, message);
    });
}

void GamePluginManager::BroadcastShout(ICHARACTER* ch, const char* message)
{
    BroadcastToGamePlugins([ch, message](IGamePlugin* plugin) {
        plugin->OnShout(ch, message);
    });
}

bool GamePluginManager::BroadcastCommand(ICHARACTER* ch, const char* command, const char* args)
{
    bool handled = false;
    BroadcastToGamePlugins([ch, command, args, &handled](IGamePlugin* plugin) {
        if (plugin->OnCommand(ch, command, args))
        {
            handled = true;
        }
    });
    return handled;
}

void GamePluginManager::BroadcastMapEnter(ICHARACTER* ch, long mapIndex)
{
    BroadcastToGamePlugins([ch, mapIndex](IGamePlugin* plugin) {
        plugin->OnMapEnter(ch, mapIndex);
    });
}

void GamePluginManager::BroadcastMapLeave(ICHARACTER* ch, long mapIndex)
{
    BroadcastToGamePlugins([ch, mapIndex](IGamePlugin* plugin) {
        plugin->OnMapLeave(ch, mapIndex);
    });
}

void GamePluginManager::BroadcastQuestStart(ICHARACTER* ch, int questIndex)
{
    BroadcastToGamePlugins([ch, questIndex](IGamePlugin* plugin) {
        plugin->OnQuestStart(ch, questIndex);
    });
}

void GamePluginManager::BroadcastQuestComplete(ICHARACTER* ch, int questIndex)
{
    BroadcastToGamePlugins([ch, questIndex](IGamePlugin* plugin) {
        plugin->OnQuestComplete(ch, questIndex);
    });
}

void GamePluginManager::BroadcastQuestGiveUp(ICHARACTER* ch, int questIndex)
{
    BroadcastToGamePlugins([ch, questIndex](IGamePlugin* plugin) {
        plugin->OnQuestGiveUp(ch, questIndex);
    });
}

void GamePluginManager::BroadcastServerStart()
{
    BroadcastToGamePlugins([](IGamePlugin* plugin) {
        plugin->OnServerStart();
    });
}

void GamePluginManager::BroadcastServerShutdown()
{
    BroadcastToGamePlugins([](IGamePlugin* plugin) {
        plugin->OnServerShutdown();
    });
}

void GamePluginManager::BroadcastHeartbeat()
{
    BroadcastToGamePlugins([](IGamePlugin* plugin) {
        plugin->OnHeartbeat();
    });
}

void GamePluginManager::BroadcastMinuteUpdate()
{
    BroadcastToGamePlugins([](IGamePlugin* plugin) {
        plugin->OnMinuteUpdate();
    });
}

void GamePluginManager::BroadcastHourUpdate()
{
    BroadcastToGamePlugins([](IGamePlugin* plugin) {
        plugin->OnHourUpdate();
    });
}

void GamePluginManager::BroadcastDayUpdate()
{
    BroadcastToGamePlugins([](IGamePlugin* plugin) {
        plugin->OnDayUpdate();
    });
}
void GamePluginManager::BroadcastRegisterGamePluginManager(GamePluginManager* manager)
{
	BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
		plugin->OnRegisterGamePluginManager(manager);
						   });
}

void GamePluginManager::BroadcastRegisterDBManager(IDBManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterDBManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterCharacterManager(ICharacterManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterCharacterManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterItemManager(IItemManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterItemManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterShopManager(IShopManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterShopManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterPartyManager(IPartyManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterPartyManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterArenaManager(IArenaManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterArenaManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterGuildManager(IGuildManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterGuildManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterDescManager(IDescManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterDescManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterSectreeManager(ISectreeManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterSectreeManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterDungeonManager(IDungeonManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterDungeonManager(manager);
                           });
}

void GamePluginManager::BroadcastRegisterPVPManager(IPVPManager* manager)
{
    BroadcastToGamePlugins([manager](IGamePlugin* plugin) {
        plugin->OnRegisterPVPManager(manager);
                           });
}

// Broadcast function registration to all plugins
template<typename Func>
void GamePluginManager::BroadcastRegisterFunc(const char* name, Func func) {
    BroadcastToGamePlugins([name, func](IGamePlugin* plugin) {
        plugin->OnRegisterFunction(name, func);
                           });
}

bool GamePluginManager::BroadcastPacketReceive(ICHARACTER* ch, BYTE header, const void* data, size_t size)
{
    bool handled = false;
    BroadcastToGamePlugins([ch, header, data, size, &handled](IGamePlugin* plugin) {
        if (plugin->OnPacketReceive(ch, header, data, size))
        {
            handled = true;
        }
    });
    return handled;
}

bool GamePluginManager::BroadcastPacketSend(ICHARACTER* ch, BYTE header, const void* data, size_t size)
{
    bool handled = false;
    BroadcastToGamePlugins([ch, header, data, size, &handled](IGamePlugin* plugin) {
        if (plugin->OnPacketSend(ch, header, data, size))
        {
            handled = true;
        }
    });
    return handled;
}


// Plugin event hooks
void GamePluginManager::OnPluginLoaded(const std::string& pluginName)
{
    PluginManager::OnPluginLoaded(pluginName);

    std::shared_ptr<IPlugin> gamePlugin = GetGamePlugin(pluginName);
    if (gamePlugin)
    {
        sys_log(0, "Game plugin %s loaded and ready", pluginName.c_str());
    }
}

void GamePluginManager::OnPluginUnloaded(const std::string& pluginName)
{
    // Remove from game plugins list
    auto it = std::find_if(m_gamePlugins.begin(), m_gamePlugins.end(),
        [&pluginName](IGamePlugin* plugin) {
            return plugin && plugin->GetInfo().name == pluginName;
        });

    if (it != m_gamePlugins.end())
    {
        m_gamePlugins.erase(it);
        sys_log(0, "Game plugin %s removed from active list", pluginName.c_str());
    }

    PluginManager::OnPluginUnloaded(pluginName);
}

void GamePluginManager::OnPluginError(const std::string& pluginName, const std::string& error)
{
    sys_err("Game plugin %s error: %s", pluginName.c_str(), error.c_str());

    // Remove from active plugins if error is critical
    auto it = std::find_if(m_gamePlugins.begin(), m_gamePlugins.end(),
        [&pluginName](IGamePlugin* plugin) {
            return plugin && plugin->GetInfo().name == pluginName;
        });

    if (it != m_gamePlugins.end())
    {
        IGamePlugin* plugin = *it;
        if (plugin->GetState() == PluginState::PLUGIN_ERROR)
        {
            m_gamePlugins.erase(it);
            sys_log(0, "Game plugin %s removed due to error state", pluginName.c_str());
        }
    }

    PluginManager::OnPluginError(pluginName, error);
}

bool GamePluginManager::ValidateGamePlugin(IGamePlugin* plugin)
{
    if (!plugin)
    {
        return false;
    }

    const PluginInfo& info = plugin->GetInfo();

    // Check if plugin name is unique
    for (const auto* existingPlugin : m_gamePlugins)
    {
        if (existingPlugin && existingPlugin->GetInfo().name == info.name)
        {
            sys_err("Game plugin with name %s already exists", info.name.c_str());
            return false;
        }
    }

    // Additional game-specific validation can be added here
    // For example, checking required game API version, etc.

    return true;
}


void GamePluginManager::RegisterGameClassesAndFunctions()
{
    BroadcastRegisterGamePluginManager(this);
    BroadcastRegisterCharacterManager(&CHARACTER_MANAGER::instance());
    BroadcastRegisterDBManager(&DBManager::instance());
    BroadcastRegisterDescManager(&DESC_MANAGER::instance());
    BroadcastRegisterItemManager(&ITEM_MANAGER::instance());
    BroadcastRegisterShopManager(&CShopManager::instance());
    BroadcastRegisterSectreeManager(&SECTREE_MANAGER::instance());
    BroadcastRegisterPartyManager(&CPartyManager::instance());
    BroadcastRegisterGuildManager(&CGuildManager::instance());
    BroadcastRegisterDungeonManager(&CDungeonManager::instance());
    BroadcastRegisterPVPManager(&CPVPManager::instance());
    BroadcastRegisterArenaManager(&CArenaManager::Instance());



    // functions
    BroadcastRegisterFunc("sys_log", &sys_log);
	BroadcastRegisterFunc("sys_err", &_sys_err);


    // event
	BroadcastRegisterFunc("event_create", &event_create_ex);
	BroadcastRegisterFunc("event_cancel", &event_cancel);



    // regen
	BroadcastRegisterFunc("regen_do", &regen_do);
	BroadcastRegisterFunc("regen_load", &regen_load);
	BroadcastRegisterFunc("regen_load_in_file", &regen_load_in_file);
}