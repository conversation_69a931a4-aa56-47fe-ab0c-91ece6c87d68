#ifndef __INC_IHORSE_H__
#define __INC_IHORSE_H__

#include "../stl.h"
#include "../tables.h"

// Forward declarations
// THorseInfo is defined in tables.h - don't forward declare it

/**
 * @brief Pure virtual interface for CHorseRider class
 * 
 * Provides ABI-stable access to horse riding functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on horse-related operations for mounted characters.
 */
class IHorseRider
{
public:
    virtual ~IHorseRider() = default;
    
    // ============================================================================
    // HORSE LEVEL AND PROGRESSION
    // ============================================================================
    
    // Horse level management
    virtual BYTE GetHorseLevel() const = 0;
    virtual BYTE GetHorseGrade() = 0;
    virtual void SetHorseLevel(int level) = 0;
    
    // ============================================================================
    // HORSE HEALTH AND STAMINA
    // ============================================================================
    
    // Current health and stamina
    virtual short GetHorseHealth() const = 0;
    virtual short GetHorseStamina() const = 0;
    
    // Maximum health and stamina
    virtual short GetHorseMaxHealth() = 0;
    virtual short GetHorseMaxStamina() = 0;
    
    // ============================================================================
    // HORSE STATISTICS
    // ============================================================================
    
    // Horse stats based on level
    virtual int GetHorseST() = 0;  // Strength
    virtual int GetHorseDX() = 0;  // Dexterity
    virtual int GetHorseHT() = 0;  // Health
    virtual int GetHorseIQ() = 0;  // Intelligence
    virtual int GetHorseArmor() = 0; // Armor
    
    // ============================================================================
    // HORSE STATE MANAGEMENT
    // ============================================================================
    
    // Riding state
    virtual bool IsHorseRiding() const = 0;
    virtual bool StartRiding() = 0;
    virtual bool StopRiding() = 0;
    
    // ============================================================================
    // HORSE ACTIONS
    // ============================================================================
    
    // Horse lifecycle actions
    virtual bool ReviveHorse() = 0;
    virtual void FeedHorse() = 0;
    virtual void HorseDie() = 0;
    virtual void EnterHorse() = 0;
    
    // ============================================================================
    // HORSE DATA MANAGEMENT
    // ============================================================================
    
    // Horse information updates
    virtual void SendHorseInfo() = 0;
    virtual void ClearHorseInfo() = 0;
    virtual void UpdateRideTime(int interval) = 0;
    virtual void ResetHorseHealthDropTime() = 0;
    
    // ============================================================================
    // HORSE CONFIGURATION
    // ============================================================================
    
    // Horse type and appearance
    virtual DWORD GetMyHorseVnum() const = 0;
    
    // ============================================================================
    // HORSE DATA ACCESS
    // ============================================================================
    
    // Direct horse data access
    virtual const THorseInfo& GetHorseData() const = 0;
    virtual void SetHorseData(const THorseInfo& horseInfo) = 0;
    virtual void UpdateHorseDataByLogoff(DWORD logoffTime) = 0;
};

#endif // __INC_IHORSE_H__
