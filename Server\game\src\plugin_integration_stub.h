#ifndef __INC_PLUGIN_INTEGRATION_STUB_H__
#define __INC_PLUGIN_INTEGRATION_STUB_H__

// Temporary plugin integration stub - all macros are no-ops
// This allows the game to compile while the plugin system is being redesigned

// Character event macros - disabled
#define GAME_PLUGIN_CALL_CHARACTER_CREATE(ch) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_DESTROY(ch) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_LOGIN(ch) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_LOGOUT(ch) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_LEVEL_UP(ch, level) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_DEAD(ch, killer) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_KILL(ch, victim) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_REVIVE(ch) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_CHAT(ch, type, message) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_WHISPER(ch, target, message) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_MOVE(ch, x, y) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_ATTACK(ch, victim) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_CHARACTER_SKILL_USE(ch, skillVnum, target) \
    do { /* Plugin system temporarily disabled */ } while(0)

// Item event macros - disabled
#define GAME_PLUGIN_CALL_ITEM_CREATE(item) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_ITEM_DESTROY(item) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_ITEM_EQUIP(ch, item) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_ITEM_UNEQUIP(ch, item) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_ITEM_USE(ch, item) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_ITEM_DROP(ch, item) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_ITEM_PICKUP(ch, item) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_ITEM_TRADE(ch, target, item) \
    do { /* Plugin system temporarily disabled */ } while(0)

// Guild event macros - disabled
#define GAME_PLUGIN_CALL_GUILD_CREATE(guild) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_GUILD_DESTROY(guild) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_GUILD_JOIN(ch, guild) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_GUILD_LEAVE(ch, guild) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_GUILD_WAR_START(guild1, guild2) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_GUILD_WAR_END(guild1, guild2) \
    do { /* Plugin system temporarily disabled */ } while(0)

// Shop event macros - disabled
#define GAME_PLUGIN_CALL_SHOP_BUY(ch, shop, item, count) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_SHOP_SELL(ch, shop, item, count) \
    do { /* Plugin system temporarily disabled */ } while(0)

// Quest event macros - disabled
#define GAME_PLUGIN_CALL_QUEST_START(ch, questIndex) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_QUEST_COMPLETE(ch, questIndex) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_QUEST_FAIL(ch, questIndex) \
    do { /* Plugin system temporarily disabled */ } while(0)

// System event macros - disabled
#define GAME_PLUGIN_CALL_SYSTEM_STARTUP() \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_SYSTEM_SHUTDOWN() \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_SYSTEM_TIMER() \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_SYSTEM_RELOAD() \
    do { /* Plugin system temporarily disabled */ } while(0)

#define GAME_PLUGIN_CALL_DAY_UPDATE() \
    do { /* Plugin system temporarily disabled */ } while(0)

// Helper function - disabled
inline bool IsPluginSystemEnabled()
{
    return false;  // Temporarily disabled
}

// Plugin configuration macros - disabled
#define PLUGIN_CONFIG_LOAD(pluginName, configPath) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define PLUGIN_CONFIG_SAVE(pluginName, configPath) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define PLUGIN_CONFIG_GET_STRING(pluginName, key, defaultValue) \
    (defaultValue)

#define PLUGIN_CONFIG_GET_INT(pluginName, key, defaultValue) \
    (defaultValue)

#define PLUGIN_CONFIG_GET_BOOL(pluginName, key, defaultValue) \
    (defaultValue)

#define PLUGIN_CONFIG_SET_STRING(pluginName, key, value) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define PLUGIN_CONFIG_SET_INT(pluginName, key, value) \
    do { /* Plugin system temporarily disabled */ } while(0)

#define PLUGIN_CONFIG_SET_BOOL(pluginName, key, value) \
    do { /* Plugin system temporarily disabled */ } while(0)

#endif // __INC_PLUGIN_INTEGRATION_STUB_H__
