#ifndef __INC_CHARACTER_MANAGER_H__
#define __INC_CHARACTER_MANAGER_H__

#include "../../common/stl.h"
#include "../../common/length.h"
#include "../../common/interfaces/ICharManager.h" // Temporarily disabled for compilation

#include "vid.h"
class IDungeon;
class CDungeon;
class CHARACTER;
class CharacterVectorInteractor;

typedef std::unordered_map<DWORD, LPCHARACTER> pkCharMap;
typedef std::map<DWORD, DWORD> MobKillCountMap;
typedef std::map<DWORD, CHARACTER_SET> pkCharRaceSet;

class CHARACTER_MANAGER : public singleton<CHARACTER_MANAGER> , virtual public ICharacterManager
{
public:
	typedef std::unordered_map<std::string, LPCHARACTER> NAME_MAP;

	CHARACTER_MANAGER();
	virtual ~CHARACTER_MANAGER();

	void Destroy();

	void GracefulShutdown(); // ������ �˴ٿ��� �� ���. PC�� ��� �����Ű�� Destroy �Ѵ�.

	DWORD AllocVID();

	LPCHARACTER CreateCharacter(const char* name, DWORD dwPID = 0);
	void DestroyCharacter(LPCHARACTER ch);

	void Update(int iPulse);

	LPCHARACTER SpawnMob(DWORD dwVnum, long lMapIndex, long x, long y, long z, bool bSpawnMotion = false, int iRot = -1, bool bShow = true
#if defined(__WJ_SHOW_MOB_INFO__)
		, bool bAggressive = false
#endif
	);
	LPCHARACTER SpawnMobRange(DWORD dwVnum, long lMapIndex, int sx, int sy, int ex, int ey, bool bIsException = false, bool bSpawnMotion = false, bool bAggressive = false);
	LPCHARACTER SpawnGroup(DWORD dwVnum, long lMapIndex, int sx, int sy, int ex, int ey, LPREGEN pkRegen = NULL, bool bAggressive_ = false, LPDUNGEON pDungeon = NULL);
	bool SpawnGroupGroup(DWORD dwVnum, long lMapIndex, int sx, int sy, int ex, int ey, LPREGEN pkRegen = NULL, bool bAggressive_ = false, LPDUNGEON pDungeon = NULL);
	bool SpawnMoveGroup(DWORD dwVnum, long lMapIndex, int sx, int sy, int ex, int ey, int tx, int ty, LPREGEN pkRegen = NULL, bool bAggressive_ = false);
	LPCHARACTER SpawnMobRandomPosition(DWORD dwVnum, long lMapIndex);

	void SelectStone(LPCHARACTER pkChrStone);

	NAME_MAP& GetPCMap() { return m_map_pkPCChr; }

	LPCHARACTER Find(DWORD dwVID);
	LPCHARACTER Find(const VID& vid);
	LPCHARACTER FindPC(const char* name);
	LPCHARACTER FindByPID(DWORD dwPID);

	bool AddToStateList(LPCHARACTER ch);
	void RemoveFromStateList(LPCHARACTER ch);

	// DelayedSave: ��� ��ƾ ������ ������ �ؾ� �� ���� ���� �ϸ� ����
	// ������ �ʹ� �������Ƿ� "������ �Ѵ�" ��� ǥ�ø� �صΰ� ���
	// (��: 1 frame) �Ŀ� �����Ų��.
	void DelayedSave(LPCHARACTER ch);
	bool FlushDelayedSave(LPCHARACTER ch); // Delayed ����Ʈ�� �ִٸ� ����� �����Ѵ�. ���� ó���� ��� ��.
	void ProcessDelayedSave();

	template<class Func> Func for_each_pc(Func f);

	void RegisterForMonsterLog(LPCHARACTER ch);
	void UnregisterForMonsterLog(LPCHARACTER ch);
	void PacketMonsterLog(LPCHARACTER ch, const void* buf, int size);

	void KillLog(DWORD dwVnum);

	void RegisterRaceNum(DWORD dwVnum);
	void RegisterRaceNumMap(LPCHARACTER ch);
	void UnregisterRaceNumMap(LPCHARACTER ch);
	bool GetCharactersByRaceNum(DWORD dwRaceNum, CharacterVectorInteractor& i);

	LPCHARACTER FindSpecifyPC(unsigned int uiJobFlag, long lMapIndex, LPCHARACTER except = NULL, int iMinLevel = 1, int iMaxLevel = PLAYER_MAX_LEVEL_CONST);

	void SetMobItemRate(int value) { m_iMobItemRate = value; }
	void SetMobDamageRate(int value) { m_iMobDamageRate = value; }
	void SetMobGoldAmountRate(int value) { m_iMobGoldAmountRate = value; }
	void SetMobGoldDropRate(int value) { m_iMobGoldDropRate = value; }
	void SetMobExpRate(int value) { m_iMobExpRate = value; }

	void SetMobItemRatePremium(int value) { m_iMobItemRatePremium = value; }
	void SetMobGoldAmountRatePremium(int value) { m_iMobGoldAmountRatePremium = value; }
	void SetMobGoldDropRatePremium(int value) { m_iMobGoldDropRatePremium = value; }
	void SetMobExpRatePremium(int value) { m_iMobExpRatePremium = value; }

	void SetUserDamageRatePremium(int value) { m_iUserDamageRatePremium = value; }
	void SetUserDamageRate(int value) { m_iUserDamageRate = value; }
	int GetMobItemRate(LPCHARACTER ch);
	int GetMobDamageRate(LPCHARACTER ch);
	int GetMobGoldAmountRate(LPCHARACTER ch);
	int GetMobGoldDropRate(LPCHARACTER ch);
	int GetMobExpRate(LPCHARACTER ch);

	int GetUserDamageRate(LPCHARACTER ch);
	void SendScriptToMap(long lMapIndex, const std::string& s);

	bool BeginPendingDestroy();
	void FlushPendingDestroy();


	// plugin functions
	virtual ICHARACTER* FindByPIDInterface(DWORD dwPID);
	virtual ICHARACTER* FindPCInterface(const char* name);
	virtual ICHARACTER* FindInterface(DWORD dwVID);
	ICHARACTER* SpawnMobInterface(DWORD dwVnum, long lMapIndex, long x, long y, long z, bool bSpawnMotion = false, int iRot = -1, bool bShow = true
#if defined(__WJ_SHOW_MOB_INFO__)
						 , bool bAggressive = false
#endif
	);
	ICHARACTER* SpawnMobRangeInterface(DWORD dwVnum, long lMapIndex, int sx, int sy, int ex, int ey, bool bIsException = false, bool bSpawnMotion = false, bool bAggressive = false);
	ICHARACTER* SpawnGroupInterface(DWORD dwVnum, long lMapIndex, int sx, int sy, int ex, int ey, LPREGEN pkRegen = NULL, bool bAggressive_ = false, IDungeon* pDungeon = NULL);
private:
	int m_iMobItemRate;
	int m_iMobDamageRate;
	int m_iMobGoldAmountRate;
	int m_iMobGoldDropRate;
	int m_iMobExpRate;

	int m_iMobItemRatePremium;
	int m_iMobGoldAmountRatePremium;
	int m_iMobGoldDropRatePremium;
	int m_iMobExpRatePremium;

	int m_iUserDamageRate;
	int m_iUserDamageRatePremium;
	int m_iVIDCount;

	pkCharMap m_map_pkChrByVID;
	pkCharMap m_map_pkChrByPID;
	NAME_MAP m_map_pkPCChr;

	char dummy1[1024]; // memory barrier
	CHARACTER_SET m_set_pkChrState; // FSM�� ���ư��� �ִ� ���
	CHARACTER_SET m_set_pkChrForDelayedSave;
	CHARACTER_SET m_set_pkChrMonsterLog;

	LPCHARACTER m_pkChrSelectedStone;

	MobKillCountMap m_map_dwMobKillCount;

	std::set<DWORD> m_set_dwRegisteredRaceNum;
	pkCharRaceSet m_map_pkChrByRaceNum;

	bool m_bUsePendingDestroy;
	CHARACTER_SET m_set_pkChrPendingDestroy;
};

template<class Func>
Func CHARACTER_MANAGER::for_each_pc(Func f)
{
	std::unordered_map<DWORD, LPCHARACTER>::iterator it;

	for (it = m_map_pkChrByPID.begin(); it != m_map_pkChrByPID.end(); ++it)
		f(it->second);

	return f;
}

class CharacterVectorInteractor : public CHARACTER_VECTOR
{
public:
	CharacterVectorInteractor() : m_bMyBegin(false) { }

	CharacterVectorInteractor(const CHARACTER_SET& r);
	virtual ~CharacterVectorInteractor();

private:
	bool m_bMyBegin;
};

#define M2_DESTROY_CHARACTER(ptr) CHARACTER_MANAGER::instance().DestroyCharacter(ptr)

#endif // __INC_CHARACTER_MANAGER_H__
