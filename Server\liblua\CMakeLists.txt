# liblua Library
cmake_minimum_required(VERSION 3.16)

# Enable C language for Lua
enable_language(C)

file(GLOB_RECURSE SOURCES "*.c")
file(GLOB_RECURSE HEADERS "*.h")

create_server_library(liblua
    SOURCES ${SOURCES}
    HEADERS ${HEADERS}
    INCLUDE_DIRS
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Ensure Lua is compiled as C, not C++
set_target_properties(liblua PROPERTIES
    LINKER_LANGUAGE C
)

# Set output directory to match original structure
set_target_properties(liblua PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib
    ARCHIVE_OUTPUT_NAME lua
)
