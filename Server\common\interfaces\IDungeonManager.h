#ifndef __INC_ICDUNGEON_MANAGER_H__
#define __INC_ICDUNGEON_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"
#include "../singleton.h"
#include "IDungeon.h"
// Forward declarations
class CDungeon;
typedef CDungeon* LPDUNGEON;

#if defined(__DUNGEON_RENEWAL__) || defined(__DEFENSE_WAVE__)
enum EDungeonType;
#endif

/**
 * @brief Pure virtual interface for CDungeonManager singleton
 * 
 * Provides ABI-stable access to dungeon management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on dungeon creation and management.
 */
class IDungeonManager : virtual public Isingleton<IDungeonManager>
{
public:
    virtual ~IDungeonManager() = default;
    
    // ============================================================================
    // DUNGEON LIFECYCLE MANAGEMENT
    // ============================================================================
    
    // Dungeon creation
    virtual LPDUNGEON Create(long lOriginalMapIndex
#if defined(__DUNGEON_RENEWAL__) || defined(__DEFENSE_WAVE__)
        , EDungeonType eType = DUNGEON_TYPE_DEFAULT
#endif
    ) = 0;
    
    // Dungeon destruction
    virtual void Destroy(DungeonID dungeon_id) = 0;
    
    // ============================================================================
    // DUNGEON LOOKUP OPERATIONS
    // ============================================================================
    
    // Find operations
    virtual LPDUNGEON Find(DungeonID dungeon_id) = 0;
    virtual LPDUNGEON FindByMapIndex(long lMapIndex) = 0;


	// ============================================================================
	// PLUGIN INTERFACE FUNCTIONS
	// ============================================================================
	// Plugin functions to access dungeons
	virtual IDungeon* FindInterface(DungeonID dungeon_id) = 0;
	virtual IDungeon* FindByMapIndexInterface(long lMapIndex) = 0;
	// Get all active dungeons
    virtual IDungeon* CreateInterface(long lOriginalMapIndex
#if defined(__DUNGEON_RENEWAL__) || defined(__DEFENSE_WAVE__)
                             , EDungeonType eType = DUNGEON_TYPE_DEFAULT
#endif
    ) = 0;
};

#endif // __INC_ICDUNGEON_MANAGER_H__
