# configuration file for making Lua 5.0
# see INSTALL for installation instructions

# These are default values. Skip this section and see the explanations below.

LOADLIB =
DLLIB =
NUMBER =
POPEN =
TMPNAM =
DEGREES =
USERCONF =

# == CHANGE THE SETTINGS BELOW TO SUIT YOUR ENVIRONMENT =======================

# --------------------------------------------------------------- Lua libraries

# Support for dynamically loading C libraries for Lua is a very important
# feature, which we strongly recommend be enabled. By default, this support is
# enabled on Windows systems (see below) but disabled on other systems because
# it relies on system-dependent code that is not part of ANSI C. For more
# information on dynamic loading, read the comments in src/lib/liolib.c .
#
# To enable support for dynamic loading on Unix systems that support the dlfcn
# interface (e.g., Linux, Solaris, IRIX, BSD, AIX, HPUX, and probably others),
# uncomment the next two lines.
#
#LOADLIB = -DUSE_DLOPEN=1
#DLLIB = -ldl
#
# In Linux with gcc, you should also uncomment the next definition for
# MYLDFLAGS, which passes -E (= -export-dynamic) to the linker. This option
# allows dynamic libraries to link back to the `lua' program, so that they do
# not need the Lua libraries. (Other systems may have an equivalent facility.)
#
#MYLDFLAGS = -Wl,-E
#
# On Windows systems. support for dynamic loading is enabled by default.
# To disable this support, uncomment the next line.
#
#LOADLIB = -DUSE_DLL=0

# The Lua IO library (src/lib/liolib.c) has support for pipes using popen and
# pclose. This support is enabled by default on POSIX systems.
# If your system is not POSIX but has popen and pclose, define USE_POPEN=1.
# If you don't want to support pipes, define USE_POPEN=0.
#
#POPEN = -DUSE_POPEN=1
#POPEN = -DUSE_POPEN=0
#
# The form below will probably work in (some) Windows systems.
#
#POPEN = -DUSE_POPEN=1 -Dpopen=_popen -Dpclose=_pclose

# The Lua OS library (src/lib/liolib.c) exports an interface to the C function
# tmpnam, which gcc now thinks is `dangerous'. So, support for tmpnam is
# disabled by default when compiling with gcc.
# If you still want to use tmpnam, define USE_TMPNAME=1. If you don't want to
# use tmpnam even if you're not compiling with gcc, define USE_TMPNAME=0.
#
#TMPNAM = -DUSE_TMPNAME=1
#TMPNAM = -DUSE_TMPNAME=0

# The Lua math library (src/lib/lmathlib.c) now operates in radians, unlike
# previous versions of Lua, which used degrees. To use degrees instead of
# radians, define USE_DEGREES.
#
#DEGREES = -DUSE_DEGREES

# ------------------------------------------------------------------ Lua core

# Lua uses double for numbers. To change this, uncomment and edit the following
# line, changing USE_XXX to one of USE_DOUBLE, USE_FLOAT, USE_LONG, USE_INT.
#
#NUMBER = -DLUA_USER_H='"../etc/luser_number.h"' -DUSE_XXX

# When compiling Lua with gcc on a Pentium machine, using a fast rounding
# method for the conversion of doubles to ints can give around 20% speed
# improvement. To use this rounding method, uncomment the following line.
#NUMBER = -DLUA_USER_H='"../etc/luser_number.h"' -DUSE_FASTROUND

# For partial compatibility with old upvalue syntax, define LUA_COMPATUPSYNTAX.
# For partial compatibility with old upvalue behavior in C functions, define
# LUA_COMPATUPVALUES. Add these definitions to MYCFLAGS.
#
# -DLUA_COMPATUPSYNTAX -DLUA_COMPATUPVALUES

# ------------------------------------------------------------- Lua interpreter

# The stand-alone Lua interpreter needs the math functions, which are usually
# in libm.a (-lm). If your C library already includes the math functions,
# or if you are using a modified interpreter that does not need them,
# then comment the following line or add the appropriates libraries.
#
EXTRA_LIBS = -lm

# If you want to customize the stand-alone Lua interpreter, uncomment and
# edit the following two lines; also edit etc/saconfig.c to suit your needs.
# -DUSE_READLINE adds line editing and history to the interpreter. You need
# to add -lreadline (and perhaps also -lhistory and -lcurses or -lncurses)
# to EXTRA_LIBS.
#
#USERCONF=-DLUA_USERCONFIG='"$(LUA)/etc/saconfig.c"' -DUSE_READLINE
#EXTRA_LIBS = -lm -ldl -lreadline # -lhistory -lcurses -lncurses

# ------------------------------------------------------------------ C compiler

# You need an ANSI C compiler. gcc is a popular one. We do not use -ansi in
# WARN because it disables POSIX features used in the libraries.
#
CC = clang
WARN = -m32 -Wall

# ------------------------------------------------------------------ C options

# Write here any options you may need for your C compiler.
# If you are using gcc, -O3 will get you a faster but larger code. You can
# also add -fomit-frame-pointer to get even faster code at the cost of losing
# debug information. If you only want the shared libraries, you may want to
# add -fPIC to MYCFLAGS.
#
MYCFLAGS = -O2 -g
#MYCFLAGS = -O3 -fomit-frame-pointer # -fPIC

# Write here any options you may need for your C linker.
#MYLDFLAGS =

# ------------------------------------------------------------------ librarian

# This should work in all Unix systems.
#
AR = ar rcu

# If your system doesn't have (or need) ranlib, use RANLIB=true.
# On some systems, "ar s" does what ranlib would do.
#
RANLIB = ranlib
#RANLIB = ar s
#RANLIB = true

# ------------------------------------------------------------------ stripper

# This should work in all Unix systems, but you may want to add options.
#
STRIP = strip

# ------------------------------------------------------------------ install

# Locations for "make install". You may need to be root do "make install".
#
INSTALL_ROOT = ~/lua-5.0
INSTALL_BIN = $(INSTALL_ROOT)/bin
INSTALL_INC = $(INSTALL_ROOT)/include
INSTALL_LIB = $(INSTALL_ROOT)/lib
INSTALL_MAN = $(INSTALL_ROOT)/man/man1

# You may prefer to use "install" instead of "cp" if you have it.
# If you use "install", you may also want to change the permissions after -m.
#
INSTALL_EXEC = cp
INSTALL_DATA = cp
#INSTALL_EXEC= install -m 0755
#INSTALL_DATA= install -m 0644

# == END OF USER SETTINGS. NO NEED TO CHANGE ANYTHING BELOW THIS LINE =========

V = 5.0

BIN = $(LUA)/bin
INC = $(LUA)/include
LIB = $(LUA)/lib

INCS = -I$(INC) $(EXTRA_INCS)
DEFS = $(NUMBER) $(EXTRA_DEFS)

CFLAGS = $(MYCFLAGS) $(WARN) $(INCS) $(DEFS)

# (end of config)
