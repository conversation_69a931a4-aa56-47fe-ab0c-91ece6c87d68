#ifndef __INC_ISECTREE_MANAGER_H__
#define __INC_ISECTREE_MANAGER_H__

#include "../stl.h"
#include "../tables.h"
#include "../../game/src/typedef.h"
/**
 * @brief Pure virtual interface for SECTREE_MANAGER singleton
 * 
 * Provides ABI-stable access to sectree management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on spatial partitioning and map management.
 */

struct TAreaInfo;
struct TMapSetting;
struct TMapRegion;


class ISectreeManager
{
public:
	typedef std::map<std::string, TAreaInfo> TAreaMap;

    virtual ~ISectreeManager() = default;

	virtual LPSECTREE_MAP GetMap(long lMapIndex) = 0;
	virtual LPSECTREE Get(DWORD dwIndex, DWORD package) = 0;
	virtual LPSECTREE Get(DWORD dwIndex, DWORD x, DWORD y) = 0;


	virtual int LoadSettingFile(long lIndex, const char* c_pszSettingFileName, TMapSetting& r_setting) = 0;
	virtual bool LoadMapRegion(const char* c_pszFileName, TMapSetting& r_Setting, const char* c_pszMapName) = 0;
	virtual int Build(const char* c_pszListFileName, const char* c_pszBasePath) = 0;
	virtual LPSECTREE_MAP BuildSectreeFromSetting(TMapSetting& r_setting) = 0;
	virtual bool LoadAttribute(LPSECTREE_MAP pkMapSectree, const char* c_pszFileName, TMapSetting& r_setting) = 0;
	virtual void LoadDungeon(int iIndex, const char* c_pszFileName) = 0;
	virtual bool GetValidLocation(long lMapIndex, long x, long y, long& r_lValidMapIndex, PIXEL_POSITION& r_pos, BYTE empire = 0) = 0;
	virtual bool GetSpawnPosition(long x, long y, PIXEL_POSITION& r_pos) = 0;
	virtual bool GetSpawnPositionByMapIndex(long lMapIndex, PIXEL_POSITION& r_pos) = 0;
	virtual bool GetRecallPositionByEmpire(int iMapIndex, BYTE bEmpire, PIXEL_POSITION& r_pos) = 0;

	virtual const TMapRegion* GetMapRegion(long lMapIndex) = 0;
	virtual int GetMapIndex(long x, long y) = 0;
	virtual const TMapRegion* FindRegionByPartialName(const char* szMapName) = 0;

	virtual bool GetMapBasePosition(long x, long y, PIXEL_POSITION& r_pos) = 0;
	virtual bool GetMapBasePositionByMapIndex(long lMapIndex, PIXEL_POSITION& r_pos) = 0;
	virtual bool GetMovablePosition(long lMapIndex, long x, long y, PIXEL_POSITION& pos) = 0;
	virtual bool IsMovablePosition(long lMapIndex, long x, long y) = 0;
	virtual bool GetCenterPositionOfMap(long lMapIndex, PIXEL_POSITION& r_pos) = 0;
	virtual bool GetRandomLocation(long lMapIndex, PIXEL_POSITION& r_pos, DWORD dwCurrentX = 0, DWORD dwCurrentY = 0, int iMaxDistance = 0) = 0;

	virtual long CreatePrivateMap(long lMapIndex) = 0; // returns new private map index, returns 0 when fail
	virtual void DestroyPrivateMap(long lMapIndex) = 0;

	virtual TAreaMap& GetDungeonArea(long lMapIndex) = 0;
	virtual void SendNPCPosition(LPCHARACTER ch) = 0;
	virtual void InsertNPCPosition(long lMapIndex, BYTE bType, const char* szName, long x, long y) = 0;

	virtual BYTE GetEmpireFromMapIndex(long lMapIndex) = 0;

	virtual void PurgeMonstersInMap(long lMapIndex) = 0;
	virtual void PurgeStonesInMap(long lMapIndex) = 0;
	virtual void PurgeNPCsInMap(long lMapIndex) = 0;
	virtual size_t GetMonsterCountInMap(long lMapIndex) = 0;
	virtual size_t GetMonsterCountInMap(long lMapIndex, DWORD dwVnum) = 0;

	virtual bool ForAttrRegion(long lMapIndex, long lStartX, long lStartY, long lEndX, long lEndY, long lRotate, DWORD dwAttr, EAttrRegionMode mode) = 0;

	virtual bool SaveAttributeToImage(int lMapIndex, const char* c_pszFileName, LPSECTREE_MAP pMapSrc = NULL) = 0;

	virtual void GetRestartCityPos(int iMapIndex, int iEmpire, int& iTargetX, int& iTargetY, int& iTargetZ) = 0;
	virtual void AddRestartCityPos(int iMapIndex = 0, int iEmpire = 0, int iX = 0, int iY = 0, int iZ = 0) = 0;


#if defined(__CONQUEROR_LEVEL__)
	public:
	
	virtual bool LoadNewWorldMapIndexFile(const char* c_pszListFileName) = 0;

	virtual bool IsNewWorldMapIndex(int iMapIndex) const = 0;
	virtual BYTE GetNewWorldSungMa(int iMapIndex, POINT_TYPE wPointType) const = 0;

#endif

	public:


	virtual bool LoadBlockFilterMapIndexFile(const BYTE bType) = 0;
	virtual bool IsBlockFilterMapIndex(const BYTE bType, int iMapIndex) = 0;


};

#endif // __INC_ISECTREE_MANAGER_H__
