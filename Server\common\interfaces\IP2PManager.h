#ifndef __INC_IP2P_MANAGER_H__
#define __INC_IP2P_MANAGER_H__

#include "../stl.h"
#include "../singleton.h"
#include "../../game/src/typedef.h"
struct CCI;
/**
 * @brief Pure virtual interface for P2P_MANAGER singleton
 * 
 * Provides ABI-stable access to P2P management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on peer-to-peer communication management.
 */
class IP2PManager : virtual public Isingleton<IP2PManager>
{
public:
	virtual ~IP2PManager() = default;
    // ============================================================================
    // P2P CONNECTION MANAGEMENT
    // ============================================================================
    
    // Acceptor management
    virtual void RegisterAcceptor(LPDESC d) = 0;
    virtual void UnregisterAcceptor(LPDESC d) = 0;
    
    // Connector management
    virtual void RegisterConnector(LPDESC d) = 0;
    virtual void UnregisterConnector(LPDESC d) = 0;
    
    // User management
    virtual void EraseUserByDesc(LPDESC d) = 0;
    
    // ============================================================================
    // P2P COMMUNICATION
    // ============================================================================
    
    // Data transmission
    virtual void FlushOutput() = 0;
    virtual void Send(const void* c_pvData, int iSize, LPDESC except = NULL) = 0;
    
    // Boot process
    virtual void Boot(LPDESC d) = 0;
    
    // ============================================================================
    // USER SESSION MANAGEMENT
    // ============================================================================
    
    // Login/Logout
    virtual void Login(LPDESC d, const TPacketGGLogin* p) = 0;
    virtual void Logout(const char* c_pszName) = 0;
    
    // User lookup
    virtual CCI* Find(const char* c_pszName) = 0;
    virtual CCI* FindByPID(DWORD pid) = 0;
    
    // ============================================================================
    // STATISTICS AND INFORMATION
    // ============================================================================
    
    // Count information
    virtual int GetCount() = 0;
    virtual int GetPIDCount() = 0;
    virtual int GetEmpireUserCount(int idx) = 0;
    virtual int GetDescCount() = 0;
    
    // Host information
    virtual void GetP2PHostNames(std::string& hostNames) = 0;
};

#endif // __INC_IP2P_MANAGER_H__
