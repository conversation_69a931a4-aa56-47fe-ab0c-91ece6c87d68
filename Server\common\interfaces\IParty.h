#ifndef __INC_IPARTY_H__
#define __INC_IPARTY_H__

#include "../stl.h"
#include "../../game/src/typedef.h"
/**
 * @brief Pure virtual interface for CParty class
 * 
 * Provides ABI-stable access to party functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on party properties and operations.
 */
class IPARTY
{
public:
    virtual ~IPARTY() = default;
    
    // ============================================================================
    // BASIC PARTY PROPERTIES
    // ============================================================================
    
    // Party identification
    virtual DWORD GetLeaderPID() = 0;
    virtual LPCHARACTER GetLeaderCharacter() = 0;
    virtual LPCHARACTER GetLeader() = 0;

    // Party size and limits
    virtual DWORD GetMemberCount() = 0;
    virtual DWORD GetNearMemberCount() = 0;
    
    // ============================================================================
    // PARTY MEMBERS
    // ============================================================================

    // Member management
    virtual void Join(DWORD dwPID) = 0;
    virtual void Quit(DWORD dwPID) = 0;
    virtual void Link(LPCHARACTER pkChr) = 0;
    virtual void Unlink(LPCHARACTER pkChr) = 0;

    // Member queries
    virtual bool IsMember(DWORD pid) = 0;
    virtual bool IsNearLeader(DWORD pid) = 0;
    

    // ============================================================================
    // PARTY COMMUNICATION
    // ============================================================================

    // Party chat and messaging
    virtual void ChatPacketToAllMember(BYTE type, const char* format, ...) = 0;
    virtual void SendMessage(LPCHARACTER ch, BYTE bMsg, DWORD dwArg1, DWORD dwArg2) = 0;
    
    // ============================================================================
    // PARTY SETTINGS
    // ============================================================================

    // Party configuration
    virtual void SetParameter(int iMode) = 0;
    virtual int GetExpDistributionMode() = 0;
    virtual void SetExpCentralizeCharacter(DWORD pid) = 0;
    virtual LPCHARACTER GetExpCentralizeCharacter() = 0;

    // Party flags and options
    virtual void SetFlag(const std::string& name, int value) = 0;
    virtual int GetFlag(const std::string& name) = 0;

    // ============================================================================
    // PARTY BONUSES
    // ============================================================================

    // Party bonuses
    virtual int GetPartyBonusExpPercent() = 0;
    virtual int GetPartyBonusAttackGrade() = 0;
    virtual int GetPartyBonusDefenseGrade() = 0;
    virtual int GetExpBonusPercent() = 0;

    // ============================================================================
    // PARTY ROLES
    // ============================================================================

    // Role management
    virtual bool SetRole(DWORD pid, BYTE bRole, bool on) = 0;
    virtual BYTE GetRole(DWORD pid) = 0;
    virtual bool IsRole(DWORD pid, BYTE bRole) = 0;

    // ============================================================================
    // PARTY POSITIONING
    // ============================================================================

    // Position management
    virtual bool IsPositionNearLeader(LPCHARACTER ch) = 0;
    virtual LPCHARACTER GetNextOwnership(LPCHARACTER ch, long x, long y) = 0;

    // ============================================================================
    // PARTY DUNGEON SUPPORT
    // ============================================================================

    // Dungeon operations
    virtual void SetDungeon(LPDUNGEON pDungeon) = 0;
    virtual LPDUNGEON GetDungeon() = 0;
};

#endif // __INC_IPARTY_H__
