#ifndef __INC_IDBMANAGER_H__
#define __INC_IDBMANAGER_H__

#include "../stl.h"
#include "../tables.h"
#include "../../game/src/typedef.h"

// Forward declarations
typedef struct _SQLMsg SQLMsg;

/**
 * @brief Pure virtual interface for CDBManager singleton
 * 
 * Provides ABI-stable access to database management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on database connection and query management.
 */
class IDBManager
{

public:
    virtual ~IDBManager() = default;
    
    // ============================================================================
    // DATABASE CONNECTION MANAGEMENT
    // ============================================================================
    

    
    // Connection setup
    virtual bool Connect(const char* host, const int port, const char* user, const char* pwd, const char* db) = 0;
    
    // ============================================================================
    // QUERY OPERATIONS
    // ============================================================================
    
    // Query execution
    virtual SQLMsg* DirectQuery(const char* c_pszFormat, ...) = 0;
    virtual void ReturnQuery(int iType, DWORD dwIdent, void* pvData, const char* c_pszFormat, ...) = 0;
    virtual void Query(const char* c_pszFormat, ...) = 0;

    
    // ============================================================================
    // UTILITY FUNCTIONS
    // ============================================================================
    
    // String operations
    virtual size_t EscapeString(char* dst, size_t dstSize, const char* src, size_t srcSize) = 0;

};

#endif // __INC_IDBMANAGER_H__
