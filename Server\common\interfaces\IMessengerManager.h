#ifndef __INC_ICMESSENGER_MANAGER_H__
#define __INC_ICMESSENGER_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

/**
 * @brief Pure virtual interface for CMessengerManager singleton
 * 
 * Provides ABI-stable access to messenger management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on friend list and messenger system management.
 */
class ICMessengerManager
{
public:
    virtual ~ICMessengerManager() = default;
    
    // ============================================================================
    // LIFECYCLE MANAGEMENT
    // ============================================================================
    
    // System lifecycle
    virtual void Initialize() = 0;
    virtual void Destroy() = 0;
    
    // ============================================================================
    // P2P LOGIN/LOGOUT OPERATIONS
    // ============================================================================
    
    // P2P operations
    virtual void P2PLogin(const std::string& c_strPlayer) = 0;
    virtual void P2PLogout(const std::string& c_strPlayer) = 0;
    
    // ============================================================================
    // LOCAL LOGIN/LOGOUT OPERATIONS
    // ============================================================================
    
    // Local operations
    virtual void Login(const std::string& c_strPlayer) = 0;
    virtual void Logout(const std::string& c_strPlayer) = 0;
    
    // ============================================================================
    // FRIEND REQUEST OPERATIONS
    // ============================================================================
    
    // Friend management
    virtual void RequestToAdd(const LPCHARACTER c_lpChar, const LPCHARACTER c_lpCharTarget) = 0;
    virtual void AuthToAdd(const std::string& c_strAccount, const std::string& c_strCompanion, bool bDeny) = 0;
    
    // ============================================================================
    // FRIEND LIST MANAGEMENT
    // ============================================================================
    
    // List operations
    virtual void AddToList(const std::string& c_strAccount, const std::string& c_strCompanion) = 0;
    virtual void RemoveFromList(const std::string& c_strAccount, const std::string& c_strCompanion) = 0;
    virtual void RemoveAllList(const std::string& c_strAccount) = 0;
    
    // ============================================================================
    // FRIEND STATUS QUERIES
    // ============================================================================
    
    // Status checking
    virtual bool IsInList(const std::string& c_strAccount, const std::string& c_strCompanion) = 0;
    virtual bool IsFriend(const char* c_strAccount, const char* c_szName) = 0;
    
#if defined(__MESSENGER_BLOCK_SYSTEM__)
    // ============================================================================
    // BLOCK LIST MANAGEMENT
    // ============================================================================
    
    // Block operations
    virtual void AddToBlockList(const std::string& c_strAccount, const std::string& c_strCompanion) = 0;
    virtual void RemoveFromBlockList(const std::string& c_strAccount, const std::string& c_strCompanion) = 0;
    virtual void RemoveAllBlockList(const std::string& c_strAccount) = 0;
    
    // Block status checking
    virtual bool IsBlocked(const char* c_strAccount, const char* c_szName) = 0;
#endif
};

#endif // __INC_ICMESSENGER_MANAGER_H__
