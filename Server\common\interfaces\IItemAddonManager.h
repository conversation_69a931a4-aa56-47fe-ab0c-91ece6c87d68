#ifndef __INC_ICITEM_ADDON_MANAGER_H__
#define __INC_ICITEM_ADDON_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

/**
 * @brief Pure virtual interface for CItemAddonManager singleton
 * 
 * Provides ABI-stable access to item addon management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on item addon application and management.
 */
class ICItemAddonManager
{
public:
    virtual ~ICItemAddonManager() = default;
    
    // ============================================================================
    // ADDON APPLICATION
    // ============================================================================
    
    // Apply addon to item
    virtual void ApplyAddonTo(int iAddonType, LPITEM pItem) = 0;
};

#endif // __INC_ICITEM_ADDON_MANAGER_H__
