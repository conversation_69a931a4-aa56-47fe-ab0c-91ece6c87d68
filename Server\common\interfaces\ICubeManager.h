#ifndef __INC_ICCUBE_MANAGER_H__
#define __INC_ICCUBE_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

#if defined(__CUBE_RENEWAL__)
// Forward declarations
struct SCubeData;
typedef std::unique_ptr<SCubeData> CubeDataPtr;

/**
 * @brief Pure virtual interface for CCubeManager singleton (Cube Renewal)
 * 
 * Provides ABI-stable access to cube management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on cube crafting system management.
 */
class ICCubeManager
{
public:
    virtual ~ICCubeManager() = default;
    
    // ============================================================================
    // INITIALIZATION AND CONFIGURATION
    // ============================================================================
    
    // System initialization
    virtual void Initialize() = 0;
    
    // Table loading
    virtual void LoadCubeTable(const char* szFileName) = 0;
    virtual bool CheckCubeData(const CubeDataPtr& pkCubeData) = 0;
    virtual bool CheckValidNPC(const DWORD dwNPCVnum) = 0;
    
    // ============================================================================
    // CUBE INTERFACE OPERATIONS
    // ============================================================================
    
    // Cube UI management
    virtual void OpenCube(const LPCHARACTER pChar) = 0;
    virtual void CloseCube(const LPCHARACTER pChar) = 0;
    virtual void MakeCube(const LPCHARACTER pChar, const UINT iCubeIndex, INT iQuantity, const INT iImproveItemPos) = 0;
    
    // ============================================================================
    // CUBE DATA OPERATIONS
    // ============================================================================
    
    // Data retrieval
    virtual const SCubeData& GetCubeData(const DWORD dwNPCVnum, const UINT iCubeIndex) = 0;
    virtual INT GetCubeCategory(const std::string& rkCategory) const = 0;
    virtual bool IsCubeSetAddCategory(const BYTE bCategory) const = 0;
    
    // ============================================================================
    // PACKET PROCESSING
    // ============================================================================
    
    // Network processing
    virtual void Process(LPDESC pDesc, BYTE bSubHeader, DWORD dwNPCVnum = 0, bool bSuccess = false) = 0;
    
    // ============================================================================
    // UTILITY OPERATIONS
    // ============================================================================
    
    // File information
    virtual DWORD GetFileCrc() const = 0;
};

#else

/**
 * @brief Pure virtual interface for legacy cube system
 * 
 * Provides ABI-stable access to legacy cube functionality without exposing
 * internal implementation. All functions are pure virtual to ensure
 * stable vtable layout across compilers.
 */
class ICCubeManager
{
public:
    virtual ~ICCubeManager() = default;
    
    // ============================================================================
    // LEGACY CUBE OPERATIONS
    // ============================================================================
    
    // Legacy cube functions
    virtual void Cube_init() = 0;
    virtual bool Cube_load(const char* file) = 0;
    virtual bool Cube_make(LPCHARACTER ch) = 0;
    virtual void Cube_clean_item(LPCHARACTER ch) = 0;
    virtual void Cube_open(LPCHARACTER ch) = 0;
    virtual void Cube_close(LPCHARACTER ch) = 0;
    virtual void Cube_show_list(LPCHARACTER ch) = 0;
    virtual void Cube_add_item(LPCHARACTER ch, int cube_index, int inven_index) = 0;
    virtual void Cube_delete_item(LPCHARACTER ch, int cube_index) = 0;
    virtual void Cube_request_result_list(LPCHARACTER ch) = 0;
    virtual void Cube_request_material_info(LPCHARACTER ch, int request_start_index, int request_count = 1) = 0;
    virtual void Cube_print() = 0;
    virtual bool Cube_InformationInitialize() = 0;
};

#endif

#endif // __INC_ICCUBE_MANAGER_H__
