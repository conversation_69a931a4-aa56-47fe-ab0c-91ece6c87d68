#include "plugin_packet_interface.h"
#include <cstring>
#include <ctime>
#include <algorithm>

// ============================================================================
// PluginPacketUtils Implementation
// ============================================================================

uint32_t PluginPacketUtils::CalculatePluginID(const std::string& pluginName)
{
    // Simple hash function for plugin name
    uint32_t hash = 0;
    for (char c : pluginName) {
        hash = hash * 31 + static_cast<uint32_t>(c);
    }
    return hash;
}

TPluginPacketHeader PluginPacketUtils::CreatePacketHeader(
    uint8_t header,
    uint8_t type,
    uint16_t size,
    uint32_t pluginID,
    const std::string& targetPlugin,
    uint32_t requestID)
{
    TPluginPacketHeader packetHeader;
    packetHeader.bHeader = header;
    packetHeader.bType = type;
    packetHeader.wSize = size;
    packetHeader.dwPluginID = pluginID;
    packetHeader.dwRequestID = requestID;

    // Copy target plugin name (truncate if too long)
    size_t nameLen = std::min(targetPlugin.length(), sizeof(packetHeader.szTargetPlugin) - 1);
    memcpy(packetHeader.szTargetPlugin, targetPlugin.c_str(), nameLen);
    packetHeader.szTargetPlugin[nameLen] = '\0';

    return packetHeader;
}

bool PluginPacketUtils::ValidatePacket(const TPluginPacket* packet, uint32_t expectedSize)
{
    if (!packet || expectedSize < sizeof(TPluginPacketHeader))
        return false;

    // Check if packet size matches expected size
    if (packet->header.wSize < expectedSize)
        return false;

    // Basic validation of header fields
    if (packet->header.bHeader == 0 || packet->header.wSize < sizeof(TPluginPacketHeader))
        return false;

    return true;
}

const void* PluginPacketUtils::GetPacketData(const TPluginPacket* packet)
{
    if (!packet)
        return nullptr;
        
    return reinterpret_cast<const uint8_t*>(packet) + sizeof(TPluginPacketHeader);
}

uint32_t PluginPacketUtils::GetPacketDataSize(const TPluginPacket* packet)
{
    if (!packet)
        return 0;

    return packet->header.wSize - sizeof(TPluginPacketHeader);
}
