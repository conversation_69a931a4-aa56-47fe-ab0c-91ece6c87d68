/**
 * @file test_external_api.c
 * @brief Simple test file for the External C API
 * 
 * This file contains basic tests to verify that the external C API
 * compiles correctly and provides the expected functionality.
 * It can be used as a compilation test and basic functionality check.
 */

#include "external_api.h"
#include <stdio.h>
#include <assert.h>
#include <string.h>

// ============================================================================
// TEST HELPER FUNCTIONS
// ============================================================================

static int test_count = 0;
static int test_passed = 0;
static int test_failed = 0;

#define TEST_START(name) \
    do { \
        printf("Running test: %s\n", name); \
        test_count++; \
    } while(0)

#define TEST_ASSERT(condition, message) \
    do { \
        if (condition) { \
            printf("  PASS: %s\n", message); \
            test_passed++; \
        } else { \
            printf("  FAIL: %s\n", message); \
            test_failed++; \
        } \
    } while(0)

#define TEST_END() \
    do { \
        printf("Test completed.\n\n"); \
    } while(0)

// ============================================================================
// BASIC API TESTS
// ============================================================================

/**
 * @brief Test API initialization and cleanup
 */
void test_api_initialization(void)
{
    TEST_START("API Initialization");
    
    // Test initialization
    CharApiResult result = char_api_initialize();
    TEST_ASSERT(result == CHAR_API_SUCCESS, "API initialization should succeed");
    
    // Test version information
    int major, minor, patch;
    char_api_get_version(&major, &minor, &patch);
    TEST_ASSERT(major == CHAR_API_VERSION_MAJOR, "Major version should match");
    TEST_ASSERT(minor == CHAR_API_VERSION_MINOR, "Minor version should match");
    TEST_ASSERT(patch == CHAR_API_VERSION_PATCH, "Patch version should match");
    
    // Test compatibility check
    int compatible = char_api_is_compatible(1, 0, 0);
    TEST_ASSERT(compatible == 1, "Should be compatible with version 1.0.0");
    
    // Test cleanup
    char_api_cleanup();
    TEST_ASSERT(1, "API cleanup should not crash");
    
    TEST_END();
}

/**
 * @brief Test error handling functionality
 */
void test_error_handling(void)
{
    TEST_START("Error Handling");
    
    // Initialize API first
    char_api_initialize();
    
    // Test error code to string conversion
    const char* success_str = char_api_result_to_string(CHAR_API_SUCCESS);
    TEST_ASSERT(strcmp(success_str, "Success") == 0, "Success string should be correct");
    
    const char* error_str = char_api_result_to_string(CHAR_API_ERROR_NULL_HANDLE);
    TEST_ASSERT(strcmp(error_str, "Null handle error") == 0, "Error string should be correct");
    
    // Test last error functionality
    char_api_clear_last_error();
    const char* last_error = char_api_get_last_error();
    TEST_ASSERT(last_error == NULL, "Last error should be NULL after clearing");
    
    // Test convenience macros
    TEST_ASSERT(CHAR_API_SUCCEEDED(CHAR_API_SUCCESS), "SUCCEEDED macro should work");
    TEST_ASSERT(CHAR_API_FAILED(CHAR_API_ERROR_NULL_HANDLE), "FAILED macro should work");
    TEST_ASSERT(CHAR_API_HANDLE_INVALID(NULL), "HANDLE_INVALID macro should work");
    
    char_api_cleanup();
    TEST_END();
}

/**
 * @brief Test debug functionality
 */
void test_debug_functionality(void)
{
    TEST_START("Debug Functionality");
    
    char_api_initialize();
    
    // Test debug mode
    char_api_set_debug_mode(1);
    TEST_ASSERT(char_api_is_debug_mode() == 1, "Debug mode should be enabled");
    
    char_api_set_debug_mode(0);
    TEST_ASSERT(char_api_is_debug_mode() == 0, "Debug mode should be disabled");
    
    // Test debug logging (should not crash)
    char_api_set_debug_mode(1);
    char_api_debug_log("Test debug message: %s %d", "test", 123);
    TEST_ASSERT(1, "Debug logging should not crash");
    
    char_api_cleanup();
    TEST_END();
}

/**
 * @brief Test statistics functionality
 */
void test_statistics(void)
{
    TEST_START("Statistics");
    
    char_api_initialize();
    
    // Reset statistics
    char_api_reset_statistics();
    
    // Get initial statistics
    unsigned long total, successful, failed;
    char_api_get_statistics(&total, &successful, &failed);
    TEST_ASSERT(total == 0, "Initial total calls should be 0");
    TEST_ASSERT(successful == 0, "Initial successful calls should be 0");
    TEST_ASSERT(failed == 0, "Initial failed calls should be 0");
    
    // Record some test calls
    char_api_record_call_result(CHAR_API_SUCCESS);
    char_api_record_call_result(CHAR_API_SUCCESS);
    char_api_record_call_result(CHAR_API_ERROR_NULL_HANDLE);
    
    // Check updated statistics
    char_api_get_statistics(&total, &successful, &failed);
    TEST_ASSERT(total == 3, "Total calls should be 3");
    TEST_ASSERT(successful == 2, "Successful calls should be 2");
    TEST_ASSERT(failed == 1, "Failed calls should be 1");
    
    char_api_cleanup();
    TEST_END();
}

/**
 * @brief Test character manager handle access
 */
void test_character_manager_access(void)
{
    TEST_START("Character Manager Access");
    
    char_api_initialize();
    
    // Try to get character manager instance
    // Note: This will likely fail in a test environment without the actual game running
    CharacterManagerHandle_t mgr = char_manager_api_get_instance();
    
    if (mgr != NULL) {
        TEST_ASSERT(CHAR_API_HANDLE_VALID(mgr), "Manager handle should be valid");
        printf("  NOTE: Character manager instance obtained successfully\n");
    } else {
        printf("  NOTE: Character manager not available (expected in test environment)\n");
        TEST_ASSERT(1, "Test completed (manager unavailable is expected)");
    }
    
    char_api_cleanup();
    TEST_END();
}

/**
 * @brief Test invalid handle operations
 */
void test_invalid_handles(void)
{
    TEST_START("Invalid Handle Operations");
    
    char_api_initialize();
    
    // Test character API with NULL handle
    char name_buffer[64];
    CharApiResult result = char_api_get_name(NULL, name_buffer, sizeof(name_buffer));
    TEST_ASSERT(result == CHAR_API_ERROR_NULL_HANDLE, "NULL handle should return error");
    
    DWORD vid;
    result = char_api_get_vid(NULL, &vid);
    TEST_ASSERT(result == CHAR_API_ERROR_NULL_HANDLE, "NULL handle should return error");
    
    // Test character manager API with NULL handle
    CharacterHandle_t character = NULL;
    result = char_manager_api_find_by_vid(NULL, 12345, &character);
    TEST_ASSERT(result == CHAR_API_ERROR_NULL_HANDLE, "NULL manager handle should return error");
    
    char_api_cleanup();
    TEST_END();
}

/**
 * @brief Test parameter validation
 */
void test_parameter_validation(void)
{
    TEST_START("Parameter Validation");
    
    char_api_initialize();
    
    // Test NULL buffer parameters
    CharApiResult result = char_api_get_name(NULL, NULL, 0);
    TEST_ASSERT(result != CHAR_API_SUCCESS, "NULL buffer should return error");
    
    // Test invalid buffer size
    char small_buffer[1];
    result = char_api_get_name(NULL, small_buffer, 0);
    TEST_ASSERT(result != CHAR_API_SUCCESS, "Zero buffer size should return error");
    
    // Test NULL output parameters
    result = char_api_get_vid(NULL, NULL);
    TEST_ASSERT(result != CHAR_API_SUCCESS, "NULL output parameter should return error");
    
    char_api_cleanup();
    TEST_END();
}

// ============================================================================
// MAIN TEST RUNNER
// ============================================================================

/**
 * @brief Main test function
 */
int main(void)
{
    printf("=== External C API Test Suite ===\n\n");
    
    // Run all tests
    test_api_initialization();
    test_error_handling();
    test_debug_functionality();
    test_statistics();
    test_character_manager_access();
    test_invalid_handles();
    test_parameter_validation();
    
    // Print test summary
    printf("=== Test Summary ===\n");
    printf("Total tests: %d\n", test_count);
    printf("Assertions passed: %d\n", test_passed);
    printf("Assertions failed: %d\n", test_failed);
    printf("Overall result: %s\n", (test_failed == 0) ? "PASS" : "FAIL");
    
    return (test_failed == 0) ? 0 : 1;
}
