#ifndef __INC_ICTARGET_MANAGER_H__
#define __INC_ICTARGET_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
struct TargetInfo;
class LPEVENT;

/**
 * @brief Pure virtual interface for CTargetManager singleton
 * 
 * Provides ABI-stable access to target management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on quest target and objective management.
 */
class ICTargetManager
{
public:
    virtual ~ICTargetManager() = default;
    
    // ============================================================================
    // TARGET CREATION AND MANAGEMENT
    // ============================================================================
    
    // Target creation
    virtual void CreateTarget(DWORD dwPID, DWORD dwQuestIndex, const char* c_pszTargetName, int iType, int iArg1, int iArg2, int iMapIndex, const char* c_pszTargetDesc = NULL, int iSendFlag = 1) = 0;
    
    // Target deletion
    virtual void DeleteTarget(DWORD dwPID, DWORD dwQuestIndex, const char* c_pszTargetName) = 0;
    
#if defined(__PRIVATESHOP_SEARCH_SYSTEM__)
    // Shop search target deletion
    virtual void DeleteShopSearchTarget(DWORD dwPID) = 0;
#endif
    
    // ============================================================================
    // TARGET LOOKUP OPERATIONS
    // ============================================================================
    
    // Target information retrieval
    virtual TargetInfo* GetTargetInfo(DWORD dwPID, int iType, int iArg1) = 0;
    virtual LPEVENT GetTargetEvent(DWORD dwPID, DWORD dwQuestIndex, const char* c_pszTargetName) = 0;
    
    // ============================================================================
    // PLAYER SESSION MANAGEMENT
    // ============================================================================
    
    // Player logout handling
    virtual void Logout(DWORD dwPID) = 0;
};

#endif // __INC_ICTARGET_MANAGER_H__
