/**
 * @file BasicGamePlugin.cpp
 * @brief Basic example of a Game Plugin
 * <AUTHOR> System Team
 * @version 2.0
 * @date 2025
 * 
 * This is a simple example showing how to create a basic Game plugin that:
 * - Handles player enter/leave game events
 * - Processes custom packets
 * - Demonstrates player interaction
 * - Shows proper error handling
 */

#include "../../Server/common/plugin_interface.h"
#include <string>
#include <map>

/**
 * @class BasicGamePlugin
 * @brief A simple game plugin that demonstrates basic functionality
 * 
 * This plugin shows the minimal implementation required for a Game plugin:
 * - Basic plugin information
 * - Player game world events
 * - Custom packet handling
 * - Player interaction features
 */
class BasicGamePlugin : public IGamePlugin
{
private:
    std::string m_pluginName;
    bool m_initialized;
    uint32_t m_playersEntered;
    uint32_t m_packetsProcessed;
    
    // Simple player tracking
    std::map<DWORD, std::string> m_activePlayers; // PlayerID -> PlayerName

public:
    /**
     * @brief Constructor
     */
    BasicGamePlugin() 
        : m_pluginName("BasicGamePlugin")
        , m_initialized(false)
        , m_playersEntered(0)
        , m_packetsProcessed(0)
    {
        sys_log(0, "[%s] Plugin instance created", m_pluginName.c_str());
    }
    
    /**
     * @brief Destructor
     */
    virtual ~BasicGamePlugin()
    {
        sys_log(0, "[%s] Plugin destroyed. Processed %u player entries, %u packets", 
                m_pluginName.c_str(), m_playersEntered, m_packetsProcessed);
    }

    /**
     * @brief Get plugin information
     */
    virtual PluginInfo GetPluginInfo() const override
    {
        PluginInfo info;
        info.name = m_pluginName;
        info.version = "1.0.0";
        info.description = "Basic example Game plugin for learning purposes";
        info.author = "Plugin System Team";
        info.supportedInterfaces = PLUGIN_INTERFACE_GAME;
        return info;
    }
    
    /**
     * @brief Initialize the plugin
     */
    virtual bool Initialize() override
    {
        sys_log(0, "[%s] Initializing plugin...", m_pluginName.c_str());
        
        try {
            // Perform any initialization here
            // For example: load configuration, initialize data structures, etc.
            
            m_initialized = true;
            sys_log(0, "[%s] Plugin initialized successfully", m_pluginName.c_str());
            return true;
            
        } catch (const std::exception& e) {
            sys_err("[%s] Failed to initialize: %s", m_pluginName.c_str(), e.what());
            return false;
        }
    }
    
    /**
     * @brief Shutdown the plugin
     */
    virtual void Shutdown() override
    {
        sys_log(0, "[%s] Shutting down plugin...", m_pluginName.c_str());
        
        try {
            // Perform cleanup here
            // For example: save data, close connections, free resources, etc.
            
            // Clear active players
            m_activePlayers.clear();
            
            m_initialized = false;
            sys_log(0, "[%s] Plugin shutdown complete", m_pluginName.c_str());
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error during shutdown: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle player entering game world
     * This is called when a player enters the game world (after login)
     */
    virtual void OnPlayerEnterGame(DWORD playerID, LPCHARACTER ch) override
    {
        if (!m_initialized || !ch) return;
        
        try {
            const char* playerName = ch->GetName();
            sys_log(0, "[%s] Player entered game: ID=%u, Name=%s, Level=%d", 
                    m_pluginName.c_str(), playerID, playerName, ch->GetLevel());
            
            // Track the player
            m_activePlayers[playerID] = playerName ? playerName : "Unknown";
            m_playersEntered++;
            
            // Example: Send welcome message to player
            ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Welcome to the game, %s!", 
                          m_pluginName.c_str(), playerName);
            
            // Example: Special handling for high-level players
            if (ch->GetLevel() >= 50) {
                ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Welcome back, veteran player!", 
                              m_pluginName.c_str());
            }
            
            // Example: Show current online count
            ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Players currently tracked: %zu", 
                          m_pluginName.c_str(), m_activePlayers.size());
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error handling player enter game: %s", m_pluginName.c_str(), e.what());
        }
    }
    
    /**
     * @brief Handle player leaving game world
     * This is called when a player leaves the game world
     */
    virtual void OnPlayerLeaveGame(DWORD playerID, LPCHARACTER ch) override
    {
        if (!m_initialized) return;
        
        try {
            const char* playerName = ch ? ch->GetName() : "Unknown";
            sys_log(0, "[%s] Player left game: ID=%u, Name=%s", 
                    m_pluginName.c_str(), playerID, playerName);
            
            // Remove player from tracking
            auto it = m_activePlayers.find(playerID);
            if (it != m_activePlayers.end()) {
                sys_log(0, "[%s] Removed player %s from tracking", 
                        m_pluginName.c_str(), it->second.c_str());
                m_activePlayers.erase(it);
            }
            
            // Example: You could perform cleanup actions here such as:
            // - Save player session data
            // - Clean up temporary effects
            // - Update statistics
            // - Notify other players
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error handling player leave game: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle player level up event
     * This is called when a player gains a level
     */
    virtual void OnPlayerLevelUp(DWORD playerID, LPCHARACTER ch, int newLevel) override
    {
        if (!m_initialized || !ch) return;
        
        try {
            sys_log(0, "[%s] Player leveled up: %s reached level %d", 
                    m_pluginName.c_str(), ch->GetName(), newLevel);
            
            // Example: Congratulate the player
            ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Congratulations on reaching level %d!", 
                          m_pluginName.c_str(), newLevel);
            
            // Example: Special rewards for milestone levels
            if (newLevel % 10 == 0) { // Every 10 levels
                ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Milestone level reached! You deserve a reward!", 
                              m_pluginName.c_str());
                // Here you could give special items, bonuses, etc.
            }
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error handling player level up: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle custom packet processing
     * This is called when a custom packet is received from a client
     */
    virtual bool OnPacketReceive(DWORD playerID, LPCHARACTER ch, const void* data, size_t size) override
    {
        if (!m_initialized || !ch || !data || size == 0) return false;
        
        try {
            const BYTE* packet = static_cast<const BYTE*>(data);
            BYTE header = packet[0];
            
            // Handle custom packet types for this plugin
            switch (header) {
                case 0xE0: // Example: Plugin info request
                    return HandleInfoRequest(playerID, ch, packet, size);
                    
                case 0xE1: // Example: Plugin command
                    return HandlePluginCommand(playerID, ch, packet, size);
                    
                case 0xE2: // Example: Player list request
                    return HandlePlayerListRequest(playerID, ch, packet, size);
                    
                default:
                    // Not our packet, let other systems handle it
                    return false;
            }
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error processing packet: %s", m_pluginName.c_str(), e.what());
            return false;
        }
    }

    /**
     * @brief Handle chat messages
     * This is called when a player sends a chat message
     */
    virtual void OnChatMessage(DWORD playerID, LPCHARACTER ch, const char* message, int chatType) override
    {
        if (!m_initialized || !ch || !message) return;
        
        try {
            // Example: Log chat messages (be careful with privacy!)
            sys_log(0, "[%s] Chat from %s (Type %d): %s", 
                    m_pluginName.c_str(), ch->GetName(), chatType, message);
            
            // Example: Handle special commands in chat
            if (strncmp(message, "!plugin", 7) == 0) {
                ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Plugin is active! Version: %s", 
                              m_pluginName.c_str(), GetPluginInfo().version.c_str());
            }
            else if (strncmp(message, "!players", 8) == 0) {
                ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Currently tracking %zu players", 
                              m_pluginName.c_str(), m_activePlayers.size());
            }
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error handling chat message: %s", m_pluginName.c_str(), e.what());
        }
    }

private:
    /**
     * @brief Handle plugin info request packet
     */
    bool HandleInfoRequest(DWORD playerID, LPCHARACTER ch, const BYTE* packet, size_t size)
    {
        sys_log(0, "[%s] Info request from player %s", m_pluginName.c_str(), ch->GetName());
        
        // Send plugin information to the player
        PluginInfo info = GetPluginInfo();
        ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Plugin: %s v%s", 
                      m_pluginName.c_str(), info.name.c_str(), info.version.c_str());
        ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Description: %s", 
                      m_pluginName.c_str(), info.description.c_str());
        ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Author: %s", 
                      m_pluginName.c_str(), info.author.c_str());
        
        m_packetsProcessed++;
        return true;
    }
    
    /**
     * @brief Handle plugin command packet
     */
    bool HandlePluginCommand(DWORD playerID, LPCHARACTER ch, const BYTE* packet, size_t size)
    {
        sys_log(0, "[%s] Command packet from player %s", m_pluginName.c_str(), ch->GetName());
        
        // Example: Parse command from packet
        if (size >= 2) {
            BYTE command = packet[1];
            
            switch (command) {
                case 1: // Status command
                    ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Status: Active, %u players entered, %u packets processed", 
                                  m_pluginName.c_str(), m_playersEntered, m_packetsProcessed);
                    break;
                    
                case 2: // Help command
                    ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Available commands: status, help, list", 
                                  m_pluginName.c_str());
                    break;
                    
                default:
                    ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Unknown command: %d", 
                                  m_pluginName.c_str(), command);
                    break;
            }
        }
        
        m_packetsProcessed++;
        return true;
    }
    
    /**
     * @brief Handle player list request packet
     */
    bool HandlePlayerListRequest(DWORD playerID, LPCHARACTER ch, const BYTE* packet, size_t size)
    {
        sys_log(0, "[%s] Player list request from %s", m_pluginName.c_str(), ch->GetName());
        
        ch->ChatPacket(CHAT_TYPE_INFO, "[%s] Currently tracking %zu players:", 
                      m_pluginName.c_str(), m_activePlayers.size());
        
        int count = 0;
        for (const auto& pair : m_activePlayers) {
            ch->ChatPacket(CHAT_TYPE_INFO, "[%s] %d. %s (ID: %u)", 
                          m_pluginName.c_str(), ++count, pair.second.c_str(), pair.first);
            
            // Limit output to prevent spam
            if (count >= 10) {
                ch->ChatPacket(CHAT_TYPE_INFO, "[%s] ... and %zu more players", 
                              m_pluginName.c_str(), m_activePlayers.size() - 10);
                break;
            }
        }
        
        m_packetsProcessed++;
        return true;
    }
};

// =================================================================
// PLUGIN FACTORY FUNCTIONS
// =================================================================

/**
 * @brief Create plugin instance
 * This function is called by the plugin loader to create an instance of the plugin
 */
extern "C" __declspec(dllexport) IPlugin* CreatePlugin()
{
    return new BasicGamePlugin();
}

/**
 * @brief Destroy plugin instance
 * This function is called by the plugin loader to destroy a plugin instance
 */
extern "C" __declspec(dllexport) void DestroyPlugin(IPlugin* plugin)
{
    delete plugin;
}

/**
 * @brief Get plugin information
 * This function allows the plugin loader to get information about the plugin
 * without creating an instance
 */
extern "C" __declspec(dllexport) PluginInfo GetPluginInfo()
{
    BasicGamePlugin temp;
    return temp.GetPluginInfo();
}
