# Plugins CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# Only build plugins if we have the necessary dependencies
if(NOT TARGET Server::libthecore OR NOT TARGET Server::libgame)
    message(STATUS "Plugin dependencies not available, skipping plugin examples...")
    return()
endif()

# Set C++ standard to match the main project
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Common function to create plugin libraries
function(create_plugin_library target_name plugin_type)
    # Parse arguments
    cmake_parse_arguments(PLUGIN "" "" "SOURCES;HEADERS;DEPENDENCIES" ${ARGN})

    # Create shared library
    add_library(${target_name} SHARED ${PLUGIN_SOURCES} ${PLUGIN_HEADERS})

    # Set target properties
    set_target_properties(${target_name} PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
        FOLDER "Plugins/${plugin_type}"
        OUTPUT_NAME "${target_name}"
        DEBUG_POSTFIX "_d"
        PREFIX ""  # Remove lib prefix on Unix
        POSITION_INDEPENDENT_CODE ON
    )

    # Apply ProjectZ compiler settings for Windows builds
    if(WIN32)
        apply_projectz_compiler_settings(${target_name})

        # Plugin-specific Windows settings
        target_compile_definitions(${target_name} PRIVATE
            _WIN32_WINNT=0x0601
            WIN32_LEAN_AND_MEAN
            NOMINMAX
            _CRT_SECURE_NO_WARNINGS
            _SCL_SECURE_NO_WARNINGS
            PLUGIN_EXPORTS
        )

        # Ensure proper runtime library linking - force static runtime
        set_property(TARGET ${target_name} PROPERTY
            MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>"
        )

        # Explicitly set compiler flags to override any inherited debug settings
        target_compile_options(${target_name} PRIVATE
            $<$<CONFIG:Release>:/MT>
            $<$<CONFIG:Debug>:/MTd>
            $<$<CONFIG:Distribute>:/MT>
        )

        # Linker options
        target_link_options(${target_name} PRIVATE
            /SUBSYSTEM:WINDOWS
            $<$<CONFIG:Release>:/LTCG>
        )
    endif()

    # Include directories based on plugin type
    if(plugin_type STREQUAL "Game")
        target_include_directories(${target_name} PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_SOURCE_DIR}/Server/common
            ${CMAKE_SOURCE_DIR}/Server/common/interfaces
            ${CMAKE_SOURCE_DIR}/Server/game/src
            ${CMAKE_SOURCE_DIR}/Server/libthecore/include
            ${CMAKE_SOURCE_DIR}/Server/libgame/include
            ${CMAKE_SOURCE_DIR}/Server/liblua/include
            ${CMAKE_SOURCE_DIR}/External/include
        )
    elseif(plugin_type STREQUAL "DB")
        target_include_directories(${target_name} PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_SOURCE_DIR}/Server/common
            ${CMAKE_SOURCE_DIR}/Server/common/interfaces
            ${CMAKE_SOURCE_DIR}/Server/db/src
            ${CMAKE_SOURCE_DIR}/Server/libthecore/include
            ${CMAKE_SOURCE_DIR}/Server/libsql
            ${CMAKE_SOURCE_DIR}/External/include
            ${MYSQL_INCLUDE_DIRS}
        )
    endif()

    # Link with dependencies
    if(plugin_type STREQUAL "Game")
        target_link_libraries(${target_name} PRIVATE
            ${PLUGIN_DEPENDENCIES}
        )
    elseif(plugin_type STREQUAL "DB")
        target_link_libraries(${target_name} PRIVATE
            Server::libthecore
            Server::libsql
            ${MYSQL_LIBRARIES}
            ${PLUGIN_DEPENDENCIES}
        )
    endif()

    # Platform-specific libraries
    if(WIN32)
        target_link_libraries(${target_name} PRIVATE
            ws2_32 kernel32 user32 gdi32 winspool shell32 ole32 oleaut32 uuid comdlg32 advapi32
        )
    else()
        target_link_libraries(${target_name} PRIVATE
            dl m
        )
    endif()

    # Set output directories
    set_target_properties(${target_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/plugins/${plugin_type}
        LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/plugins/${plugin_type}
        RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_SOURCE_DIR}/plugins/${plugin_type}
        LIBRARY_OUTPUT_DIRECTORY_DEBUG ${CMAKE_SOURCE_DIR}/plugins/${plugin_type}
        RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_SOURCE_DIR}/plugins/${plugin_type}
        LIBRARY_OUTPUT_DIRECTORY_RELEASE ${CMAKE_SOURCE_DIR}/plugins/${plugin_type}
        RUNTIME_OUTPUT_DIRECTORY_DISTRIBUTE ${CMAKE_SOURCE_DIR}/plugins/${plugin_type}
        LIBRARY_OUTPUT_DIRECTORY_DISTRIBUTE ${CMAKE_SOURCE_DIR}/plugins/${plugin_type}
    )

    # Copy plugin to game directory after build for game plugins
    if(plugin_type STREQUAL "Game" AND WIN32)
        add_custom_command(TARGET ${target_name} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory 
                "${CMAKE_SOURCE_DIR}/Server/game/channel1/core1/plugins/game"
            COMMAND ${CMAKE_COMMAND} -E copy 
                "$<TARGET_FILE:${target_name}>"
                "${CMAKE_SOURCE_DIR}/Server/game/channel1/core1/plugins/game/"
            COMMENT "Copying ${target_name} to game plugins directory"
        )
    endif()

    # Create alias
    add_library(Plugins::${target_name} ALIAS ${target_name})
endfunction()

# Add subdirectories for different plugin types
add_subdirectory(Game)
add_subdirectory(Examples)

# Create convenience targets
add_custom_target(AllPlugins DEPENDS
    CrossCompilerGamePlugin
    SimpleGamePlugin
    ComprehensiveGamePlugin
    MinimalGamePlugin
    ComprehensiveWorkingPlugin
)

# Print configuration summary
message(STATUS "=== Plugin Examples Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Output directory: ${CMAKE_SOURCE_DIR}/plugins")
message(STATUS "=====================================")
