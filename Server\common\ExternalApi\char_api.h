#ifndef __INC_CHAR_API_H__
#define __INC_CHAR_API_H__

/**
 * @file char_api.h
 * @brief External C API for CHARACTER class functionality
 * 
 * This file provides a C API wrapper around the CHARACTER class to ensure
 * cross-compiler compatibility between game server (clang) and plugins (gcc/vs).
 * 
 * All functions use extern "C" linkage to maintain ABI stability across
 * different compilers and prevent C++ name mangling issues.
 * 
 * Design principles:
 * - Pure C interface with no C++ features exposed
 * - Opaque handles to hide C++ implementation details
 * - Simple data types (int, char*, etc.) for parameters
 * - Error codes instead of exceptions
 * - No STL containers in the interface
 */

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// FORWARD DECLARATIONS AND TYPES
// ============================================================================

// Opaque handle to CHARACTER instance
typedef struct CharacterHandle* CharacterHandle_t;

// Opaque handle to ITEM instance  
typedef struct ItemHandle* ItemHandle_t;

// Opaque handle to GUILD instance
typedef struct GuildHandle* GuildHandle_t;

// Opaque handle to PARTY instance
typedef struct PartyHandle* PartyHandle_t;

// Basic data types matching the game's definitions
typedef unsigned int DWORD;
typedef unsigned short WORD;
typedef unsigned char BYTE;
typedef int POINT_VALUE;

// Error codes for API functions
typedef enum {
    CHAR_API_SUCCESS = 0,
    CHAR_API_ERROR_NULL_HANDLE = -1,
    CHAR_API_ERROR_INVALID_PARAM = -2,
    CHAR_API_ERROR_BUFFER_TOO_SMALL = -3,
    CHAR_API_ERROR_NOT_FOUND = -4,
    CHAR_API_ERROR_PERMISSION_DENIED = -5,
    CHAR_API_ERROR_INTERNAL = -6
} CharApiResult;

// Character position structure
typedef struct {
    long x;
    long y;
    long z;
} CharPosition;

// Character stats structure
typedef struct {
    int hp;
    int max_hp;
    int sp;
    int max_sp;
    int stamina;
    int level;
    DWORD exp;
    int gold;
    BYTE job;
} CharStats;

// ============================================================================
// BASIC CHARACTER INFORMATION
// ============================================================================

/**
 * @brief Get character's name
 * @param handle Character handle
 * @param buffer Output buffer for name
 * @param buffer_size Size of output buffer
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_name(CharacterHandle_t handle, char* buffer, int buffer_size);

/**
 * @brief Get character's VID (Virtual ID)
 * @param handle Character handle
 * @param vid Output parameter for VID
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_vid(CharacterHandle_t handle, DWORD* vid);

/**
 * @brief Get character's player ID
 * @param handle Character handle
 * @param pid Output parameter for player ID
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_player_id(CharacterHandle_t handle, DWORD* pid);

/**
 * @brief Get character's race number
 * @param handle Character handle
 * @param race Output parameter for race
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_race_num(CharacterHandle_t handle, WORD* race);

/**
 * @brief Get character's job
 * @param handle Character handle
 * @param job Output parameter for job
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_job(CharacterHandle_t handle, BYTE* job);

/**
 * @brief Check if character is PC (player character)
 * @param handle Character handle
 * @param is_pc Output parameter (1 if PC, 0 if NPC)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_is_pc(CharacterHandle_t handle, int* is_pc);

/**
 * @brief Check if character is NPC
 * @param handle Character handle
 * @param is_npc Output parameter (1 if NPC, 0 if PC)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_is_npc(CharacterHandle_t handle, int* is_npc);

/**
 * @brief Check if character is monster
 * @param handle Character handle
 * @param is_monster Output parameter (1 if monster, 0 otherwise)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_is_monster(CharacterHandle_t handle, int* is_monster);

// ============================================================================
// POSITION AND MOVEMENT
// ============================================================================

/**
 * @brief Get character's current position
 * @param handle Character handle
 * @param position Output parameter for position
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_position(CharacterHandle_t handle, CharPosition* position);

/**
 * @brief Get character's map index
 * @param handle Character handle
 * @param map_index Output parameter for map index
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_map_index(CharacterHandle_t handle, long* map_index);

/**
 * @brief Warp character to specified position
 * @param handle Character handle
 * @param map_index Target map index
 * @param x Target X coordinate
 * @param y Target Y coordinate
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_warp(CharacterHandle_t handle, long map_index, long x, long y);

// ============================================================================
// CHARACTER STATS AND POINTS
// ============================================================================

/**
 * @brief Get character's basic stats
 * @param handle Character handle
 * @param stats Output parameter for stats
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_stats(CharacterHandle_t handle, CharStats* stats);

/**
 * @brief Get specific point value
 * @param handle Character handle
 * @param point_type Point type (from POINT_* constants)
 * @param value Output parameter for point value
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_point(CharacterHandle_t handle, BYTE point_type, POINT_VALUE* value);

/**
 * @brief Set specific point value
 * @param handle Character handle
 * @param point_type Point type (from POINT_* constants)
 * @param value New point value
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_set_point(CharacterHandle_t handle, BYTE point_type, POINT_VALUE value);

/**
 * @brief Get character's level
 * @param handle Character handle
 * @param level Output parameter for level
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_level(CharacterHandle_t handle, BYTE* level);

/**
 * @brief Get character's experience
 * @param handle Character handle
 * @param exp Output parameter for experience
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_exp(CharacterHandle_t handle, DWORD* exp);

/**
 * @brief Get character's gold
 * @param handle Character handle
 * @param gold Output parameter for gold
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_gold(CharacterHandle_t handle, int* gold);

/**
 * @brief Set character's gold
 * @param handle Character handle
 * @param gold New gold amount
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_set_gold(CharacterHandle_t handle, int gold);

// ============================================================================
// HEALTH AND STATUS
// ============================================================================

/**
 * @brief Get character's HP
 * @param handle Character handle
 * @param hp Output parameter for current HP
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_hp(CharacterHandle_t handle, int* hp);

/**
 * @brief Get character's maximum HP
 * @param handle Character handle
 * @param max_hp Output parameter for maximum HP
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_max_hp(CharacterHandle_t handle, int* max_hp);

/**
 * @brief Set character's HP
 * @param handle Character handle
 * @param hp New HP value
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_set_hp(CharacterHandle_t handle, int hp);

/**
 * @brief Get character's SP (Skill Points)
 * @param handle Character handle
 * @param sp Output parameter for current SP
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_get_sp(CharacterHandle_t handle, int* sp);

/**
 * @brief Set character's SP
 * @param handle Character handle
 * @param sp New SP value
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_set_sp(CharacterHandle_t handle, int sp);

/**
 * @brief Check if character is dead
 * @param handle Character handle
 * @param is_dead Output parameter (1 if dead, 0 if alive)
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_is_dead(CharacterHandle_t handle, int* is_dead);

// ============================================================================
// COMMUNICATION
// ============================================================================

/**
 * @brief Send chat message to character
 * @param handle Character handle
 * @param message Message to send
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_chat_packet(CharacterHandle_t handle, const char* message);

/**
 * @brief Send notice to character
 * @param handle Character handle
 * @param message Notice message
 * @return CHAR_API_SUCCESS on success, error code on failure
 */
CharApiResult char_api_notice(CharacterHandle_t handle, const char* message);

#ifdef __cplusplus
}
#endif

#endif // __INC_CHAR_API_H__
