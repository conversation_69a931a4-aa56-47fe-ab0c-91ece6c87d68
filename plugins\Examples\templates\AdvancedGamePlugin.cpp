/**
 * @file AdvancedGamePlugin.cpp
 * @brief Advanced Game Plugin Example
 * 
 * This example demonstrates all the advanced features of the plugin system:
 * - Packet communication with database server and clients
 * - Custom database queries with compiled query strings
 * - Event handling for all game events
 * - Configuration management
 * - Statistics tracking
 * - Error handling and recovery
 */

#include "AdvancedGamePlugin.h"
#include "../../Server/game/src/plugin_packet_manager.h"
#include <cstring>
#include <sstream>

// ============================================================================
// Compiled Query Strings (Security: Not exposed in source)
// ============================================================================

namespace {
    // Player statistics queries
    const char* QUERY_LOAD_PLAYER_STATS = 
        "SELECT kills, deaths, playtime, last_login FROM player_stats WHERE player_id = %u";
    
    const char* QUERY_SAVE_PLAYER_STATS = 
        "INSERT INTO player_stats (player_id, kills, deaths, playtime, last_login) "
        "VALUES (%u, %d, %d, %d, %u) ON DUPLICATE KEY UPDATE "
        "kills = VALUES(kills), deaths = VALUES(deaths), playtime = VALUES(playtime), last_login = VALUES(last_login)";
    
    // Guild ranking queries
    const char* QUERY_GUILD_RANKING = 
        "SELECT guild_id, guild_name, total_exp, member_count FROM guild_ranking ORDER BY total_exp DESC LIMIT 10";
    
    // Custom item tracking
    const char* QUERY_TRACK_ITEM_USAGE = 
        "INSERT INTO item_usage_log (player_id, item_vnum, usage_count, last_used) "
        "VALUES (%u, %u, 1, NOW()) ON DUPLICATE KEY UPDATE "
        "usage_count = usage_count + 1, last_used = NOW()";
}

// ============================================================================
// Constructor & Initialization
// ============================================================================

AdvancedGamePlugin::AdvancedGamePlugin()
    : m_state(PluginState::UNLOADED), m_packetInterface(nullptr)
{
    // Initialize plugin information
    m_info.name = "AdvancedGamePlugin";
    m_info.description = "Advanced game plugin demonstrating all features";
    m_info.author = "Plugin Developer";
    m_info.version = PluginVersion(1, 0, 0);
    m_info.requiredApiVersion = PluginVersion(1, 0, 0);
    
    // Initialize statistics
    m_stats.startTime = time(nullptr);
    
    LogEvent(0, "Advanced plugin created");
}

bool AdvancedGamePlugin::Initialize()
{
    if (m_state != PluginState::UNLOADED)
        return false;
    
    LogEvent(0, "Initializing advanced plugin...");
    
    // Load configuration
    LoadDefaultConfig();
    if (!ValidateConfig())
    {
        LogEvent(2, "Configuration validation failed");
        m_state = PluginState::ERROR;
        return false;
    }
    
    // Initialize packet communication
    if (!InitializePacketCommunication())
    {
        LogEvent(2, "Failed to initialize packet communication");
        m_state = PluginState::ERROR;
        return false;
    }
    
    // Initialize custom systems
    InitializePlayerTracking();
    InitializeGuildRanking();
    InitializeItemTracking();
    
    m_state = PluginState::INITIALIZED;
    LogEvent(0, "Advanced plugin initialized successfully");
    return true;
}

bool AdvancedGamePlugin::Start()
{
    if (m_state != PluginState::INITIALIZED)
        return false;
    
    LogEvent(0, "Starting advanced plugin...");
    
    // Start background tasks
    StartPeriodicTasks();
    
    m_state = PluginState::RUNNING;
    LogEvent(0, "Advanced plugin started successfully");
    return true;
}

// ============================================================================
// Event Handlers with Advanced Features
// ============================================================================

void AdvancedGamePlugin::OnCharacterLogin(LPCHARACTER ch)
{
    if (!IsValidCharacter(ch)) return;
    
    m_stats.eventsProcessed++;
    m_stats.playersOnline++;
    
    LogEvent(0, "Player login: " + std::string(ch->GetName()));
    
    // Send welcome packet with custom data
    SendWelcomePacket(ch);
    
    // Load player statistics from database
    LoadPlayerStatistics(ch->GetPlayerID());
    
    // Update player tracking
    UpdatePlayerTracking(ch, "login");
    
    // Notify database about login for analytics
    NotifyDatabaseLogin(ch);
}

void AdvancedGamePlugin::OnCharacterLogout(LPCHARACTER ch)
{
    if (!IsValidCharacter(ch)) return;
    
    m_stats.eventsProcessed++;
    if (m_stats.playersOnline > 0)
        m_stats.playersOnline--;
    
    LogEvent(0, "Player logout: " + std::string(ch->GetName()));
    
    // Save player statistics to database
    SavePlayerStatistics(ch);
    
    // Update player tracking
    UpdatePlayerTracking(ch, "logout");
    
    // Notify database about logout
    NotifyDatabaseLogout(ch);
}

void AdvancedGamePlugin::OnItemUse(LPCHARACTER ch, LPITEM item)
{
    if (!IsValidCharacter(ch) || !item) return;
    
    m_stats.eventsProcessed++;
    
    // Track item usage in database
    TrackItemUsage(ch->GetPlayerID(), item->GetVnum());
    
    // Send item usage notification to client
    SendItemUsageNotification(ch, item);
    
    // Check for special item effects
    HandleSpecialItemEffects(ch, item);
}

void AdvancedGamePlugin::OnKill(LPCHARACTER killer, LPCHARACTER victim)
{
    if (!IsValidCharacter(killer) || !IsValidCharacter(victim)) return;
    
    m_stats.eventsProcessed++;
    
    // Update kill/death statistics
    UpdateKillDeathStats(killer, victim);
    
    // Send kill notification to clients
    BroadcastKillNotification(killer, victim);
    
    // Update guild ranking if applicable
    if (killer->GetGuild() && victim->GetGuild())
    {
        UpdateGuildRanking(killer->GetGuild(), victim->GetGuild());
    }
}

bool AdvancedGamePlugin::OnCommand(LPCHARACTER ch, const char* command, const char* args)
{
    if (!IsValidCharacter(ch)) return false;
    
    std::string cmd(command);
    
    // Handle custom commands
    if (cmd == "stats")
    {
        SendPlayerStatistics(ch);
        return true;
    }
    else if (cmd == "ranking")
    {
        SendGuildRanking(ch);
        return true;
    }
    else if (cmd == "plugin_info")
    {
        SendPluginInfo(ch);
        return true;
    }
    
    return false; // Command not handled
}

// ============================================================================
// Packet Communication Implementation
// ============================================================================

bool AdvancedGamePlugin::InitializePacketCommunication()
{
    m_packetInterface = GetGamePluginPacketInterface();
    if (!m_packetInterface)
    {
        LogEvent(2, "Failed to get packet interface");
        return false;
    }
    
    // Register packet handler
    m_packetInterface->RegisterPacketHandler([this](const void* data, uint32_t size, const std::string& source) {
        HandleDatabasePacket(data, size, source);
    });
    
    // Register query result handlers
    m_packetInterface->RegisterQueryHandler(QID_PLAYER_STATS, [this](uint32_t queryID, const void* data, uint32_t size, uint32_t playerID) {
        HandlePlayerStatsResult(queryID, data, size, playerID);
    });
    
    m_packetInterface->RegisterQueryHandler(QID_GUILD_RANKING, [this](uint32_t queryID, const void* data, uint32_t size, uint32_t playerID) {
        HandleGuildRankingResult(queryID, data, size, playerID);
    });
    
    LogEvent(0, "Packet communication initialized");
    return true;
}

void AdvancedGamePlugin::HandleDatabasePacket(const void* data, uint32_t size, const std::string& sourcePlugin)
{
    m_stats.packetsReceived++;
    
    if (size < sizeof(uint32_t)) return;
    
    uint32_t packetType = *static_cast<const uint32_t*>(data);
    
    switch (packetType)
    {
        case PACKET_TYPE_PLAYER_STATS_UPDATE:
            HandlePlayerStatsUpdate(data, size);
            break;
            
        case PACKET_TYPE_GUILD_RANKING_UPDATE:
            HandleGuildRankingUpdate(data, size);
            break;
            
        case PACKET_TYPE_SYSTEM_NOTIFICATION:
            HandleSystemNotification(data, size);
            break;
            
        default:
            LogEvent(1, "Unknown packet type from " + sourcePlugin + ": " + std::to_string(packetType));
            break;
    }
}

// ============================================================================
// Database Query Implementation
// ============================================================================

void AdvancedGamePlugin::LoadPlayerStatistics(uint32_t playerID)
{
    struct PlayerStatsQuery
    {
        uint32_t playerID;
    } query = { playerID };
    
    m_packetInterface->ExecuteCustomQuery(QID_PLAYER_STATS, &query, sizeof(query), playerID);
    m_stats.queriesExecuted++;
}

void AdvancedGamePlugin::SavePlayerStatistics(LPCHARACTER ch)
{
    if (!IsValidCharacter(ch)) return;
    
    // Get player statistics (example data)
    struct PlayerStatsData
    {
        uint32_t playerID;
        int kills;
        int deaths;
        int playtime;
        uint32_t lastLogin;
    } stats;
    
    stats.playerID = ch->GetPlayerID();
    stats.kills = 0; // Get from character data
    stats.deaths = 0; // Get from character data
    stats.playtime = 0; // Calculate playtime
    stats.lastLogin = static_cast<uint32_t>(time(nullptr));
    
    // Send to database for saving
    m_packetInterface->SendToDatabase(&stats, sizeof(stats), "MyDBPlugin");
    m_stats.packetsSent++;
}

void AdvancedGamePlugin::TrackItemUsage(uint32_t playerID, uint32_t itemVnum)
{
    struct ItemUsageData
    {
        uint32_t type;
        uint32_t playerID;
        uint32_t itemVnum;
        uint32_t timestamp;
    } data;
    
    data.type = PACKET_TYPE_ITEM_USAGE;
    data.playerID = playerID;
    data.itemVnum = itemVnum;
    data.timestamp = static_cast<uint32_t>(time(nullptr));
    
    m_packetInterface->SendToDatabase(&data, sizeof(data), "MyDBPlugin");
    m_stats.packetsSent++;
}

// ============================================================================
// Client Communication
// ============================================================================

void AdvancedGamePlugin::SendWelcomePacket(LPCHARACTER ch)
{
    struct WelcomePacket
    {
        uint32_t type;
        char message[256];
        uint32_t bonusExp;
        uint32_t onlineTime;
    } packet;
    
    packet.type = CLIENT_PACKET_WELCOME;
    strncpy(packet.message, m_config.welcomeMessage.c_str(), sizeof(packet.message) - 1);
    packet.message[sizeof(packet.message) - 1] = '\0';
    packet.bonusExp = 1000;
    packet.onlineTime = static_cast<uint32_t>(time(nullptr) - m_stats.startTime);
    
    m_packetInterface->SendToClient(ch->GetDesc(), &packet, sizeof(packet));
    m_stats.packetsSent++;
}

void AdvancedGamePlugin::SendPlayerStatistics(LPCHARACTER ch)
{
    // Request player statistics from database
    LoadPlayerStatistics(ch->GetPlayerID());
    
    // Statistics will be sent to client when query result is received
}

void AdvancedGamePlugin::BroadcastKillNotification(LPCHARACTER killer, LPCHARACTER victim)
{
    struct KillNotification
    {
        uint32_t type;
        char killerName[32];
        char victimName[32];
        uint32_t killerLevel;
        uint32_t victimLevel;
    } notification;
    
    notification.type = CLIENT_PACKET_KILL_NOTIFICATION;
    strncpy(notification.killerName, killer->GetName(), sizeof(notification.killerName) - 1);
    strncpy(notification.victimName, victim->GetName(), sizeof(notification.victimName) - 1);
    notification.killerName[sizeof(notification.killerName) - 1] = '\0';
    notification.victimName[sizeof(notification.victimName) - 1] = '\0';
    notification.killerLevel = killer->GetLevel();
    notification.victimLevel = victim->GetLevel();
    
    m_packetInterface->BroadcastToClients(&notification, sizeof(notification));
    m_stats.packetsSent++;
}

// ============================================================================
// Plugin Registration
// ============================================================================

extern "C" {
    IPlugin* CreatePlugin() {
        return new AdvancedGamePlugin();
    }
    
    void DestroyPlugin(IPlugin* plugin) {
        delete plugin;
    }
}

REGISTER_GAME_PLUGIN("AdvancedGamePlugin", AdvancedGamePlugin, 
    CAPABILITY_CHARACTER_EVENTS | CAPABILITY_ITEM_EVENTS | CAPABILITY_COMBAT_EVENTS |
    CAPABILITY_GUILD_EVENTS | CAPABILITY_COMMAND_HANDLING | CAPABILITY_NETWORK_EVENTS)
