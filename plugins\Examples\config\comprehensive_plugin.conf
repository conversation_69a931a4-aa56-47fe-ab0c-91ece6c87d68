# Comprehensive Working Plugin Configuration
# This file contains all configuration options for the comprehensive plugin example
# Edit these values to customize the plugin behavior

#==============================================================================
# GENERAL SETTINGS
#==============================================================================

# Enable or disable the plugin
enabled=true

# Plugin log level (DEBUG, INFO, WARNING, ERROR)
log_level=INFO

# Enable debug mode for detailed logging
debug_mode=false

# Plugin data file for persistent storage
data_file=plugins/data/comprehensive_plugin.dat

#==============================================================================
# FEATURE TOGGLES
#==============================================================================

# Enable welcome message when players log in
enable_welcome_message=true

# Enable automatic level up rewards
enable_levelup_rewards=true

# Enable chat message filtering
enable_chat_filter=false

# Enable statistics tracking
enable_statistics=true

# Maximum login reward amount (for future features)
max_login_reward=1000

#==============================================================================
# CHAT FILTER SETTINGS
#==============================================================================

# List of banned words (comma-separated, case-insensitive)
banned_words=spam,hack,cheat,bot,gold,sell,buy,website,discord,youtube

#==============================================================================
# LEVEL UP REWARDS
#==============================================================================

# Reward items given at specific levels (level_reward_X=item_vnum)
# These are example item vnums - replace with your server's item IDs

# Basic potions for early levels
level_reward_10=27001
level_reward_20=27002
level_reward_30=27003

# Better rewards for higher levels
level_reward_40=27004
level_reward_50=27005

# Special milestone rewards
level_reward_75=70038
level_reward_100=70039

#==============================================================================
# STATISTICS SETTINGS
#==============================================================================

# Statistics save interval in seconds
stats_save_interval=300

# Statistics file location
stats_file=plugins/data/comprehensive_plugin_stats.dat

# Track detailed events (may impact performance)
track_detailed_events=true

#==============================================================================
# COMMAND SETTINGS
#==============================================================================

# Enable plugin commands (/plugin)
enable_commands=true

# Commands available to all players (comma-separated)
player_commands=stats,config,help

# Commands available only to GMs (comma-separated)
gm_commands=reload,reset

#==============================================================================
# NOTIFICATION SETTINGS
#==============================================================================

# Send notifications for various events
notify_login=true
notify_levelup=true
notify_death=false
notify_guild_events=true

#==============================================================================
# PERFORMANCE SETTINGS
#==============================================================================

# Enable performance monitoring
monitor_performance=true

# Performance log interval in seconds
perf_log_interval=3600

# Maximum time allowed for event processing (milliseconds)
max_event_time=100

#==============================================================================
# DATABASE SETTINGS
#==============================================================================

# Enable custom database queries (advanced feature)
enable_custom_queries=false

# Database query timeout in seconds
db_query_timeout=30

# Use prepared statements for better performance
use_prepared_statements=true

#==============================================================================
# ADVANCED SETTINGS
#==============================================================================

# Plugin heartbeat interval in milliseconds
heartbeat_interval=1000

# Cache size for event data
event_cache_size=1000

# Enable event batching for better performance
enable_event_batching=true

# Event batch size
event_batch_size=10

#==============================================================================
# EXAMPLE CUSTOM SETTINGS
#==============================================================================

# These are examples of how you can add custom settings for your plugin

# Custom feature toggle
custom_feature_enabled=false

# Custom numeric setting
custom_max_value=9999

# Custom string setting
custom_message=Hello from the plugin!

# Custom list setting (comma-separated)
custom_list=item1,item2,item3

#==============================================================================
# NOTES
#==============================================================================

# Lines starting with # are comments and are ignored
# Boolean values can be: true/false, 1/0, yes/no, on/off
# Numeric values should be integers unless specified otherwise
# String values should not be quoted unless the quotes are part of the value
# List values should be comma-separated without spaces (unless spaces are intended)

# To reload this configuration in-game, use: /plugin reload (GM only)
# To view current configuration in-game, use: /plugin config

# For more information, see the documentation:
# - IMPLEMENTATION_GUIDE.md
# - CONFIGURATION_GUIDE.md
# - API_REFERENCE.md
# - TROUBLESHOOTING.md
