# libthecore/src Library
cmake_minimum_required(VERSION 3.16)

create_server_library(libthecore
    SOURCES
        buffer.cpp
        des.cpp
        fdwatch.cpp
        gost.cpp
        gost_old.c
        hangul.cpp
        heart.cpp
        kstbl.c
        log.cpp
        main.cpp
        memcpy.c
        signal.cpp
        socket.cpp
        tea.cpp
        utils.cpp
        xdirent.cpp
        xgetopt.cpp
        xmd5.cpp
    INCLUDE_DIRS
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
    DEPENDENCIES
        Threads::Threads
)

# Set output directory to match original Makefile
set_target_properties(libthecore PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../lib
    ARCHIVE_OUTPUT_NAME thecore
    C_STANDARD 11
    C_STANDARD_REQUIRED ON
)

# Convert C files to C++ for easier compilation
file(GLOB C_FILES "*.c")
foreach(C_FILE ${C_FILES})
    get_filename_component(BASENAME ${C_FILE} NAME_WE)
    get_filename_component(DIR ${C_FILE} DIRECTORY)
    set(CPP_FILE "${DIR}/${BASENAME}.cpp")
    if(NOT EXISTS ${CPP_FILE})
        configure_file(${C_FILE} ${CPP_FILE} COPYONLY)
    endif()
endforeach()
