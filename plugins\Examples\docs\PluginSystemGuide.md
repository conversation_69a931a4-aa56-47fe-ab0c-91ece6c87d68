# Plugin System Comprehensive Guide

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Plugin Types](#plugin-types)
4. [Getting Started](#getting-started)
5. [Plugin Interfaces](#plugin-interfaces)
6. [Event System](#event-system)
7. [Database Integration](#database-integration)
8. [Packet Handling](#packet-handling)
9. [Configuration Management](#configuration-management)
10. [Best Practices](#best-practices)
11. [Troubleshooting](#troubleshooting)

## Overview

The Plugin System is a comprehensive, modular architecture that allows developers to extend the game server functionality without modifying the core codebase. It supports both Database (DB) and Game server plugins, providing a clean separation of concerns and enabling hot-swappable functionality.

### Key Features
- **Dual Interface Support**: Both DB and Game server plugin interfaces
- **Event-Driven Architecture**: React to player, item, guild, and system events
- **Database Integration**: Custom QID handlers and database operations
- **Packet Processing**: Handle custom client-server communication
- **Configuration Management**: Flexible plugin configuration system
- **Hot Loading**: Load and unload plugins without server restart
- **Thread Safety**: Built-in synchronization for multi-threaded environments

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Plugin System Architecture               │
├─────────────────────────────────────────────────────────────┤
│  Client                                                     │
│    ↓ (Custom Packets)                                       │
│  Game Server                                                │
│    ├── GamePluginManager                                    │
│    ├── PluginLoader                                         │
│    └── PluginPacketManager                                  │
│    ↓ (Database Operations)                                  │
│  DB Server                                                  │
│    ├── DBPluginManager                                      │
│    ├── PluginQIDManager                                     │
│    └── PluginDBInterface                                    │
│    ↓ (Database Queries)                                     │
│  Database                                                   │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

1. **Plugin Interfaces**: Define contracts for plugin functionality
2. **Plugin Managers**: Handle plugin lifecycle and event distribution
3. **Plugin Loaders**: Manage dynamic loading/unloading of plugin DLLs
4. **Packet Managers**: Route custom packets to appropriate plugins
5. **QID Managers**: Handle custom database query routing
6. **Configuration System**: Manage plugin settings and parameters

## Plugin Types

### 1. DB Plugins (IDBPlugin)
Handle database-related events and operations:
- Player login/logout processing
- Item creation/modification/deletion
- Guild management
- Boot-time initialization
- Custom database queries (QID handling)

### 2. Game Plugins (IGamePlugin)
Handle game world events and player interactions:
- Player enter/leave game world
- Custom packet processing
- Real-time game events
- Player interaction handling
- Custom command processing

### 3. Hybrid Plugins
Plugins can implement both interfaces to handle both DB and Game events.

## Getting Started

### 1. Plugin Structure
```cpp
class MyPlugin : public IDBPlugin, public IGamePlugin
{
public:
    // Core interface methods
    virtual PluginInfo GetPluginInfo() const override;
    virtual bool Initialize() override;
    virtual void Shutdown() override;
    
    // DB Plugin methods
    virtual void OnPlayerLogin(DWORD playerID, const char* playerName) override;
    virtual void OnPlayerLogout(DWORD playerID) override;
    virtual void OnItemCreate(DWORD itemID, TPlayerItem* item) override;
    
    // Game Plugin methods
    virtual void OnPlayerEnterGame(DWORD playerID, LPCHARACTER ch) override;
    virtual void OnPlayerLeaveGame(DWORD playerID, LPCHARACTER ch) override;
    virtual bool OnPacketReceive(DWORD playerID, LPCHARACTER ch, const void* data, size_t size) override;
};
```

### 2. Plugin Factory Functions
```cpp
extern "C" __declspec(dllexport) IPlugin* CreatePlugin()
{
    return new MyPlugin();
}

extern "C" __declspec(dllexport) void DestroyPlugin(IPlugin* plugin)
{
    delete plugin;
}

extern "C" __declspec(dllexport) PluginInfo GetPluginInfo()
{
    MyPlugin temp;
    return temp.GetPluginInfo();
}
```

### 3. CMakeLists.txt Template
```cmake
cmake_minimum_required(VERSION 3.16)
project(MyPlugin)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add plugin source files
add_library(MyPlugin SHARED
    MyPlugin.cpp
    # Add other source files here
)

# Link against required libraries
target_link_libraries(MyPlugin
    # Add required libraries here
)

# Set output directory
set_target_properties(MyPlugin PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/plugins"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/plugins"
)
```

## Plugin Interfaces

### IPlugin (Base Interface)
```cpp
class IPlugin
{
public:
    virtual ~IPlugin() = default;
    virtual PluginInfo GetPluginInfo() const = 0;
    virtual bool Initialize() = 0;
    virtual void Shutdown() = 0;
};
```

### IDBPlugin Interface
Handles database server events:

#### Player Events
- `OnPlayerLogin(DWORD playerID, const char* playerName)`: Player logs into the game
- `OnPlayerLogout(DWORD playerID)`: Player logs out of the game
- `OnPlayerCreate(DWORD playerID, const char* playerName)`: New player account created
- `OnPlayerDelete(DWORD playerID)`: Player account deleted

#### Item Events
- `OnItemCreate(DWORD itemID, TPlayerItem* item)`: Item created in database
- `OnItemLoad(DWORD itemID, TPlayerItem* item)`: Item loaded from database
- `OnItemSave(DWORD itemID, TPlayerItem* item)`: Item saved to database
- `OnItemDestroy(DWORD itemID)`: Item deleted from database

#### Guild Events
- `OnGuildCreate(DWORD guildID, const char* guildName)`: Guild created
- `OnGuildDestroy(DWORD guildID)`: Guild destroyed
- `OnGuildMemberAdd(DWORD guildID, DWORD playerID)`: Member added to guild
- `OnGuildMemberRemove(DWORD guildID, DWORD playerID)`: Member removed from guild

#### System Events
- `OnBootStart()`: Server boot process started
- `OnBootInitialization()`: Server initialization phase
- `OnBootComplete()`: Server boot completed
- `OnServerShutdown()`: Server shutting down
- `OnHeartbeat()`: Regular heartbeat event
- `OnMinuteUpdate()`: Called every minute
- `OnHourUpdate()`: Called every hour
- `OnDayUpdate()`: Called every day

### IGamePlugin Interface
Handles game server events:

#### Player Events
- `OnPlayerEnterGame(DWORD playerID, LPCHARACTER ch)`: Player enters game world
- `OnPlayerLeaveGame(DWORD playerID, LPCHARACTER ch)`: Player leaves game world
- `OnPlayerLevelUp(DWORD playerID, LPCHARACTER ch, int newLevel)`: Player levels up
- `OnPlayerDeath(DWORD playerID, LPCHARACTER ch, LPCHARACTER killer)`: Player dies

#### Combat Events
- `OnPlayerKill(DWORD killerID, DWORD victimID, LPCHARACTER killer, LPCHARACTER victim)`: Player kills another player
- `OnMonsterKill(DWORD playerID, LPCHARACTER ch, DWORD monsterVnum)`: Player kills monster

#### Item Events
- `OnItemUse(DWORD playerID, LPCHARACTER ch, DWORD itemVnum)`: Player uses item
- `OnItemEquip(DWORD playerID, LPCHARACTER ch, DWORD itemVnum)`: Player equips item
- `OnItemUnequip(DWORD playerID, LPCHARACTER ch, DWORD itemVnum)`: Player unequips item

#### Communication Events
- `OnChatMessage(DWORD playerID, LPCHARACTER ch, const char* message, int chatType)`: Player sends chat message
- `OnWhisperMessage(DWORD senderID, DWORD receiverID, const char* message)`: Whisper message sent

#### Packet Events
- `OnPacketReceive(DWORD playerID, LPCHARACTER ch, const void* data, size_t size)`: Custom packet received

## Event System

The plugin system uses an event-driven architecture where plugins register for specific events and receive callbacks when those events occur.

### Event Flow
1. **Event Occurs**: Something happens in the game (player login, item creation, etc.)
2. **Event Broadcast**: The appropriate manager broadcasts the event to all registered plugins
3. **Plugin Processing**: Each plugin that implements the relevant interface method processes the event
4. **Error Handling**: Any exceptions are caught and logged without affecting other plugins

### Event Priority
Events are processed in the order plugins are loaded. Critical plugins should be loaded first.

### Event Filtering
Plugins can choose which events to handle by implementing only the relevant interface methods.

## Database Integration

### Custom QID Handlers
Plugins can register custom Query ID (QID) handlers to process specific database operations:

```cpp
// Register a custom QID handler
void RegisterCustomQIDHandlers()
{
    PluginQIDManager::instance().RegisterHandler(QID_CUSTOM_PLAYER_DATA, 
        [this](SQLMsg* msg) {
            return HandleCustomPlayerDataQuery(msg);
        });
}

// Handle the custom query
bool HandleCustomPlayerDataQuery(SQLMsg* msg)
{
    // Process the database result
    if (msg->Get()->uiNumRows > 0) {
        MYSQL_ROW row = mysql_fetch_row(msg->Get()->pSQLResult);
        // Process row data
        return true;
    }
    return false;
}
```

### Database Interface
Plugins can use the PluginDBInterface to perform database operations:

```cpp
// Execute a custom query
std::string query = "SELECT * FROM player_custom_data WHERE player_id = " + std::to_string(playerID);
PluginDBInterface::ExecuteQuery(query, [this](SQLMsg* msg) {
    // Handle result
});

// Escape strings for safe queries
std::string safeName = PluginDBInterface::EscapeString(playerName);
```

## Packet Handling

### Custom Packet Processing
Plugins can handle custom packets sent from the client:

```cpp
bool OnPacketReceive(DWORD playerID, LPCHARACTER ch, const void* data, size_t size)
{
    const BYTE* packet = static_cast<const BYTE*>(data);
    BYTE header = packet[0];
    
    switch (header) {
        case 0xF0: // Custom plugin command
            return HandleCustomCommand(playerID, ch, packet, size);
        case 0xF1: // Plugin configuration
            return HandleConfigPacket(playerID, ch, packet, size);
        default:
            return false; // Not handled by this plugin
    }
}
```

### Packet Utilities
Use the PluginPacketUtils for common packet operations:

```cpp
// Send a custom packet to a player
PluginPacketUtils::SendCustomPacket(ch, customData, dataSize);

// Broadcast a packet to all players
PluginPacketUtils::BroadcastPacket(customData, dataSize);

// Send packet to specific players
std::vector<LPCHARACTER> players = {ch1, ch2, ch3};
PluginPacketUtils::SendToPlayers(players, customData, dataSize);
```

## Configuration Management

### Plugin Configuration
Plugins can use a flexible configuration system:

```cpp
class MyPlugin : public IDBPlugin
{
private:
    std::map<std::string, std::string> m_config;
    
public:
    bool Initialize() override
    {
        // Load configuration
        LoadConfiguration();
        
        // Use configuration values
        bool enableFeature = GetConfigBool("enable_special_feature", true);
        int maxItems = GetConfigInt("max_items", 100);
        std::string serverName = GetConfigString("server_name", "Default");
        
        return true;
    }
    
private:
    void LoadConfiguration()
    {
        // Load from file, database, or use defaults
        m_config["enable_special_feature"] = "true";
        m_config["max_items"] = "100";
        m_config["server_name"] = "MyServer";
    }
    
    bool GetConfigBool(const std::string& key, bool defaultValue)
    {
        auto it = m_config.find(key);
        if (it != m_config.end()) {
            return it->second == "true" || it->second == "1";
        }
        return defaultValue;
    }
    
    int GetConfigInt(const std::string& key, int defaultValue)
    {
        auto it = m_config.find(key);
        if (it != m_config.end()) {
            return std::stoi(it->second);
        }
        return defaultValue;
    }
    
    std::string GetConfigString(const std::string& key, const std::string& defaultValue)
    {
        auto it = m_config.find(key);
        if (it != m_config.end()) {
            return it->second;
        }
        return defaultValue;
    }
};
```

## Best Practices

### 1. Error Handling
Always use try-catch blocks in plugin methods:

```cpp
virtual void OnPlayerLogin(DWORD playerID, const char* playerName) override
{
    try {
        // Plugin logic here
    } catch (const std::exception& e) {
        sys_err("[MyPlugin] Error in OnPlayerLogin: %s", e.what());
        // Handle error gracefully
    }
}
```

### 2. Resource Management
- Use RAII principles for resource management
- Clean up resources in the Shutdown() method
- Avoid memory leaks by properly managing dynamic allocations

### 3. Thread Safety
- Use appropriate synchronization mechanisms for shared data
- Be aware that plugin methods may be called from different threads
- Use atomic operations for simple shared variables

### 4. Performance Considerations
- Avoid blocking operations in event handlers
- Use asynchronous operations for database queries
- Cache frequently accessed data
- Profile plugin performance regularly

### 5. Logging
Use the server's logging system consistently:

```cpp
sys_log(0, "[MyPlugin] Info message");
sys_err("[MyPlugin] Error message");
```

### 6. Version Compatibility
- Always specify plugin version information
- Handle version compatibility in plugin initialization
- Document breaking changes between versions

## Troubleshooting

### Common Issues

1. **Plugin Not Loading**
   - Check that the DLL is in the correct plugins directory
   - Verify that all required dependencies are available
   - Check the server logs for loading errors

2. **Events Not Firing**
   - Ensure the plugin implements the correct interface methods
   - Verify that the plugin is properly registered
   - Check that the plugin initialization succeeded

3. **Database Errors**
   - Verify database connection settings
   - Check SQL query syntax
   - Ensure proper escaping of string values

4. **Memory Issues**
   - Check for memory leaks using debugging tools
   - Verify proper resource cleanup in Shutdown()
   - Use smart pointers where appropriate

### Debug Mode
Enable debug mode for additional logging:

```cpp
#ifdef _DEBUG
    sys_log(0, "[MyPlugin] Debug: Processing player %u", playerID);
#endif
```

### Performance Monitoring
Monitor plugin performance:

```cpp
class MyPlugin : public IDBPlugin
{
private:
    struct PluginStats {
        uint32_t eventsProcessed;
        uint32_t errorsEncountered;
        std::chrono::steady_clock::time_point startTime;
    } m_stats;
    
public:
    void LogStats()
    {
        auto now = std::chrono::steady_clock::now();
        auto uptime = std::chrono::duration_cast<std::chrono::seconds>(now - m_stats.startTime);
        
        sys_log(0, "[MyPlugin] Stats: Events=%u, Errors=%u, Uptime=%lds", 
                m_stats.eventsProcessed, m_stats.errorsEncountered, uptime.count());
    }
};
```

For more detailed information, see the API Reference and example plugins in the Examples directory.
