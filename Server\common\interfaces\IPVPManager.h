#ifndef __INC_ICPVP_MANAGER_H__
#define __INC_ICPVP_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

/**
 * @brief Pure virtual interface for CPVPManager singleton
 * 
 * Provides ABI-stable access to PVP management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on player vs player combat management.
 */
class IPVPManager
{
public:
    virtual ~IPVPManager() = default;
    
    // ============================================================================
    // PVP MANAGEMENT
    // ============================================================================
    
    // PVP operations
    virtual void Insert(LPCHARACTER pkChr, LPCHARACTER pkVictim) = 0;
    virtual void Connect(LPCHARACTER pkChr) = 0;
    virtual void Disconnect(LPCHARACTER pkChr) = 0;
    virtual void GiveUp(LPCHARACTER pkChr, DWORD dwKillerPID) = 0;

    virtual bool Dead(LPCHARACTER pkChr, DWORD dwKillerPID) = 0;
    virtual void SendList(LPDESC d) = 0;
    virtual void Process() = 0;

    virtual bool IsFighting(LPCHARACTER pkChr) = 0;
    virtual bool IsFighting(DWORD dwPID) = 0;

    virtual bool CanAttack(LPCHARACTER pkChr, LPCHARACTER pkVictim) = 0;
};

#endif // __INC_ICPVP_MANAGER_H__
