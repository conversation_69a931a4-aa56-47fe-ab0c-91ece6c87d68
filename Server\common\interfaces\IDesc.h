#ifndef __INC_IDESC_H__
#define __INC_IDESC_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
class CInputProcessor;
class CLoginKey;
struct sockaddr_in;

/**
 * @brief Pure virtual interface for DESC class
 * 
 * Provides ABI-stable access to descriptor functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on network connection and communication.
 */
class IDESC
{
public:
    virtual ~IDESC() = default;
    
    // ============================================================================
    // BASIC DESCRIPTOR PROPERTIES
    // ============================================================================
    
    // Type and lifecycle
    virtual BYTE GetType() = 0;
    virtual void Destroy() = 0;
    virtual void SetPhase(int _phase) = 0;
    
    // ============================================================================
    // CONNECTION MANAGEMENT
    // ============================================================================
    
    // Setup and connection
    virtual bool Setup(LPFDWATCH _fdw, socket_t _fd, const struct sockaddr_in& c_rSockAddr, DWORD _handle, DWORD _handshake) = 0;
    virtual void FlushOutput() = 0;
    
    // Socket information
    virtual socket_t GetSocket() const = 0;
    virtual const char* GetHostName() = 0;
    virtual WORD GetPort() = 0;
    virtual DWORD GetHandle() const = 0;
    
    // P2P information
    virtual void SetP2P(const char* h, WORD w, BYTE b) = 0;
    virtual const char* GetP2PHost() = 0;
    virtual WORD GetP2PPort() const = 0;
    virtual BYTE GetP2PChannel() const = 0;
    
    // ============================================================================
    // PACKET HANDLING
    // ============================================================================
    
    // Packet transmission
    virtual void BufferedPacket(const void* c_pvData, int iSize) = 0;
    virtual void Packet(const void* c_pvData, int iSize) = 0;
    virtual void LargePacket(const void* c_pvData, int iSize) = 0;
    virtual void ChatPacket(BYTE type, const char* format, ...) = 0;
    
    // Input/Output processing
    virtual int ProcessInput() = 0;
    virtual int ProcessOutput() = 0;
    virtual CInputProcessor* GetInputProcessor() = 0;
    virtual LPBUFFER GetOutputBuffer() = 0;
    
    // ============================================================================
    // ACCOUNT AND CHARACTER BINDING
    // ============================================================================
    
    // Account management
    virtual void BindAccountTable(TAccountTable* pTable) = 0;
    virtual TAccountTable& GetAccountTable() = 0;
    
    // Character binding
    virtual void BindCharacter(LPCHARACTER ch) = 0;
    virtual LPCHARACTER GetCharacter() = 0;
    
    // ============================================================================
    // PHASE AND STATE MANAGEMENT
    // ============================================================================
    
    // Phase checking
    virtual bool IsPhase(int phase) const = 0;
    
    // Address information
    virtual const struct sockaddr_in& GetAddr() = 0;
    virtual void UDPGrant(const struct sockaddr_in& c_rSockAddr) = 0;
    virtual const struct sockaddr_in& GetUDPAddr() = 0;
    
    // ============================================================================
    // HANDSHAKE AND SECURITY
    // ============================================================================
    
    // Handshake management
    virtual void StartHandshake(DWORD _dw) = 0;
    virtual void SendHandshake(DWORD dwCurTime, long lNewDelta) = 0;
    virtual bool HandshakeProcess(DWORD dwTime, long lDelta, bool bInfiniteRetry = false) = 0;
    virtual bool IsHandshaking() = 0;
    virtual DWORD GetHandshake() const = 0;
    virtual DWORD GetClientTime() = 0;
    
#if defined(__IMPROVED_PACKET_ENCRYPTION__)
    // Improved encryption
    virtual void SendKeyAgreement() = 0;
    virtual void SendKeyAgreementCompleted() = 0;
    virtual bool FinishHandshake(size_t agreed_length, const void* buffer, size_t length) = 0;
    virtual bool IsCipherPrepared() = 0;
#else
    // Legacy encryption
    virtual void SetSecurityKey(const DWORD* c_pdwKey) = 0;
    virtual const DWORD* GetEncryptionKey() const = 0;
    virtual const DWORD* GetDecryptionKey() const = 0;
#endif
    
    // ============================================================================
    // EMPIRE AND RELAY
    // ============================================================================
    
    // Empire information
    virtual BYTE GetEmpire() = 0;
    
    // Relay and disconnection
    virtual void SetRelay(const char* c_pszName) = 0;
    virtual bool DelayedDisconnect(int iSec) = 0;
    virtual void DisconnectOfSameLogin() = 0;
    
    // ============================================================================
    // ADMIN AND SPECIAL MODES
    // ============================================================================
    
    // Admin mode
    virtual void SetAdminMode() = 0;
    virtual bool IsAdminMode() = 0;
    
    // Pong handling
    virtual void SetPong(bool b) = 0;
    virtual bool IsPong() = 0;
    
#if defined(__SEND_SEQUENCE__)
    // Sequence handling
    virtual BYTE GetSequence() = 0;
    virtual void SetNextSequence() = 0;
#endif
    
    // ============================================================================
    // LOGIN AND AUTHENTICATION
    // ============================================================================
    
    // Login packets
    virtual void SendLoginSuccessPacket() = 0;
    
    // Login and authentication keys
    virtual void SetPanamaKey(DWORD dwKey) = 0;
    virtual DWORD GetPanamaKey() const = 0;
    virtual void SetLoginKey(DWORD dwKey) = 0;
    virtual void SetLoginKey(CLoginKey* pkKey) = 0;
    virtual DWORD GetLoginKey() = 0;
    
    // ============================================================================
    // CLIENT INFORMATION
    // ============================================================================
    
    // CRC and security
    virtual void AssembleCRCMagicCube(BYTE bProcPiece, BYTE bFilePiece) = 0;
    
    // Client version
    virtual void SetClientVersion(const char* c_pszTimestamp) = 0;
    virtual const char* GetClientVersion() = 0;
    
    // Channel status
    virtual bool isChannelStatusRequested() const = 0;
    virtual void SetChannelStatusRequested(bool bChannelStatusRequested) = 0;
    
    // ============================================================================
    // LOGIN INFORMATION
    // ============================================================================
    
    // Login management
    virtual void SetLogin(const std::string& login) = 0;
    virtual void SetLogin(const char* login) = 0;
    virtual const std::string& GetLogin() = 0;
    
    // Time management
    virtual void SetOutTime(int outtime) = 0;
    virtual void SetOffTime(int offtime) = 0;
    virtual void SetPlayTime(int playtime) = 0;
    
    // ============================================================================
    // LOGGING
    // ============================================================================
    
    // Logging functionality
    virtual void Log(const char* format, ...) = 0;

#if defined(__SEND_SEQUENCE__)
    // Sequence management
    virtual void push_seq(BYTE hdr, BYTE seq) = 0;
#endif
};

#endif // __INC_IDESC_H__
