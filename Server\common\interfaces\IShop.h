#ifndef __INC_ISHOP_H__
#define __INC_ISHOP_H__

#include "../stl.h"
#include "../tables.h"
#include "../../game/src/typedef.h"
struct SHOP_ITEM;

/**
 * @brief Pure virtual interface for CShop class
 *
 * Provides ABI-stable access to shop functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 *
 * This interface focuses on shop properties and operations.
 */
class ISHOP
{
public:
    virtual ~ISHOP() = default;


	virtual bool Create(DWORD dwVnum, DWORD dwNPCVnum, TShopItemTable* pItemTable) = 0;
	virtual void SetShopItems(TShopItemTable* pItemTable, BYTE bItemCount) = 0;

	virtual void SetPCShop(LPCHARACTER ch) = 0;
	virtual bool IsPCShop() = 0;
#if defined(__SHOPEX_RENEWAL__)
	virtual bool IsShopEx() const = 0;
#endif


	virtual bool AddGuest(LPCHARACTER ch, DWORD owner_vid, bool bOtherEmpire) = 0;
	virtual void RemoveGuest(LPCHARACTER ch) = 0;

	virtual int Buy(LPCHARACTER ch, BYTE pos
#if defined(__PRIVATESHOP_SEARCH_SYSTEM__)
					, bool bIsShopSearch = false
#endif
	) = 0;

	virtual void BroadcastUpdateItem(BYTE pos) = 0;


	virtual int GetNumberByVnum(DWORD dwVnum) = 0;


	virtual bool IsSellingItem(DWORD itemID) = 0;

	virtual DWORD GetVnum() = 0;
	virtual DWORD GetNPCVnum() = 0;

#if defined(__PRIVATESHOP_SEARCH_SYSTEM__)
	virtual LPCHARACTER GetShopOwner() = 0;
#endif

};

#endif // __INC_ISHOP_H__
