# Game Plugins CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# =================================================================
# CROSS COMPILER GAME PLUGIN
# =================================================================

create_plugin_library(CrossCompilerGamePlugin "Game"
    SOURCES ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/CrossCompilerGamePlugin.cpp
    HEADERS ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/CrossCompilerGamePlugin.h
)

# =================================================================
# SIMPLE GAME PLUGIN
# =================================================================

create_plugin_library(SimpleGamePlugin "Game"
    SOURCES ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/SimpleGamePlugin.cpp
    HEADERS ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/SimpleGamePlugin.h
)

# =================================================================
# COMPREHENSIVE GAME PLUGIN
# =================================================================

create_plugin_library(ComprehensiveGamePlugin "Game"
    SOURCES
        ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/ComprehensiveGamePlugin.cpp
        ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/ComprehensiveGamePlugin_Helpers.cpp
    HEADERS ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/ComprehensiveGamePlugin.h
)

# =================================================================
# MINIMAL GAME PLUGIN
# =================================================================

create_plugin_library(MinimalGamePlugin "Game"
    SOURCES ${CMAKE_SOURCE_DIR}/External/PluginTemplate/PluginExample/MinimalGamePlugin.cpp
)

# =================================================================
# PLUGIN CONFIGURATION FILES
# =================================================================

# Create plugin configuration files
file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/plugins/config)

# Create configuration for CrossCompilerGamePlugin
file(WRITE ${CMAKE_SOURCE_DIR}/plugins/config/CrossCompilerGamePlugin.json
"{
    \"name\": \"CrossCompilerGamePlugin\",
    \"version\": \"1.0.0\",
    \"enabled\": true,
    \"settings\": {
        \"enableCrossCompilerFeatures\": true,
        \"enableCompatibilityMode\": true,
        \"enableDetailedLogging\": true
    }
}")

# Create configuration for SimpleGamePlugin
file(WRITE ${CMAKE_SOURCE_DIR}/plugins/config/SimpleGamePlugin.json
"{
    \"name\": \"SimpleGamePlugin\",
    \"version\": \"1.0.0\",
    \"enabled\": true,
    \"settings\": {
        \"enableBasicFeatures\": true,
        \"enableLogging\": true
    }
}")

# Create configuration for ComprehensiveGamePlugin
file(WRITE ${CMAKE_SOURCE_DIR}/plugins/config/ComprehensiveGamePlugin.json
"{
    \"name\": \"ComprehensiveGamePlugin\",
    \"version\": \"1.0.0\",
    \"enabled\": true,
    \"settings\": {
        \"enableCharacterTracking\": true,
        \"enableItemTracking\": true,
        \"enableCombatTracking\": true,
        \"enableGuildTracking\": true,
        \"enableShopTracking\": true,
        \"enableQuestTracking\": true,
        \"enableChatTracking\": true,
        \"enablePacketTracking\": false,
        \"enableCommandTracking\": true,
        \"enableDetailedLogging\": true
    }
}")

# Create configuration for MinimalGamePlugin
file(WRITE ${CMAKE_SOURCE_DIR}/plugins/config/MinimalGamePlugin.json
"{
    \"name\": \"MinimalGamePlugin\",
    \"version\": \"1.0.0\",
    \"enabled\": true,
    \"settings\": {
        \"enableMinimalFeatures\": true
    }
}")

# =================================================================
# INSTALLATION
# =================================================================

# Install plugins to the plugins directory
install(TARGETS 
    CrossCompilerGamePlugin
    SimpleGamePlugin
    ComprehensiveGamePlugin
    MinimalGamePlugin
    RUNTIME DESTINATION plugins/game
    LIBRARY DESTINATION plugins/game
)

# Install configuration files
install(FILES
    ${CMAKE_SOURCE_DIR}/plugins/config/CrossCompilerGamePlugin.json
    ${CMAKE_SOURCE_DIR}/plugins/config/SimpleGamePlugin.json
    ${CMAKE_SOURCE_DIR}/plugins/config/ComprehensiveGamePlugin.json
    ${CMAKE_SOURCE_DIR}/plugins/config/MinimalGamePlugin.json
    DESTINATION plugins/config
)
