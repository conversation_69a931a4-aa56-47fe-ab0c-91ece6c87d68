#ifndef __INC_ICWAR_MAP_H__
#define __INC_ICWAR_MAP_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
struct TGuildWarInfo;
struct TWarMapInfo;
class CGuild;
class LPEVENT;

/**
 * @brief Pure virtual interface for CWarMap class
 * 
 * Provides ABI-stable access to war map functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on individual guild war map management.
 */
class ICWarMap
{
public:
    virtual ~ICWarMap() = default;
    
    // ============================================================================
    // TEAM MANAGEMENT
    // ============================================================================
    
    // Team operations
    virtual bool GetTeamIndex(DWORD dwGuild, BYTE& bIdx) = 0;
    
    // ============================================================================
    // MEMBER MANAGEMENT
    // ============================================================================
    
    // Member operations
    virtual void IncMember(LPCHARACTER ch) = 0;
    virtual void DecMember(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // GUILD INFORMATION
    // ============================================================================
    
    // Guild operations
    virtual CGuild* GetGuild(BYTE bIdx) = 0;
    virtual DWORD GetGuildID(BYTE bIdx) = 0;
    
    // ============================================================================
    // MAP INFORMATION
    // ============================================================================
    
    // Map properties
    virtual BYTE GetType() = 0;
    virtual long GetMapIndex() = 0;
    virtual DWORD GetGuildOpponent(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // WAR RESULTS
    // ============================================================================
    
    // War outcome
    virtual DWORD GetWinnerGuild() = 0;
    virtual void UsePotion(LPCHARACTER ch, LPITEM item) = 0;
    
    // ============================================================================
    // WAR STATE MANAGEMENT
    // ============================================================================
    
    // War state operations
    virtual void Draw() = 0;
    virtual void Timeout() = 0;
    virtual void CheckWarEnd() = 0;
    virtual bool SetEnded() = 0;
    virtual void ExitAll() = 0;
    
    // ============================================================================
    // EVENT MANAGEMENT
    // ============================================================================
    
    // Event operations
    virtual void SetBeginEvent(LPEVENT pkEv) = 0;
    virtual void SetTimeoutEvent(LPEVENT pkEv) = 0;
    virtual void SetEndEvent(LPEVENT pkEv) = 0;
    virtual void SetResetFlagEvent(LPEVENT pkEv) = 0;
    
    // ============================================================================
    // SCORE MANAGEMENT
    // ============================================================================
    
    // Score operations
    virtual void UpdateScore(DWORD g1, int score1, DWORD g2, int score2) = 0;
    virtual bool CheckScore() = 0;
    
    // ============================================================================
    // REWARD SYSTEM
    // ============================================================================
    
    // Reward operations
    virtual int GetRewardGold(BYTE bWinnerIdx) = 0;
    
    // ============================================================================
    // GUILD INDEX OPERATIONS
    // ============================================================================
    
    // Guild index operations
    virtual bool GetGuildIndex(DWORD dwGuild, int& iIndex) = 0;
    
    // ============================================================================
    // COMMUNICATION
    // ============================================================================
    
    // Communication operations
    virtual void Packet(const void* pv, int size) = 0;
    virtual void Notice(const char* psz) = 0;
    virtual void SendWarPacket(LPDESC d) = 0;
    virtual void SendScorePacket(BYTE bIdx, LPDESC d = NULL) = 0;
    
    // ============================================================================
    // COMBAT OPERATIONS
    // ============================================================================
    
    // Combat operations
    virtual void OnKill(LPCHARACTER killer, LPCHARACTER ch) = 0;
    
    // ============================================================================
    // FLAG SYSTEM
    // ============================================================================
    
    // Flag operations
    virtual void AddFlag(BYTE bIdx, DWORD x = 0, DWORD y = 0) = 0;
    virtual void AddFlagBase(BYTE bIdx, DWORD x = 0, DWORD y = 0) = 0;
    virtual void RemoveFlag(BYTE bIdx) = 0;
    virtual bool IsFlagOnBase(BYTE bIdx) = 0;
    virtual void ResetFlag() = 0;
};

#endif // __INC_ICWAR_MAP_H__
