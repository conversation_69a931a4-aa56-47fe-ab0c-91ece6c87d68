#include "CrossCompilerGamePlugin.h"
#include <cstring>
#include <cstdio>
#include <cstdarg>

// Simple logging macros that don't depend on external functions
#define PLUGIN_LOG(msg) printf("[CrossCompilerGamePlugin] %s\n", msg)
#define PLUGIN_LOG_FMT(fmt, ...) printf("[CrossCompilerGamePlugin] " fmt "\n", __VA_ARGS__)

// ============================================================================
// PLUGIN INITIALIZATION USING INTERFACE FACTORY
// ============================================================================

CrossCompilerGamePlugin::CrossCompilerGamePlugin()
    : m_initialized(false)
    , m_running(false)
    , m_debugMode(true)
    , m_itemManager(nullptr)
    , m_characterManager(nullptr)
    , m_guildManager(nullptr)
    , m_shopManager(nullptr)
    , m_descManager(nullptr)
    , m_sectreeManager(nullptr)
    , m_gameUtils(nullptr)
{
    // Initialize plugin info
    m_info.name = "CrossCompilerGamePlugin";
    m_info.version = PluginVersion(1, 0, 0);
    m_info.description = "Cross-compiler compatible game plugin using pure virtual interfaces";
    m_info.author = "Plugin System Developer";

    m_state = PluginState::PLUGIN_UNLOADED;

    // Initialize statistics
    memset(&m_stats, 0, sizeof(m_stats));


	PLUGIN_LOG("Constructor called - factory received");
    // Note: When factory is null, we operate in basic mode without logging
}

CrossCompilerGamePlugin::~CrossCompilerGamePlugin()
{
    if (m_running)
        Stop();
    if (m_initialized)
        Shutdown();

    PLUGIN_LOG("Destructor called");
}

bool CrossCompilerGamePlugin::Initialize()
{
    if (m_initialized)
        return true;

	PLUGIN_LOG("Initialize() called");

    // Simple initialization without factory dependency
    m_state = PluginState::PLUGIN_INITIALIZED;
    m_initialized = true;

    PLUGIN_LOG("Plugin initialized successfully");
    return true;
}

bool CrossCompilerGamePlugin::Start()
{
    if (!m_initialized)
    {
        PLUGIN_LOG("ERROR: Cannot start - plugin not initialized");
        return false;
    }

    if (m_running)
        return true;

    PLUGIN_LOG("Start() called");

    m_state = PluginState::PLUGIN_RUNNING;
    m_running = true;

    PLUGIN_LOG("Plugin started successfully with ABI-stable interfaces");

    return true;
}

void CrossCompilerGamePlugin::Stop()
{
    if (!m_running)
        return;
    
    PLUGIN_LOG("Stop() called");
    
    m_running = false;
    m_state = PluginState::PLUGIN_STOPPED;
    
    PLUGIN_LOG("Plugin stopped");
}

void CrossCompilerGamePlugin::Shutdown()
{
    if (!m_initialized)
        return;
    
    PLUGIN_LOG("Shutdown() called");
    
    if (m_running)
        Stop();
    
    // Save final statistics
    SaveStatistics();
    
    m_initialized = false;
    m_state = PluginState::PLUGIN_UNLOADED;
    
    PLUGIN_LOG("Plugin shutdown complete");
}

const PluginInfo& CrossCompilerGamePlugin::GetInfo() const
{
    return m_info;
}

PluginState CrossCompilerGamePlugin::GetState() const
{
    return m_state;
}

void CrossCompilerGamePlugin::SetState(PluginState state)
{
    m_state = state;
    m_info.state = state;
}
// ============================================================================
// CHARACTER EVENT IMPLEMENTATIONS - DEMONSTRATING ICHARACTER INTERFACE
// ============================================================================

void CrossCompilerGamePlugin::OnCharacterCreate(ICHARACTER* ch)
{
    if (!m_running || !ch)
        return;

    m_stats.charactersCreated++;

    PLUGIN_LOG_FMT("Character created: %s", ch->GetName());

    // Send welcome message using ICHARACTER interface
    ch->ChatPacket(0, "[Plugin] Welcome! CrossCompilerGamePlugin is active with ABI-stable interfaces!");
}

void CrossCompilerGamePlugin::OnCharacterDestroy(ICHARACTER* ch)
{
    if (!m_running || !ch)
        return;
    
    PLUGIN_LOG("Character destroyed: %s", GetCharacterInfo(ch));
    
    LogEvent("CHARACTER_DESTROY", GetCharacterInfo(ch));
}

void CrossCompilerGamePlugin::OnCharacterLogin(ICHARACTER* ch)
{
    if (!m_running || !ch)
    {
        PLUGIN_LOG("error character pointer is null.");
        return;
    }
    
    PLUGIN_LOG("Character login: %s", GetCharacterInfo(ch));
    printf("Character login: %s\n", GetCharacterInfo(ch));
    // Demonstrate full ICHARACTER interface access
    DemonstrateCharacterAccess(ch);
    
    // Show player information using interface methods
    char buffer[512];
    snprintf(buffer, sizeof(buffer), 
        "Login detected! Your info: Name=%s, Level=%d, Gold=%lld, HP=%d/%d",
        ch->GetName(), ch->GetLevel(), ch->GetGold(), ch->GetHP(), ch->GetMaxHP());
    SendMessage(ch, buffer);
    ch->ChatPacket(1, "Hello %s from the plugin: %s.", ch->GetName(), GetInfo().name.c_str());
    // Check for milestone levels and give rewards
    int level = ch->GetLevel();
    if (level >= 50 && level % 10 == 0)
    {
        // Schedule delayed reward using interface factory
        ScheduleDelayedReward(ch, 50300, 5000); // 5 second delay
    }
    
    LogEvent("CHARACTER_LOGIN", GetCharacterInfo(ch));
}

void CrossCompilerGamePlugin::OnCharacterLogout(ICHARACTER* ch)
{
    if (!m_running || !ch)
        return;
    
    PLUGIN_LOG("Character logout: %s", GetCharacterInfo(ch));
    
    LogEvent("CHARACTER_LOGOUT", GetCharacterInfo(ch));
}

void CrossCompilerGamePlugin::OnCharacterLevelUp(ICHARACTER* ch, PLUGIN_BYTE newLevel)
{
    if (!m_running || !ch)
        return;
    
    PLUGIN_LOG("Character %s leveled up to %d", ch->GetName(), newLevel);
    
    // Congratulate on milestone levels
    if (newLevel % 10 == 0)
    {
        char message[256];
        snprintf(message, sizeof(message), "Congratulations on reaching level %d!", newLevel);
        SendMessage(ch, message);
        
        // Give milestone reward using IITEM_MANAGER interface
        IITEM* reward = m_itemManager->CreateItem(50300, 1); // Example item
        if (reward && reward)
        {
            if (ch->AutoGiveItem(reward))
            {
                SendMessage(ch, "You received a milestone reward!");
                m_stats.itemsGiven++;
            }
            else
            {
                // If can't give item, destroy it to prevent memory leak
                m_itemManager->DestroyItem(reward);
            }
        }
    }
    
    char details[128];
    snprintf(details, sizeof(details), "%s -> Level %d", GetCharacterInfo(ch), newLevel);
    LogEvent("CHARACTER_LEVELUP", details);
}

void CrossCompilerGamePlugin::OnCharacterDead(ICHARACTER* ch, ICHARACTER* killer)
{
    if (!m_running || !ch)
        return;
    
    const char* killerInfo = (killer) ? GetCharacterInfo(killer) : "Unknown";
    
    if (killer && killer)
    {
        PLUGIN_LOG("%s was killed by %s", ch->GetName(), killer->GetName());
    }
    else
    {
        PLUGIN_LOG("%s died", ch->GetName());
    }
    
    char details[256];
    snprintf(details, sizeof(details), "%s killed by %s", GetCharacterInfo(ch), killerInfo);
    LogEvent("CHARACTER_DEATH", details);
}

void CrossCompilerGamePlugin::OnCharacterRevive(ICHARACTER* ch)
{
    if (!m_running || !ch)
        return;
    
    PLUGIN_LOG("Character revived: %s", ch->GetName());
    
    SendMessage(ch, "Welcome back to life! Plugin interfaces working perfectly!");
    
    LogEvent("CHARACTER_REVIVE", GetCharacterInfo(ch));
}

// ============================================================================
// ITEM EVENT IMPLEMENTATIONS - DEMONSTRATING IITEM INTERFACE
// ============================================================================

void CrossCompilerGamePlugin::OnItemCreate(IITEM* item)
{
    if (!m_running || !item)
        return;
    
    m_stats.itemsCreated++;
    
    PLUGIN_LOG("Item created: %s", GetItemInfo(item));
    
    // Demonstrate IITEM interface access
    DemonstrateItemAccess(item);
    
    LogEvent("ITEM_CREATE", GetItemInfo(item));
}

void CrossCompilerGamePlugin::OnItemDestroy(IITEM* item)
{
    if (!m_running || !item)
        return;
    
    PLUGIN_LOG("Item destroyed: %s", GetItemInfo(item));
    
    LogEvent("ITEM_DESTROY", GetItemInfo(item));
}

void CrossCompilerGamePlugin::OnItemEquip(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !ch || !item)
        return;
    
    PLUGIN_LOG("%s equipped %s", ch->GetName(), GetItemInfo(item));
    
    char message[256];
    snprintf(message, sizeof(message), "Item equipped: %s", GetItemInfo(item));
    SendMessage(ch, message);
    
    char details[512];
    snprintf(details, sizeof(details), "%s equipped %s", GetCharacterInfo(ch), GetItemInfo(item));
    LogEvent("ITEM_EQUIP", details);
}

void CrossCompilerGamePlugin::OnItemUnequip(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !ch || !item)
        return;
    
    PLUGIN_LOG("%s unequipped %s", ch->GetName(), GetItemInfo(item));
    
    char message[256];
    snprintf(message, sizeof(message), "Item unequipped: %s", GetItemInfo(item));
    SendMessage(ch, message);
    
    char details[512];
    snprintf(details, sizeof(details), "%s unequipped %s", GetCharacterInfo(ch), GetItemInfo(item));
    LogEvent("ITEM_UNEQUIP", details);
}

void CrossCompilerGamePlugin::OnItemUse(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !ch || !item)
        return;
    
    PLUGIN_LOG("%s used %s", ch->GetName(), GetItemInfo(item));
    
    char message[256];
    snprintf(message, sizeof(message), "Item used: %s", GetItemInfo(item));
    SendMessage(ch, message);
    
    char details[512];
    snprintf(details, sizeof(details), "%s used %s", GetCharacterInfo(ch), GetItemInfo(item));
    LogEvent("ITEM_USE", details);
}

void CrossCompilerGamePlugin::OnItemDrop(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !ch || !item)
        return;

    char details[512];
    snprintf(details, sizeof(details), "%s dropped %s", GetCharacterInfo(ch), GetItemInfo(item));
    LogEvent("ITEM_DROP", details);
}

void CrossCompilerGamePlugin::OnItemPickup(ICHARACTER* ch, IITEM* item)
{
    if (!m_running || !ch || !item)
        return;

    char details[512];
    snprintf(details, sizeof(details), "%s picked up %s", GetCharacterInfo(ch), GetItemInfo(item));
    LogEvent("ITEM_PICKUP", details);
}

// ============================================================================
// COMBAT EVENT IMPLEMENTATIONS
// ============================================================================

void CrossCompilerGamePlugin::OnAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage)
{
    if (!m_running || !attacker || !victim)
        return;

    m_stats.combatEvents++;

    PLUGIN_LOG("%s attacked %s for %d damage",
                         attacker->GetName(), victim->GetName(), damage);

    char details[256];
    snprintf(details, sizeof(details), "%s attacked %s (%d damage)",
             GetCharacterInfo(attacker), GetCharacterInfo(victim), damage);
    LogEvent("ATTACK", details);
}

void CrossCompilerGamePlugin::OnKill(ICHARACTER* killer, ICHARACTER* victim)
{
    if (!m_running || !killer || !victim)
        return;

    PLUGIN_LOG("%s killed %s",
                         killer->GetName(), victim->GetName());

    char details[256];
    snprintf(details, sizeof(details), "%s killed %s",
             GetCharacterInfo(killer), GetCharacterInfo(victim));
    LogEvent("KILL", details);
}

void CrossCompilerGamePlugin::OnDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage)
{
    if (!m_running || !victim || !attacker)
        return;

    char details[256];
    snprintf(details, sizeof(details), "%s took %d damage from %s",
             GetCharacterInfo(victim), damage, GetCharacterInfo(attacker));
    LogEvent("DAMAGE", details);
}

// ============================================================================
// COMMAND HANDLING - DEMONSTRATING INTERFACE USAGE
// ============================================================================

bool CrossCompilerGamePlugin::OnCommand(ICHARACTER* ch, const char* command, const char* args)
{
    if (!m_running || !ch || !command)
        return false;

    m_stats.commandsProcessed++;

    // Convert to lowercase for case-insensitive comparison
    char cmd[64];
    strncpy(cmd, command, sizeof(cmd) - 1);
    cmd[sizeof(cmd) - 1] = '\0';
    for (char* p = cmd; *p; ++p)
        *p = tolower(*p);

    const char* arguments = args ? args : "";

    if (strcmp(cmd, "plugininfo") == 0)
    {
        return HandleInfoCommand(ch, arguments);
    }
    else if (strcmp(cmd, "pluginstats") == 0)
    {
        return HandleStatsCommand(ch, arguments);
    }
    else if (strcmp(cmd, "plugintest") == 0)
    {
        return HandleTestCommand(ch, arguments);
    }
    else if (strcmp(cmd, "pluginitem") == 0)
    {
        return HandleItemCommand(ch, arguments);
    }
    else if (strcmp(cmd, "pluginguild") == 0)
    {
        return HandleGuildCommand(ch, arguments);
    }
    else if (strcmp(cmd, "pluginreward") == 0)
    {
        return HandleRewardCommand(ch, arguments);
    }
    else if (strcmp(cmd, "plugindebug") == 0)
    {
        return HandleDebugCommand(ch, arguments);
    }

    return false; // Command not handled by this plugin
}

// ============================================================================
// SYSTEM EVENTS
// ============================================================================

void CrossCompilerGamePlugin::OnServerStart()
{
    PLUGIN_LOG("Server started");
}

void CrossCompilerGamePlugin::OnServerShutdown()
{
    PLUGIN_LOG("Server shutting down");
}

void CrossCompilerGamePlugin::OnHeartbeat()
{
    // Called every heartbeat - don't log to avoid spam
}

void CrossCompilerGamePlugin::OnMinuteUpdate()
{
    if (m_debugMode)
        PLUGIN_LOG("Minute update");
}

void CrossCompilerGamePlugin::OnHourUpdate()
{
    if (m_debugMode)
        PLUGIN_LOG("Hour update");
}

void CrossCompilerGamePlugin::OnDayUpdate()
{
    if (m_debugMode)
        PLUGIN_LOG("Day update");
}

// ============================================================================
// HELPER METHODS - DEMONSTRATING INTERFACE USAGE
// ============================================================================

void CrossCompilerGamePlugin::InitializeInterfaces()
{


    PLUGIN_LOG("All interfaces initialized successfully");
}

void CrossCompilerGamePlugin::DemonstrateCharacterAccess(ICHARACTER* ch)
{
    if (!ch)
        return;

    PLUGIN_LOG("Demonstrating ICHARACTER interface:");
    PLUGIN_LOG("  Name: %s", ch->GetName());
    PLUGIN_LOG("  Player ID: %u", ch->GetPlayerID());
    PLUGIN_LOG("  Level: %d", ch->GetLevel());
    PLUGIN_LOG("  Job: %d", ch->GetJob());
    PLUGIN_LOG("  Race: %d", ch->GetRaceNum());
    PLUGIN_LOG("  Empire: %d", ch->GetEmpire());
    PLUGIN_LOG("  Position: (%ld, %ld)", ch->GetX(), ch->GetY());
    PLUGIN_LOG("  Map Index: %ld", ch->GetMapIndex());
    PLUGIN_LOG("  HP: %d/%d", ch->GetHP(), ch->GetMaxHP());
    PLUGIN_LOG("  SP: %d/%d", ch->GetSP(), ch->GetMaxSP());
    PLUGIN_LOG("  Gold: %lld", ch->GetGold());
    PLUGIN_LOG("  Experience: %lld", ch->GetExp());
    PLUGIN_LOG("  Is PC: %s", ch->IsPC() ? "Yes" : "No");
    PLUGIN_LOG("  Is GM: %s", ch->IsGM() ? "Yes" : "No");

    // Demonstrate guild access through character
    IGUILD* guild = ch->GetGuild();
    if (guild)
    {
        PLUGIN_LOG("  Guild: %s (ID: %u)", guild->GetName(), guild->GetID());
    }
    else
    {
        PLUGIN_LOG("  Guild: None");
    }
}

void CrossCompilerGamePlugin::DemonstrateItemAccess(IITEM* item)
{
    if (!item)
        return;

    PLUGIN_LOG("Demonstrating IITEM interface:");
    PLUGIN_LOG("  Name: %s", item->GetName());
    PLUGIN_LOG("  Vnum: %u", item->GetVnum());
    PLUGIN_LOG("  Type: %d", item->GetType());
    PLUGIN_LOG("  SubType: %d", item->GetSubType());
    PLUGIN_LOG("  Count: %u", item->GetCount());
    PLUGIN_LOG("  Level: %d", item->GetLevel());
    PLUGIN_LOG("  Refine: %d", item->GetRefineLevel());
    PLUGIN_LOG("  Is Equipped: %s", item->IsEquipped() ? "Yes" : "No");

    // Demonstrate owner access
    ICHARACTER* owner = item->GetOwner();
    if (owner && owner)
    {
        PLUGIN_LOG("  Owner: %s", owner->GetName());
    }
    else
    {
        PLUGIN_LOG("  Owner: None");
    }
}

void CrossCompilerGamePlugin::DemonstrateManagerAccess()
{
    PLUGIN_LOG("Demonstrating manager interfaces:");

    // CHARACTER_MANAGER access
    int playerCount = m_characterManager->GetPlayerCount();
    PLUGIN_LOG("  CHARACTER_MANAGER: Online players = %d", playerCount);

    // ITEM_MANAGER access
    int itemCount = m_itemManager->GetItemCount();
    PLUGIN_LOG("  ITEM_MANAGER: Total items = %d", itemCount);

    // DESC_MANAGER access
    int clientCount = m_descManager->GetClientCount();
    PLUGIN_LOG("  DESC_MANAGER: Connected clients = %d", clientCount);

    // GUILD_MANAGER access
    int guildCount = m_guildManager->GetGuildCount();
    PLUGIN_LOG("  GUILD_MANAGER: Total guilds = %d", guildCount);

    // SHOP_MANAGER access
    int shopCount = m_shopManager->GetShopCount();
    PLUGIN_LOG("  SHOP_MANAGER: Active shops = %d", shopCount);

    PLUGIN_LOG("All managers accessible through interfaces!");
}

void CrossCompilerGamePlugin::SendMessage(ICHARACTER* ch, const char* message)
{
    if (!message)
        return;

    ch->ChatPacket(0, "[Plugin] %s", message); // 0 = CHAT_TYPE_INFO
}

const char* CrossCompilerGamePlugin::GetCharacterInfo(ICHARACTER* ch)
{
    static char buffer[256];
    if (!ch)
    {
        strcpy(buffer, "Invalid Character");
        return buffer;
    }

    snprintf(buffer, sizeof(buffer), "%s (ID:%u, Level:%d, Job:%d)",
             ch->GetName(), ch->GetPlayerID(), ch->GetLevel(), ch->GetJob());
    return buffer;
}

const char* CrossCompilerGamePlugin::GetItemInfo(IITEM* item)
{
    static char buffer[256];
    if (!item)
    {
        strcpy(buffer, "Invalid Item");
        return buffer;
    }

    snprintf(buffer, sizeof(buffer), "%s (Vnum:%u, Count:%u, Level:%d)",
             item->GetName(), item->GetVnum(), item->GetCount(), item->GetLevel());
    return buffer;
}


void CrossCompilerGamePlugin::LogEvent(const char* event, const char* details)
{
    PLUGIN_LOG("Event: %s - %s", event, details ? details : "");
}

void CrossCompilerGamePlugin::LoadConfiguration()
{
    //m_debugMode = m_factory->GetConfigInt("plugin.debug", 1) != 0;
    PLUGIN_LOG("Configuration loaded - Debug mode: %s", m_debugMode ? "ON" : "OFF");
}

void CrossCompilerGamePlugin::SaveStatistics()
{
    PLUGIN_LOG("Final Statistics:");
    PLUGIN_LOG("  Characters Created: %d", m_stats.charactersCreated);
    PLUGIN_LOG("  Items Created: %d", m_stats.itemsCreated);
    PLUGIN_LOG("  Combat Events: %d", m_stats.combatEvents);
    PLUGIN_LOG("  Quests Completed: %d", m_stats.questsCompleted);
    PLUGIN_LOG("  Commands Processed: %d", m_stats.commandsProcessed);
    PLUGIN_LOG("  Items Given: %u", m_stats.itemsGiven);
    PLUGIN_LOG("  Gold Given: %u", m_stats.goldGiven);
}

// ============================================================================
// REMAINING EVENT IMPLEMENTATIONS (Simplified for demonstration)
// ============================================================================

void CrossCompilerGamePlugin::OnGuildCreate(IGUILD* guild)
{
    if (!m_running || !guild)
        return;

    PLUGIN_LOG("Guild created: %s (ID: %u)", guild->GetName(), guild->GetID());
    LogEvent("GUILD_CREATE", GetGuildInfo(guild));
}

void CrossCompilerGamePlugin::OnGuildDestroy(IGUILD* guild)
{
    if (!m_running || !guild)
        return;

    PLUGIN_LOG("Guild destroyed: %s", guild->GetName());
    LogEvent("GUILD_DESTROY", GetGuildInfo(guild));
}

void CrossCompilerGamePlugin::OnGuildJoin(ICHARACTER* ch, IGUILD* guild)
{
    if (!m_running || !ch || !guild)
        return;

    PLUGIN_LOG("%s joined guild %s", ch->GetName(), guild->GetName());

    char details[256];
    snprintf(details, sizeof(details), "%s joined %s", GetCharacterInfo(ch), GetGuildInfo(guild));
    LogEvent("GUILD_JOIN", details);
}

void CrossCompilerGamePlugin::OnGuildLeave(ICHARACTER* ch, IGUILD* guild)
{
    if (!m_running || !ch || !guild)
        return;

    PLUGIN_LOG("%s left guild %s", ch->GetName(), guild->GetName());

    char details[256];
    snprintf(details, sizeof(details), "%s left %s", GetCharacterInfo(ch), GetGuildInfo(guild));
    LogEvent("GUILD_LEAVE", details);
}

void CrossCompilerGamePlugin::OnGuildWar(IGUILD* guild1, IGUILD* guild2)
{
    if (!m_running || !guild1 || !guild2)
        return;

    PLUGIN_LOG("Guild war: %s vs %s", guild1->GetName(), guild2->GetName());

    char details[256];
    snprintf(details, sizeof(details), "%s vs %s", GetGuildInfo(guild1), GetGuildInfo(guild2));
    LogEvent("GUILD_WAR", details);
}

void CrossCompilerGamePlugin::OnShopBuy(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count)
{
    if (!m_running || !ch || !item)
        return;

    PLUGIN_LOG("%s bought %s x%d", ch->GetName(), item->GetName(), count);

    char details[256];
    snprintf(details, sizeof(details), "%s bought %s x%d", GetCharacterInfo(ch), GetItemInfo(item), count);
    LogEvent("SHOP_BUY", details);
}

void CrossCompilerGamePlugin::OnShopSell(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count)
{
    if (!m_running || !ch || !item)
        return;

    PLUGIN_LOG("%s sold %s x%d", ch->GetName(), item->GetName(), count);

    char details[256];
    snprintf(details, sizeof(details), "%s sold %s x%d", GetCharacterInfo(ch), GetItemInfo(item), count);
    LogEvent("SHOP_SELL", details);
}

void CrossCompilerGamePlugin::OnQuestStart(ICHARACTER* ch, int questIndex)
{
    if (!m_running || !ch)
        return;

    PLUGIN_LOG("%s started quest %d", ch->GetName(), questIndex);

    char details[128];
    snprintf(details, sizeof(details), "%s started quest %d", GetCharacterInfo(ch), questIndex);
    LogEvent("QUEST_START", details);
}

void CrossCompilerGamePlugin::OnQuestComplete(ICHARACTER* ch, int questIndex)
{
    if (!m_running || !ch)
        return;

    m_stats.questsCompleted++;

    PLUGIN_LOG("%s completed quest %d", ch->GetName(), questIndex);

    char details[128];
    snprintf(details, sizeof(details), "%s completed quest %d", GetCharacterInfo(ch), questIndex);
    LogEvent("QUEST_COMPLETE", details);
}

void CrossCompilerGamePlugin::OnQuestGiveUp(ICHARACTER* ch, int questIndex)
{
    if (!m_running || !ch)
        return;

    PLUGIN_LOG("%s gave up quest %d", ch->GetName(), questIndex);

    char details[128];
    snprintf(details, sizeof(details), "%s gave up quest %d", GetCharacterInfo(ch), questIndex);
    LogEvent("QUEST_GIVEUP", details);
}

void CrossCompilerGamePlugin::OnChat(ICHARACTER* ch, const char* message, int type)
{
    if (!m_running || !ch || !message)
        return;

    PLUGIN_LOG("%s chat (type %d): %s", ch->GetName(), type, message);
}

void CrossCompilerGamePlugin::OnWhisper(ICHARACTER* from, ICHARACTER* to, const char* message)
{
    if (!m_running || !from || !to || !message)
        return;

    PLUGIN_LOG("%s whispered to %s: %s", from->GetName(), to->GetName(), message);
}

void CrossCompilerGamePlugin::OnShout(ICHARACTER* ch, const char* message)
{
    if (!m_running || !ch || !message)
        return;

    PLUGIN_LOG("%s shouted: %s", ch->GetName(), message);
}

void CrossCompilerGamePlugin::OnMapEnter(ICHARACTER* ch, PLUGIN_LONG mapIndex)
{
    if (!m_running || !ch)
        return;

    PLUGIN_LOG("%s entered map %ld", ch->GetName(), mapIndex);

    char details[128];
    snprintf(details, sizeof(details), "%s entered map %ld", GetCharacterInfo(ch), mapIndex);
    LogEvent("MAP_ENTER", details);
}

void CrossCompilerGamePlugin::OnMapLeave(ICHARACTER* ch, PLUGIN_LONG mapIndex)
{
    if (!m_running || !ch)
        return;

    PLUGIN_LOG("%s left map %ld", ch->GetName(), mapIndex);

    char details[128];
    snprintf(details, sizeof(details), "%s left map %ld", GetCharacterInfo(ch), mapIndex);
    LogEvent("MAP_LEAVE", details);
}

const char* CrossCompilerGamePlugin::GetGuildInfo(IGUILD* guild)
{
    static char buffer[256];
    if (!guild)
    {
        strcpy(buffer, "Invalid Guild");
        return buffer;
    }

    snprintf(buffer, sizeof(buffer), "%s (ID:%u, Level:%d, Members:%d)",
             guild->GetName(), guild->GetID(), guild->GetLevel(), guild->GetMemberCount());
    return buffer;
}

// ============================================================================
// PLUGIN FACTORY FUNCTIONS USING NEW INTERFACE SYSTEM
// ============================================================================



// ============================================================================
// COMMAND HANDLERS - DEMONSTRATING INTERFACE USAGE
// ============================================================================

bool CrossCompilerGamePlugin::HandleInfoCommand(ICHARACTER* ch, const char* args)
{
    SendMessage(ch, "=== CrossCompilerGamePlugin Info ===");
    SendMessage(ch, "Version: 1.0.0");
    SendMessage(ch, "Author: Plugin System Developer");
    SendMessage(ch, "Description: Cross-compiler compatible plugin using pure virtual interfaces");
    SendMessage(ch, "ABI Compatibility: Clang game server + GCC plugin = WORKING!");

    char buffer[256];
    snprintf(buffer, sizeof(buffer), "Plugin State: %s",
             m_running ? "RUNNING" : "STOPPED");
    SendMessage(ch, buffer);

    return true;
}

bool CrossCompilerGamePlugin::HandleStatsCommand(ICHARACTER* ch, const char* args)
{
    SendMessage(ch, "=== Plugin Statistics ===");

    char buffer[256];
    snprintf(buffer, sizeof(buffer), "Characters Created: %d", m_stats.charactersCreated);
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Items Created: %d", m_stats.itemsCreated);
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Combat Events: %d", m_stats.combatEvents);
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Quests Completed: %d", m_stats.questsCompleted);
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Commands Processed: %d", m_stats.commandsProcessed);
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Items Given: %u", m_stats.itemsGiven);
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Gold Given: %u", m_stats.goldGiven);
    SendMessage(ch, buffer);

    return true;
}

bool CrossCompilerGamePlugin::HandleTestCommand(ICHARACTER* ch, const char* args)
{
    SendMessage(ch, "=== Interface Test ===");

    // Test character interface
    DemonstrateCharacterAccess(ch);
    SendMessage(ch, "Character interface test completed!");

    // Test manager interfaces
    DemonstrateManagerAccess();
    SendMessage(ch, "Manager interface test completed!");

    // Test item creation through interface
    IITEM* testItem = m_itemManager->CreateItem(50300, 1);
    if (testItem)
    {
        DemonstrateItemAccess(testItem);
        SendMessage(ch, "Item interface test completed!");

        // Give item to player or destroy it
        if (!ch->AutoGiveItem(testItem))
        {
            m_itemManager->DestroyItem(testItem);
        }
        else
        {
            m_stats.itemsGiven++;
        }
    }
    else
    {
        SendMessage(ch, "Item creation test failed!");
    }

    return true;
}

bool CrossCompilerGamePlugin::HandleItemCommand(ICHARACTER* ch, const char* args)
{
    if (!args || strlen(args) == 0)
    {
        SendMessage(ch, "Usage: /pluginitem <vnum> [count]");
        return true;
    }

    PLUGIN_DWORD vnum = 0;
    PLUGIN_DWORD count = 1;

    if (sscanf(args, "%u %u", &vnum, &count) < 1)
    {
        SendMessage(ch, "Invalid item vnum!");
        return true;
    }

    if (count == 0 || count > 200)
        count = 1;

    // Create item using interface
    IITEM* item = m_itemManager->CreateItem(vnum, count);
    if (item && item)
    {
        if (ch->AutoGiveItem(item))
        {
            char message[256];
            snprintf(message, sizeof(message), "Created and gave you: %s x%u", item->GetName(), count);
            SendMessage(ch, message);
            m_stats.itemsGiven += count;
        }
        else
        {
            SendMessage(ch, "Could not give item - inventory full?");
            m_itemManager->DestroyItem(item);
        }
    }
    else
    {
        SendMessage(ch, "Invalid item vnum or creation failed!");
    }

    return true;
}

bool CrossCompilerGamePlugin::HandleGuildCommand(ICHARACTER* ch, const char* args)
{
    IGUILD* guild = ch->GetGuild();
    if (!guild || !guild)
    {
        SendMessage(ch, "You are not in a guild!");
        return true;
    }

    SendMessage(ch, "=== Guild Information ===");

    char buffer[256];
    snprintf(buffer, sizeof(buffer), "Name: %s", guild->GetName());
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "ID: %u", guild->GetID());
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Level: %d", guild->GetLevel());
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Members: %d/%d", guild->GetMemberCount(), guild->GetMaxMemberCount());
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Master: %s", guild->GetMasterName());
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Experience: %d", guild->GetExp());
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Skill Points: %d", guild->GetSkillPoint());
    SendMessage(ch, buffer);

    snprintf(buffer, sizeof(buffer), "Guild Money: %lld", guild->GetGuildMoney());
    SendMessage(ch, buffer);

    return true;
}

bool CrossCompilerGamePlugin::HandleRewardCommand(ICHARACTER* ch, const char* args)
{
    if (ch->GetLevel() < 10)
    {
        SendMessage(ch, "You must be at least level 10 to receive rewards!");
        return true;
    }

    // Give gold reward
    long long goldReward = ch->GetLevel() * 1000;
    ch->PointChange(0, goldReward); // 0 = POINT_GOLD
    m_stats.goldGiven += goldReward;

    char message[256];
    snprintf(message, sizeof(message), "You received %lld gold as a level reward!", goldReward);
    SendMessage(ch, message);

    // Give item reward for higher levels
    if (ch->GetLevel() >= 30)
    {
        IITEM* reward = m_itemManager->CreateItem(50300, 1); // Example reward item
        if (reward)
        {
            if (ch->AutoGiveItem(reward))
            {
                SendMessage(ch, "You also received a special item reward!");
                m_stats.itemsGiven++;
            }
            else
            {
                m_itemManager->DestroyItem(reward);
                SendMessage(ch, "Could not give item reward - inventory full!");
            }
        }
    }

    return true;
}

bool CrossCompilerGamePlugin::HandleDebugCommand(ICHARACTER* ch, const char* args)
{
    if (!ch->IsGM())
    {
        SendMessage(ch, "This command is only available to GMs!");
        return true;
    }

    m_debugMode = !m_debugMode;

    char message[128];
    snprintf(message, sizeof(message), "Debug mode: %s", m_debugMode ? "ON" : "OFF");
    SendMessage(ch, message);

    return true;
}

// ============================================================================
// UTILITY FUNCTIONS FOR DELAYED EVENTS AND PLUGIN COMMUNICATION
// ============================================================================

void CrossCompilerGamePlugin::ScheduleDelayedReward(ICHARACTER* ch, PLUGIN_DWORD itemVnum, PLUGIN_DWORD delayMs)
{
    if (!ch)
        return;

    DelayedRewardData* data = new DelayedRewardData;
    data->plugin = this;
    data->characterPID = ch->GetPlayerID();
    data->itemVnum = itemVnum;
    data->count = 1;

    //m_factory->ScheduleDelayedCall(delayMs, DelayedRewardCallback, data);

    char message[256];
    snprintf(message, sizeof(message), "A reward will be delivered in %u seconds!", delayMs / 1000);
    SendMessage(ch, message);
}

void CrossCompilerGamePlugin::SchedulePeriodicAnnouncement()
{
    //m_factory->ScheduleRepeatingCall(300000, PeriodicAnnouncementCallback, this); // Every 5 minutes
}

void CrossCompilerGamePlugin::RegisterForPluginMessages()
{
    PluginMessageData* data = new PluginMessageData;
    data->plugin = this;

    //m_factory->RegisterMessageHandler(1001, PluginMessageCallback, data); // Custom message type
}

// Static callback implementations
void CrossCompilerGamePlugin::DelayedRewardCallback(void* userData)
{
    DelayedRewardData* data = static_cast<DelayedRewardData*>(userData);
    if (!data || !data->plugin)
    {
        delete data;
        return;
    }

    // Find character by PID
    ICHARACTER* ch = data->plugin->m_characterManager->FindByPID(data->characterPID);
    if (ch)
    {
        IITEM* reward = data->plugin->m_itemManager->CreateItem(data->itemVnum, data->count);
        if (reward)
        {
            if (ch->AutoGiveItem(reward))
            {
                data->plugin->SendMessage(ch, "Your delayed reward has arrived!");
                data->plugin->m_stats.itemsGiven += data->count;
            }
            else
            {
                data->plugin->m_itemManager->DestroyItem(reward);
                data->plugin->SendMessage(ch, "Could not deliver reward - inventory full!");
            }
        }
    }

    delete data;
}

void CrossCompilerGamePlugin::PeriodicAnnouncementCallback(void* userData)
{
    CrossCompilerGamePlugin* plugin = static_cast<CrossCompilerGamePlugin*>(userData);
    if (!plugin || !plugin->m_running)
        return;


    //plugin->m_factory->SendGlobalNotice("CrossCompilerGamePlugin is running with ABI-stable interfaces!");
}

void CrossCompilerGamePlugin::PluginMessageCallback(const char* senderPlugin, PLUGIN_DWORD messageType, const void* data, PLUGIN_DWORD dataSize, void* userData)
{
    PluginMessageData* msgData = static_cast<PluginMessageData*>(userData);
    if (!msgData || !msgData->plugin)
        return;

    //msgData->plugin->m_factory->LogMessage(0, "CrossCompilerGamePlugin",
    //                                      "Received message from %s (type: %u, size: %u)",
     //                                     senderPlugin, messageType, dataSize);
}


// ============================================================================
// PLUGIN FACTORY FUNCTIONS (OLD INTERFACE FOR COMPATIBILITY)
// ============================================================================

// Old plugin interface (for compatibility)
extern "C" {
    IPlugin* CreatePlugin()
    {
        return new CrossCompilerGamePlugin();
    }

    void DestroyPlugin(IPlugin* plugin)
    {
        delete plugin;
    }

    const char* GetPluginName()
    {
        return "CrossCompilerGamePlugin";
    }

    const char* GetPluginVersion()
    {
        return "1.0.0";
    }

    const char* GetPluginDescription()
    {
        return "Cross-compiler compatible game plugin using pure virtual interfaces";
    }
}

// ============================================================================
// PLUGIN FACTORY FUNCTIONS FOR GAME PLUGIN MANAGER
// ============================================================================

// Game plugin factory functions (required by GamePluginManager)
extern "C" {
    __declspec(dllexport) IGamePlugin* CreateGamePlugin()
    {
        return new CrossCompilerGamePlugin();
    }

    __declspec(dllexport) void DestroyGamePlugin(IGamePlugin* plugin)
    {
        delete plugin;
    }
}
