#ifndef __INC_ICPRIV_MANAGER_H__
#define __INC_ICPRIV_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
struct SPrivGuildData;
struct SPrivEmpireData;

/**
 * @brief Pure virtual interface for CPrivManager singleton
 * 
 * Provides ABI-stable access to privilege management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on empire, guild, and character privilege management.
 */
class ICPrivManager
{
public:
    virtual ~ICPrivManager() = default;
    
    // ============================================================================
    // PRIVILEGE REQUEST OPERATIONS
    // ============================================================================
    
    // Request privilege operations
    virtual void RequestGiveGuildPriv(DWORD guild_id, BYTE type, int value, time_t dur_time_sec) = 0;
    virtual void RequestGiveEmpirePriv(BYTE empire, BYTE type, int value, time_t dur_time_sec) = 0;
    virtual void RequestGiveCharacterPriv(DWORD pid, BYTE type, int value) = 0;
    
    // ============================================================================
    // PRIVILEGE GRANT OPERATIONS
    // ============================================================================
    
    // Grant privilege operations
    virtual void GiveGuildPriv(DWORD guild_id, BYTE type, int value, BYTE bLog, time_t end_time_sec) = 0;
    virtual void GiveEmpirePriv(BYTE empire, BYTE type, int value, BYTE bLog, time_t end_time_sec) = 0;
    virtual void GiveCharacterPriv(DWORD pid, BYTE type, int value, BYTE bLog) = 0;
    
    // ============================================================================
    // PRIVILEGE REMOVAL OPERATIONS
    // ============================================================================
    
    // Remove privilege operations
    virtual void RemoveGuildPriv(DWORD guild_id, BYTE type) = 0;
    virtual void RemoveEmpirePriv(BYTE empire, BYTE type) = 0;
    virtual void RemoveCharacterPriv(DWORD pid, BYTE type) = 0;
    
    // ============================================================================
    // PRIVILEGE QUERY OPERATIONS
    // ============================================================================
    
    // Get privilege values
    virtual int GetPriv(LPCHARACTER ch, BYTE type) = 0;
    virtual int GetPrivByEmpire(BYTE bEmpire, BYTE type) = 0;
    virtual int GetPrivByGuild(DWORD guild_id, BYTE type) = 0;
    virtual int GetPrivByCharacter(DWORD pid, BYTE type) = 0;
    
    // ============================================================================
    // EXTENDED PRIVILEGE QUERY OPERATIONS
    // ============================================================================
    
    // Get privilege data structures
    virtual SPrivEmpireData* GetPrivByEmpireEx(BYTE bEmpire, BYTE type) = 0;
    virtual const SPrivGuildData* GetPrivByGuildEx(DWORD dwGuildID, BYTE byType) const = 0;
};

#endif // __INC_ICPRIV_MANAGER_H__
