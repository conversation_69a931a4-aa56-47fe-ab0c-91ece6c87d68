#ifndef __INC_DESC_MANAGER_H__
#define __INC_DESC_MANAGER_H__

#include "../../common/stl.h"
#include "../../common/length.h"
#include "../../common/interfaces/IDescManager.h" // Temporarily disabled for compilation
#include "IFileMonitor.h"

class CLoginKey;
class CClientPackageCryptInfo;

class DESC_MANAGER : public singleton<DESC_MANAGER> , virtual public IDescManager
{
public:
	typedef std::unordered_set<LPDESC> DESC_SET;
	typedef std::unordered_set<LPCLIENT_DESC> CLIENT_DESC_SET;
	typedef std::map<int, LPDESC> DESC_HANDLE_MAP;
	typedef std::map<DWORD, LPDESC> DESC_HANDSHAKE_MAP;
	typedef std::map<DWORD, LPDESC> DESC_ACCOUNTID_MAP;
	typedef std::unordered_map<std::string, LPDESC> DESC_LOGINNAME_MAP;
	typedef std::map<DWORD, DWORD> DESC_HANDLE_RANDOM_KEY_MAP;

public:
	DESC_MANAGER();
	virtual ~DESC_MANAGER();

	void Initialize();
	void Destroy();

	LPDESC AcceptDesc(LPFDWATCH fdw, socket_t s);
	LPDESC AcceptP2PDesc(LPFDWATCH fdw, socket_t s);
	void DestroyDesc(LPDESC d, bool erase_from_set = true);

	DWORD CreateHandshake();

	LPCLIENT_DESC CreateConnectionDesc(LPFDWATCH fdw, const char* host, WORD port, int iPhaseWhenSucceed, bool bRetryWhenClosed);
	void TryConnect();

	LPDESC FindByHandle(DWORD handle);
	LPDESC FindByHandshake(DWORD dwHandshake);

	LPDESC FindByCharacterName(const char* name);
	LPDESC FindByLoginName(const std::string& login);
	void ConnectAccount(const std::string& login, LPDESC d);
	void DisconnectAccount(const std::string& login);

	void DestroyClosed();

	void UpdateLocalUserCount();
	DWORD GetLocalUserCount() { return m_iLocalUserCount; }
	void GetUserCount(int& iTotal, int** paiEmpireUserCount, int& iLocalCount);

	const DESC_SET& GetClientSet();

	DWORD MakeRandomKey(DWORD dwHandle);
	bool GetRandomKey(DWORD dwHandle, DWORD* prandom_key);

	DWORD CreateLoginKey(LPDESC d);
	LPDESC FindByLoginKey(DWORD dwKey);
	void ProcessExpiredLoginKey();

	bool IsDisconnectInvalidCRC() { return m_bDisconnectInvalidCRC; }
	void SetDisconnectInvalidCRCMode(bool bMode) { m_bDisconnectInvalidCRC = bMode; }

	bool IsP2PDescExist(const char* szHost, WORD wPort);

	// for C/S hybrid crypt
	bool LoadClientPackageCryptInfo(const char* pDirName);
	void SendClientPackageCryptKey(LPDESC desc);
	void SendClientPackageSDBToLoadMap(LPDESC desc, const char* pMapName);
#ifdef __FreeBSD__
	static void NotifyClientPackageFileChanged(const std::string& fileName, eFileUpdatedOptions eUpdateOption);
#endif 
	// ======================================================================================
	// PLUGIN INTERFACE FUNCTIONS
	// ======================================================================================
	IDESC* FindByHandleInterface(DWORD handle);
	IDESC* FindByHandshakeInterface(DWORD dwHandshake);
	IDESC* FindByCharacterNameInterface(const char* name);
	IDESC* FindByLoginNameInterface(const std::string& login);
private:
	bool m_bDisconnectInvalidCRC;

	DESC_HANDLE_RANDOM_KEY_MAP m_map_handle_random_key;

	CLIENT_DESC_SET m_set_pkClientDesc;
	DESC_SET m_set_pkDesc;

	DESC_HANDLE_MAP m_map_handle;
	DESC_HANDSHAKE_MAP m_map_handshake;
	//DESC_ACCOUNTID_MAP m_AccountIDMap;
	DESC_LOGINNAME_MAP m_map_loginName;
	std::map<DWORD, CLoginKey*> m_map_pkLoginKey;

	int m_iSocketsConnected;

	int m_iHandleCount;

	int m_iLocalUserCount;
	int m_aiEmpireUserCount[EMPIRE_MAX_NUM];

	bool m_bDestroyed;

	CClientPackageCryptInfo* m_pPackageCrypt;
};

#endif // __INC_DESC_MANAGER_H__
