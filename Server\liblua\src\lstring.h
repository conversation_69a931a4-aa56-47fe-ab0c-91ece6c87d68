/*
* $Id: lstring.h,v 1.37 2002/08/16 14:45:55 roberto Exp $
* String table (keep all strings handled by Lu<PERSON>)
* See Copyright Notice in lua.h
*/

#ifndef __INC_LSTRING_H__
#define __INC_LSTRING_H__

#include "lobject.h"
#include "lstate.h"

#define sizestring(l) (cast(lu_mem, sizeof(union TString))+ \
	(cast(lu_mem, l)+1)*sizeof(char))

#define sizeudata(l) (cast(lu_mem, sizeof(union Udata))+(l))

#define luaS_new(L, s) (luaS_newlstr(L, s, strlen(s)))
#define luaS_newliteral(L, s) (luaS_newlstr(L, "" s, \
	(sizeof(s)/sizeof(char))-1))

#define luaS_fix(s) ((s)->tsv.marked |= (1<<4))

void luaS_resize(lua_State* L, int newsize);
Udata* luaS_newudata(lua_State* L, size_t s);
void luaS_freeall(lua_State* L);
TString* luaS_newlstr(lua_State* L, const char* str, size_t l);

#endif // __INC_LSTRING_H__
