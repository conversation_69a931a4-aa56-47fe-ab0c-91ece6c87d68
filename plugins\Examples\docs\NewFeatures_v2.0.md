# Plugin System v2.0 - New Features Guide

This document outlines all the new features added to the ProjectZ Plugin System v2.0, including packet communication, custom database queries, and boot-time initialization.

## 🚀 Overview of New Features

### 1. Packet Communication System
Full bidirectional communication between servers and clients with custom packet handling.

### 2. Custom Database Queries (QID System)
Register custom query IDs for specialized database operations with compiled query strings.

### 3. Boot-Time Initialization
Plugins can initialize custom features during server startup, including custom table creation.

### 4. Advanced Event Integration
Complete integration with all server events with performance optimization.

### 5. Security Enhancements
Query strings compiled into plugins, not exposed in source code.

---

## 📡 Packet Communication System

### Game Plugin → Database Server Communication

```cpp
// Send custom data to database server
struct CustomPlayerData {
    uint32_t type;
    uint32_t playerID;
    char customField[128];
    uint32_t timestamp;
};

CustomPlayerData data = {1, playerID, "custom_value", time(nullptr)};
SendToDatabase(&data, sizeof(data), "MyDBPlugin");
```

### Game Plugin → Client Communication

```cpp
// Send custom packet to specific client
struct CustomClientPacket {
    uint32_t type;
    char message[256];
    uint32_t value;
};

CustomClientPacket packet = {100, "Hello from plugin!", 42};
SendToClient(ch, &packet, sizeof(packet));

// Broadcast to all clients
BroadcastToClients(&packet, sizeof(packet));
```

### Database Plugin → Game Server Communication

```cpp
// Send response back to game server
struct DatabaseResponse {
    uint32_t type;
    uint32_t playerID;
    int result;
    char data[512];
};

DatabaseResponse response = {2, playerID, 1, "success"};
SendToGame(peer, &response, sizeof(response), "MyGamePlugin");

// Broadcast to all game servers
BroadcastToGameServers(&response, sizeof(response));
```

### Packet Handler Registration

```cpp
// Game plugin packet handler
RegisterPacketHandler([this](const void* data, uint32_t size, const std::string& source) {
    if (size >= sizeof(uint32_t)) {
        uint32_t packetType = *static_cast<const uint32_t*>(data);
        switch (packetType) {
            case 1: HandlePlayerDataUpdate(data, size); break;
            case 2: HandleSystemNotification(data, size); break;
        }
    }
});

// Database plugin packet handler
RegisterPacketHandler([this](CPeer* peer, const void* data, uint32_t size, const std::string& source) {
    // Handle incoming packets from game servers
});
```

---

## 🗄️ Custom Database Queries (QID System)

### QID Range Allocation
- **Plugin QIDs**: 1000-1999 (reserved for plugins)
- **Per-plugin allocation**: Each plugin gets a sub-range
- **Automatic management**: QID manager handles allocation and conflicts

### Database Plugin QID Registration

```cpp
// Register custom QID handler
bool MyDBPlugin::Initialize() {
    // Register player statistics QID
    if (!PluginQIDManager::instance().RegisterQueryHandler(
        m_info.name, QID_PLAYER_STATS,
        [this](CPeer* peer, CQueryInfo* qi, SQLMsg* result) {
            HandlePlayerStatsQuery(peer, qi, result);
        })) {
        return false;
    }
    
    return true;
}
```

### Custom Query Handler Implementation

```cpp
void MyDBPlugin::HandlePlayerStatsQuery(CPeer* peer, CQueryInfo* qi, SQLMsg* result) {
    // Extract query data
    const auto* queryData = static_cast<const PlayerStatsQuery*>(qi->pvData);
    
    if (result && result->Get()->uiNumRows > 0) {
        MYSQL_ROW row = mysql_fetch_row(result->Get()->pSQLResult);
        if (row) {
            // Process result and send back to game server
            PlayerStatsResult statsResult;
            statsResult.playerID = queryData->playerID;
            statsResult.kills = atoi(row[0]);
            statsResult.deaths = atoi(row[1]);
            
            SendToGame(peer, &statsResult, sizeof(statsResult), "MyGamePlugin");
        }
    }
}
```

### Game Plugin Query Execution

```cpp
// Execute custom database query
void MyGamePlugin::LoadPlayerStats(uint32_t playerID) {
    struct PlayerStatsQuery {
        uint32_t playerID;
    } query = { playerID };
    
    ExecuteCustomQuery(QID_PLAYER_STATS, &query, sizeof(query), playerID);
}

// Handle query result
RegisterQueryHandler(QID_PLAYER_STATS, [this](uint32_t queryID, const void* data, uint32_t size, uint32_t playerID) {
    const auto* result = static_cast<const PlayerStatsResult*>(data);
    // Process player statistics
});
```

### Compiled Query Strings (Security)

```cpp
// ❌ BAD: Query exposed in source
std::string query = "SELECT * FROM player WHERE id = " + std::to_string(playerID);

// ✅ GOOD: Query compiled into plugin
namespace {
    const char* QUERY_PLAYER_STATS = 
        "SELECT kills, deaths, playtime FROM player_stats WHERE player_id = %u";
}

void HandleQuery(uint32_t playerID) {
    char query[512];
    snprintf(query, sizeof(query), QUERY_PLAYER_STATS, playerID);
    ExecuteQuery(query, QID_PLAYER_STATS);
}
```

---

## 🚀 Boot-Time Initialization

### Database Plugin Boot Integration

```cpp
class MyDBPlugin : public IDBPlugin {
public:
    // Called when server starts boot process
    void OnBootStart() override {
        LogEvent(0, "Boot initialization starting");
        // Prepare for custom initialization
    }
    
    // Called during boot process - can fail server startup
    bool ProcessBootInitialization() override {
        // Create custom tables
        if (!CreateCustomTables()) {
            LogEvent(2, "Failed to create custom tables");
            return false; // Fail server startup
        }
        
        // Load initial data
        if (!LoadInitialData()) {
            LogEvent(2, "Failed to load initial data");
            return false;
        }
        
        return true;
    }
    
    // Called when boot process completes
    void OnBootComplete() override {
        LogEvent(0, "Boot initialization completed");
        // Start background tasks
    }
};
```

### Custom Table Creation

```cpp
bool MyDBPlugin::CreateCustomTables() {
    // Compiled table creation queries
    const char* CREATE_PLAYER_STATS = 
        "CREATE TABLE IF NOT EXISTS `player_stats` ("
        "`player_id` int(11) NOT NULL,"
        "`kills` int(11) DEFAULT 0,"
        "`deaths` int(11) DEFAULT 0,"
        "`playtime` int(11) DEFAULT 0,"
        "PRIMARY KEY (`player_id`)"
        ") ENGINE=MyISAM DEFAULT CHARSET=utf8";
    
    SQLMsg* result = DirectQuery(CREATE_PLAYER_STATS);
    if (!result) {
        return false;
    }
    delete result;
    
    return true;
}
```

### Boot Integration in Server Source

The boot integration is automatically handled by the plugin system:

```cpp
// In ClientManagerBoot.cpp
bool CClientManager::InitializeTables() {
    // ... existing table initialization ...
    
    // Initialize plugin boot features
    if (!InitializePluginBootFeatures()) {
        sys_err("Failed to initialize plugin boot features");
        return false;
    }
    
    return true;
}
```

---

## 🎯 Advanced Event Integration

### Complete Event Coverage

All server events now have plugin integration points:

**Game Server Events:**
- Character: create, destroy, login, logout, level up, dead, revive
- Item: create, destroy, equip, unequip, use, drop, pickup
- Combat: attack, damage, kill
- Guild: create, destroy, join, leave, war
- Shop: buy, sell
- Chat: chat, whisper, shout
- Command: custom command handling
- Map: enter, leave
- Quest: start, complete, give up
- System: server start/shutdown, heartbeat, time updates
- Network: packet receive/send interception

**Database Server Events:**
- Player data: load start, load, save, create, delete
- Item data: load, save, create, destroy
- Guild data: load, save, create, destroy, member changes
- Login/logout: account and player tracking
- Peer: connect, disconnect, packet handling
- Cache: flush, load, expire
- Query: execute, result, error
- System: server lifecycle, heartbeat, time updates
- Boot: start, complete
- Backup/Maintenance: start, complete

### Performance Optimization

```cpp
// Smart initialization checks - minimal overhead
#define GAME_PLUGIN_CALL_CHARACTER_LOGIN(ch) \
    do { \
        if (GamePluginManager::instance().IsInitialized()) { \
            GamePluginManager::instance().BroadcastCharacterLogin(ch); \
        } \
    } while(0)
```

### Integration Points Added

**Game Server Files Modified:**
- `char.cpp` - Character events
- `char_item.cpp` - Item equip/unequip events
- `item_manager.cpp` - Item creation/destruction
- `input_login.cpp` - Login events
- `ClientManager.cpp` - Various events

**Database Server Files Modified:**
- `ClientManagerPlayer.cpp` - Player data events
- `ClientManagerBoot.cpp` - Boot integration
- `ClientManager.cpp` - Query processing

---

## 🔧 Usage Examples

### Complete Game Plugin Example

```cpp
class MyAdvancedGamePlugin : public IGamePlugin {
private:
    IPluginPacketInterface* m_packetInterface;
    
    // Compiled query IDs
    static const uint32_t QID_PLAYER_STATS = 1001;
    static const uint32_t QID_CUSTOM_DATA = 1002;

public:
    bool Initialize() override {
        // Initialize packet communication
        m_packetInterface = GetGamePluginPacketInterface();
        
        // Register packet handler
        m_packetInterface->RegisterPacketHandler([this](const void* data, uint32_t size, const std::string& source) {
            HandleDatabasePacket(data, size, source);
        });
        
        // Register query handlers
        m_packetInterface->RegisterQueryHandler(QID_PLAYER_STATS, [this](uint32_t queryID, const void* data, uint32_t size, uint32_t playerID) {
            HandlePlayerStatsResult(queryID, data, size, playerID);
        });
        
        return true;
    }
    
    void OnCharacterLogin(LPCHARACTER ch) override {
        // Send welcome packet to client
        SendWelcomePacket(ch);
        
        // Load player statistics from database
        LoadPlayerStats(ch->GetPlayerID());
        
        // Notify database about login
        NotifyDatabaseLogin(ch);
    }
    
    void OnItemUse(LPCHARACTER ch, LPITEM item) override {
        // Track item usage in database
        TrackItemUsage(ch->GetPlayerID(), item->GetVnum());
        
        // Send notification to client
        SendItemUsageNotification(ch, item);
    }
};
```

### Complete Database Plugin Example

```cpp
class MyAdvancedDBPlugin : public IDBPlugin {
private:
    IDBPluginPacketInterface* m_packetInterface;
    IPluginDBInterface* m_dbInterface;

public:
    bool Initialize() override {
        // Get interfaces
        m_packetInterface = GetDBPluginPacketInterface();
        m_dbInterface = GetPluginDBInterface();
        
        // Register custom QIDs
        PluginQIDManager::instance().RegisterQueryHandler("MyDBPlugin", QID_PLAYER_STATS,
            [this](CPeer* peer, CQueryInfo* qi, SQLMsg* result) {
                HandlePlayerStatsQuery(peer, qi, result);
            });
        
        return true;
    }
    
    bool ProcessBootInitialization() override {
        // Create custom tables
        return CreateCustomTables();
    }
    
    void OnPlayerLoad(DWORD playerID, TPlayerTable* playerTable) override {
        // Load additional player data
        LoadPlayerCustomData(playerID);
    }
    
    void HandleGamePacket(CPeer* peer, const void* data, uint32_t size, const std::string& source) {
        // Process packets from game servers
        uint32_t packetType = *static_cast<const uint32_t*>(data);
        switch (packetType) {
            case PACKET_TYPE_PLAYER_STATS_SAVE:
                SavePlayerStats(peer, data, size);
                break;
        }
    }
};
```

---

## 📋 Migration Guide

### From v1.0 to v2.0

1. **Update Plugin Headers**
   ```cpp
   // Add new includes
   #include "../../Server/common/plugin_packet_interface.h"
   #include "../../Server/game/src/plugin_packet_manager.h"  // Game plugins
   #include "../../Server/db/src/plugin_qid_manager.h"       // DB plugins
   ```

2. **Initialize Packet Communication**
   ```cpp
   // In Initialize() method
   m_packetInterface = GetGamePluginPacketInterface(); // or GetDBPluginPacketInterface()
   ```

3. **Register Packet Handlers**
   ```cpp
   m_packetInterface->RegisterPacketHandler([this](/* parameters */) {
       // Handle packets
   });
   ```

4. **Update Database Plugins for Boot Integration**
   ```cpp
   // Add boot methods
   void OnBootStart() override { /* ... */ }
   bool ProcessBootInitialization() override { /* ... */ return true; }
   void OnBootComplete() override { /* ... */ }
   ```

5. **Register Custom QIDs**
   ```cpp
   // Database plugins only
   PluginQIDManager::instance().RegisterQueryHandler(pluginName, qid, handler);
   ```

---

## 🔒 Security Considerations

1. **Compiled Queries**: All SQL queries should be compiled into the plugin binary
2. **Input Validation**: Always validate packet data and query parameters
3. **SQL Injection Prevention**: Use parameterized queries and proper escaping
4. **Access Control**: Plugins should only access data they're authorized for
5. **Error Handling**: Proper error handling to prevent crashes and data corruption

---

## 📈 Performance Considerations

1. **Minimal Overhead**: Plugin calls have minimal performance impact
2. **Smart Initialization**: Events only trigger if plugins are loaded
3. **Efficient Communication**: Packet communication is optimized for performance
4. **Database Optimization**: Custom QIDs allow for optimized database operations
5. **Memory Management**: Proper memory management in packet handling

---

## 🎯 Best Practices

1. **Use Compiled Queries**: Never expose SQL strings in source code
2. **Validate All Input**: Always validate packet data and parameters
3. **Handle Errors Gracefully**: Implement proper error handling and recovery
4. **Log Important Events**: Use the logging system for debugging and monitoring
5. **Test Thoroughly**: Test all packet communication and database operations
6. **Document Your Plugin**: Provide clear documentation for your plugin's features
7. **Follow Naming Conventions**: Use consistent naming for packets, QIDs, and functions

---

This completes the comprehensive guide to the new features in Plugin System v2.0. The system now provides full server integration with secure, efficient communication and database operations.
