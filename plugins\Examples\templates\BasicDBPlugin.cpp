/**
 * @file BasicDBPlugin.cpp
 * @brief Basic example of a Database Plugin
 * <AUTHOR> System Team
 * @version 2.0
 * @date 2025
 * 
 * This is a simple example showing how to create a basic DB plugin that:
 * - Handles player login/logout events
 * - Logs item creation events
 * - Demonstrates basic plugin structure
 * - Shows proper error handling
 */

#include "../../Server/common/plugin_interface.h"
#include <string>

/**
 * @class BasicDBPlugin
 * @brief A simple database plugin that demonstrates basic functionality
 * 
 * This plugin shows the minimal implementation required for a DB plugin:
 * - Basic plugin information
 * - Player event handling
 * - Item event handling
 * - Proper initialization and cleanup
 */
class BasicDBPlugin : public IDBPlugin
{
private:
    std::string m_pluginName;
    bool m_initialized;
    uint32_t m_playerLoginCount;
    uint32_t m_itemCreateCount;

public:
    /**
     * @brief Constructor
     */
    BasicDBPlugin() 
        : m_pluginName("BasicDBPlugin")
        , m_initialized(false)
        , m_playerLoginCount(0)
        , m_itemCreateCount(0)
    {
        sys_log(0, "[%s] Plugin instance created", m_pluginName.c_str());
    }
    
    /**
     * @brief Destructor
     */
    virtual ~BasicDBPlugin()
    {
        sys_log(0, "[%s] Plugin destroyed. Processed %u logins, %u item creations", 
                m_pluginName.c_str(), m_playerLoginCount, m_itemCreateCount);
    }

    /**
     * @brief Get plugin information
     */
    virtual PluginInfo GetPluginInfo() const override
    {
        PluginInfo info;
        info.name = m_pluginName;
        info.version = "1.0.0";
        info.description = "Basic example DB plugin for learning purposes";
        info.author = "Plugin System Team";
        info.supportedInterfaces = PLUGIN_INTERFACE_DB;
        return info;
    }
    
    /**
     * @brief Initialize the plugin
     */
    virtual bool Initialize() override
    {
        sys_log(0, "[%s] Initializing plugin...", m_pluginName.c_str());
        
        try {
            // Perform any initialization here
            // For example: load configuration, initialize data structures, etc.
            
            m_initialized = true;
            sys_log(0, "[%s] Plugin initialized successfully", m_pluginName.c_str());
            return true;
            
        } catch (const std::exception& e) {
            sys_err("[%s] Failed to initialize: %s", m_pluginName.c_str(), e.what());
            return false;
        }
    }
    
    /**
     * @brief Shutdown the plugin
     */
    virtual void Shutdown() override
    {
        sys_log(0, "[%s] Shutting down plugin...", m_pluginName.c_str());
        
        try {
            // Perform cleanup here
            // For example: save data, close connections, free resources, etc.
            
            m_initialized = false;
            sys_log(0, "[%s] Plugin shutdown complete", m_pluginName.c_str());
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error during shutdown: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle player login event
     * This is called when a player logs into the game
     */
    virtual void OnPlayerLogin(DWORD playerID, const char* playerName) override
    {
        if (!m_initialized) return;
        
        try {
            sys_log(0, "[%s] Player login: ID=%u, Name=%s", 
                    m_pluginName.c_str(), playerID, playerName ? playerName : "Unknown");
            
            // Example: You could perform custom actions here such as:
            // - Log login time to a custom table
            // - Check for special events
            // - Update player statistics
            // - Send welcome messages
            
            m_playerLoginCount++;
            
            // Example of conditional logic based on player ID
            if (playerID == 1) {
                sys_log(0, "[%s] Admin player logged in!", m_pluginName.c_str());
            }
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error handling player login: %s", m_pluginName.c_str(), e.what());
        }
    }
    
    /**
     * @brief Handle player logout event
     * This is called when a player logs out of the game
     */
    virtual void OnPlayerLogout(DWORD playerID) override
    {
        if (!m_initialized) return;
        
        try {
            sys_log(0, "[%s] Player logout: ID=%u", m_pluginName.c_str(), playerID);
            
            // Example: You could perform custom actions here such as:
            // - Log logout time
            // - Save player session data
            // - Update statistics
            // - Clean up temporary data
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error handling player logout: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle item creation event
     * This is called when an item is created in the database
     */
    virtual void OnItemCreate(DWORD itemID, TPlayerItem* item) override
    {
        if (!m_initialized || !item) return;
        
        try {
            sys_log(0, "[%s] Item created: ID=%u, VNum=%u, Owner=%u, Count=%u", 
                    m_pluginName.c_str(), itemID, item->vnum, item->owner, item->count);
            
            // Example: You could perform custom actions here such as:
            // - Log rare item creation
            // - Update item statistics
            // - Trigger special events for certain items
            // - Notify administrators of valuable item creation
            
            m_itemCreateCount++;
            
            // Example: Special handling for rare items (VNum >= 10000)
            if (item->vnum >= 10000) {
                sys_log(0, "[%s] Rare item created! VNum=%u for player %u", 
                        m_pluginName.c_str(), item->vnum, item->owner);
            }
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error handling item creation: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle server boot start event
     * This is called when the server starts booting
     */
    virtual void OnBootStart() override
    {
        sys_log(0, "[%s] Server boot start event received", m_pluginName.c_str());
        
        try {
            // Example: You could perform boot-time initialization here such as:
            // - Load server-wide configuration
            // - Initialize global data
            // - Prepare database connections
            // - Set up scheduled tasks
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error during boot start: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle server boot completion event
     * This is called when the server has finished booting
     */
    virtual void OnBootComplete() override
    {
        sys_log(0, "[%s] Server boot complete event received", m_pluginName.c_str());
        
        try {
            // Example: You could perform post-boot actions here such as:
            // - Start background services
            // - Enable plugin features
            // - Send notifications
            // - Begin monitoring tasks
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error during boot complete: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle server shutdown event
     * This is called when the server is shutting down
     */
    virtual void OnServerShutdown() override
    {
        sys_log(0, "[%s] Server shutdown event received", m_pluginName.c_str());
        
        try {
            // Example: You could perform shutdown actions here such as:
            // - Save important data
            // - Stop background tasks
            // - Close connections
            // - Log shutdown statistics
            
            sys_log(0, "[%s] Final stats - Logins: %u, Item creations: %u", 
                    m_pluginName.c_str(), m_playerLoginCount, m_itemCreateCount);
            
        } catch (const std::exception& e) {
            sys_err("[%s] Error during server shutdown: %s", m_pluginName.c_str(), e.what());
        }
    }

    /**
     * @brief Handle heartbeat event
     * This is called regularly (usually every few seconds)
     */
    virtual void OnHeartbeat() override
    {
        // Note: This is called very frequently, so avoid heavy operations here
        // Only use for lightweight monitoring or periodic checks
        
        // Example: You could perform periodic tasks here such as:
        // - Check system health
        // - Update counters
        // - Perform lightweight maintenance
        
        // Uncomment the line below only for debugging (will spam logs)
        // sys_log(0, "[%s] Heartbeat - Logins: %u, Items: %u", m_pluginName.c_str(), m_playerLoginCount, m_itemCreateCount);
    }
};

// =================================================================
// PLUGIN FACTORY FUNCTIONS
// =================================================================

/**
 * @brief Create plugin instance
 * This function is called by the plugin loader to create an instance of the plugin
 */
extern "C" __declspec(dllexport) IPlugin* CreatePlugin()
{
    return new BasicDBPlugin();
}

/**
 * @brief Destroy plugin instance
 * This function is called by the plugin loader to destroy a plugin instance
 */
extern "C" __declspec(dllexport) void DestroyPlugin(IPlugin* plugin)
{
    delete plugin;
}

/**
 * @brief Get plugin information
 * This function allows the plugin loader to get information about the plugin
 * without creating an instance
 */
extern "C" __declspec(dllexport) PluginInfo GetPluginInfo()
{
    BasicDBPlugin temp;
    return temp.GetPluginInfo();
}
