#ifndef __INC_PLUGIN_GAME_INTERFACES_H__
#define __INC_PLUGIN_GAME_INTERFACES_H__

/**
 * @file plugin_game_interfaces.h
 * @brief Consolidated game plugin interfaces for easy inclusion
 * 
 * This file includes all the game-related interfaces that plugins need.
 * It serves as a convenience header to avoid including multiple interface files.
 */

// Core plugin interface
#include "plugin_interface.h"

// Character and entity interfaces
#include "interfaces/IChar.h"
#include "interfaces/ICharManager.h"
#include "interfaces/IEntity.h"

// Item interfaces
#include "interfaces/IItem.h"
#include "interfaces/IItemManager.h"
#include "interfaces/IItemAddonManager.h"

// Guild interfaces
#include "interfaces/IGuild.h"
#include "interfaces/IGuildManager.h"

// Shop interfaces
#include "interfaces/IShop.h"
#include "interfaces/IShopManager.h"

// Party interfaces
#include "interfaces/IParty.h"
#include "interfaces/IPartyManager.h"

// Manager interfaces
#include "interfaces/IDescManager.h"
#include "interfaces/IBufferManager.h"
#include "interfaces/ISectreeManager.h"
#include "interfaces/ISkillManager.h"
#include "interfaces/ITargetManager.h"
#include "interfaces/ILogManager.h"
#include "interfaces/IMessengerManager.h"
#include "interfaces/IMobManager.h"
#include "interfaces/IPrivManager.h"

// PVP and Arena interfaces
#include "interfaces/IPVPManager.h"
#include "interfaces/IArenaManager.h"
#include "interfaces/ICArena.h"
#include "interfaces/ICArenaMap.h"

// Dungeon interfaces
#include "interfaces/IDungeonManager.h"
#include "interfaces/ICDungeon.h"

// War map interfaces
#include "interfaces/IWarMapManager.h"
#include "interfaces/ICWarMap.h"

// Other game interfaces
#include "interfaces/IMonarch.h"
#include "interfaces/IOXEventManager.h"
#include "interfaces/ICPVP.h"
#include "interfaces/ICFSM.h"
#include "interfaces/IHorseRider.h"
#include "interfaces/ICubeManager.h"

// P2P interfaces
#include "interfaces/IP2P.h"
#include "interfaces/IP2PManager.h"

// Database interfaces (for game-db communication)
#include "interfaces/IDBManager.h"
#include "interfaces/IDSManager.h"

// Descriptor interface
#include "interfaces/IDesc.h"

#endif // __INC_PLUGIN_GAME_INTERFACES_H__
