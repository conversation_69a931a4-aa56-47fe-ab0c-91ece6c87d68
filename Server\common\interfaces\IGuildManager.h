#ifndef __INC_IGUILD_MANAGER_H__
#define __INC_IGUILD_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"
#include "../singleton.h"

struct TGuildCreateParameter;
class CGuildWarReserveForGame;
/**
 * @brief Pure virtual interface for GUILD_MANAGER singleton
 * 
 * Provides ABI-stable access to guild management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on guild lifecycle and management operations.
 */
class IGuildManager : virtual public Isingleton<IGuildManager>
{
public:
    virtual ~IGuildManager() = default;

	virtual DWORD CreateGuild(TGuildCreateParameter& gcp) = 0;
	virtual CGuild* FindGuild(DWORD guild_id) = 0;
	virtual CGuild* FindGuildByName(const std::string guild_name) = 0;
	virtual void LoadGuild(DWORD guild_id) = 0;
	virtual CGuild* TouchGuild(DWORD guild_id) = 0;
	virtual void DisbandGuild(DWORD guild_id) = 0;

	virtual void Initialize() = 0;

	virtual void Link(DWORD pid, CGuild* guild) = 0;
	virtual void Unlink(DWORD pid) = 0;
	virtual CGuild* GetLinkedGuild(DWORD pid) = 0;

	virtual void LoginMember(LPCHARACTER ch) = 0;
	virtual void P2PLoginMember(DWORD pid) = 0;
	virtual void P2PLogoutMember(DWORD pid) = 0;

	virtual void SkillRecharge() = 0;

	virtual void ShowGuildWarList(LPCHARACTER ch) = 0;
	virtual void SendGuildWar(LPCHARACTER ch) = 0;

	virtual void RequestEndWar(DWORD guild_id1, DWORD guild_id2) = 0;
	virtual void RequestCancelWar(DWORD guild_id1, DWORD guild_id2) = 0;
	virtual void RequestWarOver(DWORD dwGuild1, DWORD dwGuild2, DWORD dwGuildWinner, long lReward) = 0;

	virtual void DeclareWar(DWORD guild_id1, DWORD guild_id2, BYTE bType) = 0;
	virtual void RefuseWar(DWORD guild_id1, DWORD guild_id2) = 0;
	virtual void StartWar(DWORD guild_id1, DWORD guild_id2) = 0;
	virtual void WaitStartWar(DWORD guild_id1, DWORD guild_id2) = 0;
	virtual void WarOver(DWORD guild_id1, DWORD guild_id2, bool bDraw) = 0;
	virtual void CancelWar(DWORD guild_id1, DWORD guild_id2) = 0;
	virtual bool EndWar(DWORD guild_id1, DWORD guild_id2) = 0;
	virtual void ReserveWar(DWORD dwGuild1, DWORD dwGuild2, BYTE bType) = 0;

	virtual void ReserveWarAdd(TGuildWarReserve* p) = 0;
	virtual void ReserveWarDelete(DWORD dwID) = 0;
	virtual std::vector<CGuildWarReserveForGame*>& GetReserveWarRef() = 0;
	virtual void ReserveWarBet(TPacketGDGuildWarBet* p) = 0;
	virtual bool IsBet(DWORD dwID, const char* c_pszLogin) = 0;

	virtual void StopAllGuildWar() = 0;

	virtual void Kill(LPCHARACTER killer, LPCHARACTER victim) = 0;

	virtual int GetRank(CGuild* g) = 0;

	virtual void GetHighRankString(DWORD dwMyGuild, char* buffer, size_t buflen) = 0;
	virtual void GetAroundRankString(DWORD dwMyGuild, char* buffer, size_t buflen) = 0;


	virtual int GetDisbandDelay() = 0;
	virtual int GetWithdrawDelay() = 0;

	virtual void ChangeMaster(DWORD dwGID) = 0;

#if defined(__GUILD_EVENT_FLAG__) //&& defined(__GUILD_RENEWAL__)
	public:
	virtual void SendGuildEventFlagList(LPCHARACTER pChar) = 0;

	virtual void RequestSetEventFlag(DWORD dwGuildID, const std::string& strFlagName, long lValue) = 0;
	virtual void SetEventFlag(DWORD dwGuildID, const std::string& strFlagName, long lValue) = 0;
	virtual long GetEventFlag(DWORD dwGuildID, const std::string& strFlagName) = 0;
#endif
};

#endif // __INC_IGUILD_MANAGER_H__
