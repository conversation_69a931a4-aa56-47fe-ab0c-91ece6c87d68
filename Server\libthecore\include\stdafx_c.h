#ifndef __INC_LIBTHECORE_STDAFX_C_H__
#define __INC_LIBTHECORE_STDAFX_C_H__

// C-only version of stdafx.h for pure C files
// This header excludes C++ headers to avoid STL1003 errors

#ifdef _WIN32
#define WIN32_LEAN_AND_MEAN
#ifndef NOMINMAX
#define NOMINMAX
#endif
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <io.h>
#include <direct.h>
#include <process.h>
#include <time.h>

// Windows-specific definitions
#ifndef PATH_MAX
#define PATH_MAX 260
#endif
// Don't redefine snprintf/vsnprintf for modern MSVC
#if _MSC_VER < 1900
#define snprintf _snprintf
#define vsnprintf _vsnprintf
#endif
#define strcasecmp _stricmp
#define strncasecmp _strnicmp

#else
// Unix/Linux headers
#include <sys/time.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/signal.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <unistd.h>
#include <dirent.h>

#ifdef __FreeBSD__
#include <sys/event.h>
#endif

#endif

// Standard C headers
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <memory.h>
#include <errno.h>
#include <fcntl.h>
#include <assert.h>
#include <ctype.h>
#include <limits.h>
#include <stdarg.h>
#include <math.h>

#ifndef false
#define false 0
#define true (!false)
#endif

#ifndef FALSE
#define FALSE false
#define TRUE (!FALSE)
#endif

// Include libthecore headers
#include "typedef.h"
#include "heart.h"
#include "fdwatch.h"
#include "socket.h"
#include "kstbl.h"
#include "hangul.h"
#include "buffer.h"
#include "signal.h"
#include "log.h"
#include "main.h"
#include "utils.h"
#include "crypt.h"
#include "memcpy.h"

#endif // __INC_LIBTHECORE_STDAFX_C_H__
