#ifndef __INC_SIMPLE_GAME_PLUGIN_H__
#define __INC_SIMPLE_GAME_PLUGIN_H__

#include "../../Server/common/plugin_interface.h"
#include "../../Server/game/src/game_plugin_manager.h"

/**
 * @brief Simple Game Plugin demonstrating CHARACTER and CItem access
 * 
 * This plugin demonstrates how to:
 * - Access CHARACTER class and its methods (ch->GetName(), ch->GetLevel(), etc.)
 * - Access CItem class and its methods (item->GetVnum(), item->GetCount(), etc.)
 * - Use singleton managers like ITEM_MANAGER for item creation
 * - Handle game events (character login, item creation, combat, etc.)
 * - Process custom commands
 * - Access all game objects passed through the plugin interface
 * 
 * Key Features:
 * - Full CHARACTER object access in all character events
 * - Full CItem object access in all item events
 * - Combat event handling with attacker/victim access
 * - Command processing with character context
 * - Statistics tracking
 * - System event handling (server start/stop, periodic updates)
 */
class SimpleGamePlugin : public IGamePlugin
{
public:
    SimpleGamePlugin();
    virtual ~SimpleGamePlugin();
    
    // IPlugin interface implementation
    virtual bool Initialize() override;
    virtual bool Start() override;
    virtual void Stop() override;
    virtual void Shutdown() override;
    virtual const PluginInfo& GetInfo() const override;
    virtual PluginState GetState() const override;
    
    // IGamePlugin interface - Character events
    virtual void OnCharacterCreate(LPCHARACTER ch) override;
    virtual void OnCharacterDestroy(LPCHARACTER ch) override;
    virtual void OnCharacterLogin(LPCHARACTER ch) override;
    virtual void OnCharacterLogout(LPCHARACTER ch) override;
    virtual void OnCharacterLevelUp(LPCHARACTER ch, BYTE newLevel) override;
    virtual void OnCharacterDead(LPCHARACTER ch, LPCHARACTER killer) override;
    virtual void OnCharacterRevive(LPCHARACTER ch) override;
    
    // IGamePlugin interface - Item events
    virtual void OnItemCreate(LPITEM item) override;
    virtual void OnItemDestroy(LPITEM item) override;
    virtual void OnItemEquip(LPCHARACTER ch, LPITEM item) override;
    virtual void OnItemUnequip(LPCHARACTER ch, LPITEM item) override;
    virtual void OnItemUse(LPCHARACTER ch, LPITEM item) override;
    virtual void OnItemDrop(LPCHARACTER ch, LPITEM item) override;
    virtual void OnItemPickup(LPCHARACTER ch, LPITEM item) override;
    
    // IGamePlugin interface - Combat events
    virtual void OnAttack(LPCHARACTER attacker, LPCHARACTER victim, int damage) override;
    virtual void OnKill(LPCHARACTER killer, LPCHARACTER victim) override;
    virtual void OnDamage(LPCHARACTER victim, LPCHARACTER attacker, int damage) override;
    
    // IGamePlugin interface - Guild events
    virtual void OnGuildCreate(LPGUILD guild) override;
    virtual void OnGuildDestroy(LPGUILD guild) override;
    virtual void OnGuildJoin(LPCHARACTER ch, LPGUILD guild) override;
    virtual void OnGuildLeave(LPCHARACTER ch, LPGUILD guild) override;
    virtual void OnGuildWar(LPGUILD guild1, LPGUILD guild2) override;
    
    // IGamePlugin interface - Shop events
    virtual void OnShopBuy(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count) override;
    virtual void OnShopSell(LPCHARACTER ch, LPSHOP shop, LPITEM item, int count) override;
    
    // IGamePlugin interface - Quest events
    virtual void OnQuestStart(LPCHARACTER ch, int questIndex) override;
    virtual void OnQuestComplete(LPCHARACTER ch, int questIndex) override;
    virtual void OnQuestGiveUp(LPCHARACTER ch, int questIndex) override;
    
    // IGamePlugin interface - Chat events
    virtual void OnChat(LPCHARACTER ch, const char* message, int type) override;
    virtual void OnWhisper(LPCHARACTER from, LPCHARACTER to, const char* message) override;
    virtual void OnShout(LPCHARACTER ch, const char* message) override;
    
    // IGamePlugin interface - Command handling
    virtual bool OnCommand(LPCHARACTER ch, const char* command, const char* args) override;
    
    // IGamePlugin interface - Map events
    virtual void OnMapEnter(LPCHARACTER ch, long mapIndex) override;
    virtual void OnMapLeave(LPCHARACTER ch, long mapIndex) override;
    
    // IGamePlugin interface - System events
    virtual void OnServerStart() override;
    virtual void OnServerShutdown() override;
    virtual void OnHeartbeat() override;
    virtual void OnMinuteUpdate() override;
    virtual void OnHourUpdate() override;
    virtual void OnDayUpdate() override;

private:
    // Plugin state
    bool m_initialized;
    bool m_running;
    bool m_debugMode;
    PluginInfo m_info;
    PluginState m_state;
    
    // Statistics tracking
    struct Statistics
    {
        int charactersCreated;
        int itemsCreated;
        int combatEvents;
        int commandsProcessed;
    } m_stats;
};

#endif // __INC_SIMPLE_GAME_PLUGIN_H__
