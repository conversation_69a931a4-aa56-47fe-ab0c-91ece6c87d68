#ifndef __INC_PLUGIN_MANAGER_INTERFACES_H__
#define __INC_PLUGIN_MANAGER_INTERFACES_H__

/**
 * @file plugin_manager_interfaces.h
 * @brief Plugin manager interfaces for accessing game managers
 * 
 * This file provides interfaces for accessing various game managers
 * through the plugin system in an ABI-stable way.
 */

#include "plugin_interface.h"
#include "plugin_interface_factory.h"

// Forward declarations for manager interfaces
class ICharacterManager;
class IItemManager;
class IGuildManager;
class IShopManager;
class IDescManager;
class IBufferManager;
class ISectreeManager;
class ISkillManager;
class ITargetManager;
class ILogManager;
class IMessengerManager;
class IMobManager;
class IPrivManager;
class IPVPManager;
class IArenaManager;
class IDungeonManager;
class IWarMapManager;
class ICubeManager;
class IP2PManager;
class IDBManager;
class IDSManager;

/**
 * @brief Interface for accessing game managers from plugins
 * 
 * This interface provides access to all game managers through
 * ABI-stable pure virtual interfaces.
 */
class IPluginManagerAccess
{
public:
    virtual ~IPluginManagerAccess() = default;

    // Character management
    virtual ICharacterManager* GetCharacterManager() = 0;
    
    // Item management
    virtual IItemManager* GetItemManager() = 0;
    
    // Guild management
    virtual IGuildManager* GetGuildManager() = 0;
    
    // Shop management
    virtual IShopManager* GetShopManager() = 0;
    
    // Descriptor management
    virtual IDescManager* GetDescManager() = 0;
    
    // Buffer management
    virtual IBufferManager* GetBufferManager() = 0;
    
    // Sectree management
    virtual ISectreeManager* GetSectreeManager() = 0;
    
    // Skill management
    virtual ISkillManager* GetSkillManager() = 0;
    
    // Target management
    virtual ITargetManager* GetTargetManager() = 0;
    
    // Log management
    virtual ILogManager* GetLogManager() = 0;
    
    // Messenger management
    virtual IMessengerManager* GetMessengerManager() = 0;
    
    // Mob management
    virtual IMobManager* GetMobManager() = 0;
    
    // Privilege management
    virtual IPrivManager* GetPrivManager() = 0;
    
    // PVP management
    virtual IPVPManager* GetPVPManager() = 0;
    
    // Arena management
    virtual IArenaManager* GetArenaManager() = 0;
    
    // Dungeon management
    virtual IDungeonManager* GetDungeonManager() = 0;
    
    // War map management
    virtual IWarMapManager* GetWarMapManager() = 0;
    
    // Cube management
    virtual ICubeManager* GetCubeManager() = 0;
    
    // P2P management
    virtual IP2PManager* GetP2PManager() = 0;
    
    // Database management
    virtual IDBManager* GetDBManager() = 0;
    virtual IDSManager* GetDSManager() = 0;
};

/**
 * @brief Get the plugin manager access interface
 * 
 * This function provides access to all game managers through
 * the plugin system. It should be called by plugins to get
 * access to game functionality.
 * 
 * @return Pointer to IPluginManagerAccess interface
 */
extern "C" IPluginManagerAccess* GetPluginManagerAccess();

#endif // __INC_PLUGIN_MANAGER_INTERFACES_H__
