# Plugin Development Quick Start Guide

This guide will help you quickly get started with developing plugins for the ProjectZ server system, including the latest features like packet communication, custom database queries, and boot-time initialization.

## Prerequisites

Before you begin, ensure you have:
- C++14 compatible compiler (GCC 5.4+ or Clang 3.4+)
- CMake 3.10+ or Make
- Access to the ProjectZ server source code
- For DB plugins: MySQL development libraries
- Basic understanding of C++ and the game server architecture

## Quick Setup

### 1. Choose Your Plugin Type

**Game Plugin**: Extends game server functionality
- Handles player events, combat, items, guilds, etc.
- Runs on game servers
- Template: `External/PluginTemplate/GamePlugin/basic/`

**Database Plugin**: Extends database server functionality  
- Handles data operations, queries, backups, etc.
- Runs on database servers
- Template: `External/PluginTemplate/DBPlugin/basic/`

### 2. Copy Template

```bash
# For a game plugin
cp -r External/PluginTemplate/GamePlugin/basic/ MyAwesomePlugin/
cd MyAwesomePlugin/

# For a database plugin
cp -r External/PluginTemplate/DBPlugin/basic/ MyDBPlugin/
cd MyDBPlugin/
```

### 3. Rename and Customize

**Rename files:**
```bash
# Game plugin example
mv MyGamePlugin.h MyAwesomePlugin.h
mv MyGamePlugin.cpp MyAwesomePlugin.cpp
mv mygameplugin.conf myawesomeplugin.conf

# DB plugin example
mv MyDBPlugin.h MyDBPlugin.h
mv MyDBPlugin.cpp MyDBPlugin.cpp
mv mydbplugin.conf mydbplugin.conf
```

**Update class names in the files:**
- Replace `MyGamePlugin` or `MyDBPlugin` with your plugin name
- Update plugin information in the constructor
- Modify configuration file name references

### 4. Configure Plugin Information

Edit the constructor in your plugin class:

```cpp
// In your plugin constructor
m_info.name = "MyAwesomePlugin";
m_info.description = "An awesome plugin that does amazing things";
m_info.author = "Your Name";
m_info.version = PluginVersion(1, 0, 0);
```

### 5. Implement Your Logic

Add your custom functionality to the event handlers you need:

**Game Plugin Events:**
```cpp
void MyAwesomePlugin::OnCharacterLogin(LPCHARACTER ch)
{
    // Your login logic here
    SendMessage(ch, "Welcome! MyAwesomePlugin is active.");
}

void MyAwesomePlugin::OnCharacterLevelUp(LPCHARACTER ch, BYTE newLevel)
{
    // Your level up logic here
    if (newLevel == 10) {
        SendMessage(ch, "Congratulations on reaching level 10!");
        // Give reward, etc.
    }
}
```

**Database Plugin Events:**
```cpp
void MyDBPlugin::OnPlayerSave(DWORD playerID, TPlayerTable* playerTable)
{
    // Your player save logic here
    LogEvent(0, "Saving player: " + std::string(playerTable->name));
    // Backup data, validate, etc.
}
```

### 6. Build Your Plugin

**Using CMake (Recommended):**
```bash
mkdir build
cd build

# For game plugin
cmake -DPLUGIN_TYPE=game ..

# For database plugin  
cmake -DPLUGIN_TYPE=db ..

make
```

**Using Makefile:**
```bash
# Edit Makefile first:
# - Set PLUGIN_NAME to your plugin name
# - Set PLUGIN_TYPE to "game" or "db"
# - Add your source files to SOURCES

make
```

### 7. Install and Test

```bash
# Copy plugin to server
cp build/plugins/game/libMyAwesomePlugin.so /path/to/server/plugins/game/
# or for DB plugin:
cp build/plugins/db/libMyDBPlugin.so /path/to/server/plugins/db/

# Copy configuration
cp myawesomeplugin.conf /path/to/server/conf/

# Restart server to load plugin
```

## Configuration

Create a configuration file for your plugin:

```ini
# myawesomeplugin.conf
enabled=true
welcome_message=Welcome to MyAwesome Server!
max_level_reward=50
debug_mode=false
```

Access configuration in your plugin:
```cpp
bool MyAwesomePlugin::LoadConfig(const std::string& configPath)
{
    // Load and parse configuration
    // See template for complete example
}
```

## Common Event Handlers

### Game Plugin Events

**Character Events:**
- `OnCharacterLogin` - Player logs in
- `OnCharacterLogout` - Player logs out  
- `OnCharacterLevelUp` - Player gains level
- `OnCharacterDead` - Player dies

**Item Events:**
- `OnItemEquip` - Player equips item
- `OnItemUse` - Player uses item
- `OnItemDrop` - Player drops item

**Combat Events:**
- `OnAttack` - Player attacks
- `OnDamage` - Player takes damage
- `OnKill` - Player kills another

**Custom Commands:**
```cpp
bool MyAwesomePlugin::OnCommand(LPCHARACTER ch, const char* command, const char* args)
{
    if (strcmp(command, "awesome") == 0) {
        SendMessage(ch, "You used the awesome command!");
        return true; // Command handled
    }
    return false; // Command not handled
}
```

### Database Plugin Events

**Data Events:**
- `OnPlayerLoad` - Player data loaded
- `OnPlayerSave` - Player data saved
- `OnItemCreate` - Item created in database
- `OnGuildCreate` - Guild created

**System Events:**
- `OnBackupStart` - Database backup starting
- `OnQueryError` - Database query error
- `OnMaintenanceStart` - Maintenance mode

## Packet Communication

The plugin system now supports full packet communication between servers and clients.

### Game Plugin Communication

```cpp
// Send packet to database server
struct CustomData { int playerID; int value; };
CustomData data = {playerID, 100};
SendToDatabase(&data, sizeof(data), "MyDBPlugin");

// Send packet to client
struct ClientPacket { int type; char message[256]; };
ClientPacket packet = {1, "Hello from plugin!"};
SendToClient(ch, &packet, sizeof(packet));

// Broadcast to all clients
BroadcastToClients(&packet, sizeof(packet));

// Register packet handler
RegisterPacketHandler([](const void* data, uint32_t size, const std::string& source) {
    // Handle incoming packet
});
```

### Database Plugin Communication

```cpp
// Send packet to game server
struct ResponseData { int result; char message[128]; };
ResponseData response = {1, "Success"};
SendToGame(peer, &response, sizeof(response), "MyGamePlugin");

// Broadcast to all game servers
BroadcastToGameServers(&response, sizeof(response));
```

## Custom Database Queries

Database plugins can register custom QIDs for specialized database operations.

### Registering Custom QIDs

```cpp
// In database plugin initialization
bool MyDBPlugin::Initialize()
{
    // Register custom QID handler
    RegisterCustomQID(QID_PLAYER_STATS, [this](CPeer* peer, CQueryInfo* qi, SQLMsg* result) {
        HandlePlayerStatsQuery(peer, qi, result);
    });

    return true;
}
```

### Executing Custom Queries

```cpp
// Game plugin requests custom data
struct PlayerStatsQuery { uint32_t playerID; };
PlayerStatsQuery query = {ch->GetPlayerID()};
ExecuteCustomQuery(QID_PLAYER_STATS, &query, sizeof(query), ch->GetPlayerID());

// Database plugin handles the query
void MyDBPlugin::HandlePlayerStatsQuery(CPeer* peer, CQueryInfo* qi, SQLMsg* result)
{
    // Process query result
    // Send response back to game server
}
```

### Compiled Query Strings

Keep query strings compiled into the plugin for security:

```cpp
// Good: Compiled into plugin
const char* QUERY_PLAYER_CUSTOM =
    "SELECT custom_field1, custom_field2 FROM player_custom WHERE player_id = %u";

// Execute with parameters
char query[512];
snprintf(query, sizeof(query), QUERY_PLAYER_CUSTOM, playerID);
ExecuteQuery(query, QID_PLAYER_CUSTOM, peer);
```

## Boot-Time Initialization

Database plugins can perform custom initialization during server boot.

```cpp
// In database plugin
void OnBootStart() override {
    // Server is starting boot process
    LogEvent(0, "Boot initialization starting");
}

bool ProcessBootInitialization() {
    // Load custom tables
    if (!LoadCustomTables()) {
        return false; // Fail server startup
    }

    // Initialize custom data
    InitializeCustomData();
    return true;
}

void OnBootComplete() override {
    // Boot process completed
    LogEvent(0, "Boot initialization completed");
}
```

## Testing Your Plugin

1. **Enable debug mode** in configuration
2. **Check server logs** for plugin messages
3. **Test event triggers** by performing actions in game
4. **Monitor performance** impact
5. **Test error conditions** (invalid data, etc.)

## Common Issues

**Plugin not loading:**
- Check file permissions
- Verify plugin is in correct directory
- Check server logs for error messages
- Ensure all dependencies are available

**Events not triggering:**
- Verify plugin is in RUNNING state
- Check capability declarations
- Enable debug logging
- Ensure integration points are in server code

**Build errors:**
- Check C++14 compiler support
- Verify include paths
- For DB plugins: ensure MySQL libraries are installed
- Check for missing dependencies

## Next Steps

1. **Read the complete documentation** in `Documentation/`
2. **Study example plugins** in `examples/` directories
3. **Check advanced templates** for complex features
4. **Join the community** for support and discussions
5. **Contribute back** improvements and bug fixes

## Getting Help

- **Documentation**: See `Documentation/` directory
- **Examples**: Check `examples/` directories  
- **Troubleshooting**: See `Documentation/Troubleshooting.md`
- **API Reference**: See `Documentation/API_Reference.md`
- **Best Practices**: See `Documentation/Best_Practices.md`

## Template Structure

```
PluginTemplate/
├── GamePlugin/basic/          # Basic game plugin template
├── DBPlugin/basic/            # Basic database plugin template
├── Common/                    # Shared utilities and build templates
├── Documentation/             # Complete documentation
└── README.md                  # Overview and quick links
```

Happy plugin development! 🚀
