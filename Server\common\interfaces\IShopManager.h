#ifndef __INC_ISHOP_MANAGER_H__
#define __INC_ISHOP_MANAGER_H__

#include "../stl.h"
#include "../tables.h"
#include "../singleton.h"
#include "../../game/src/typedef.h"
/**
 * @brief Pure virtual interface for SHOP_MANAGER singleton
 * 
 * Provides ABI-stable access to shop management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on shop lifecycle and management operations.
 */

class IShopManager : virtual public Isingleton<IShopManager>
{
public:
    virtual ~IShopManager() = default;
    

	virtual bool Initialize(TShopTable* table, int size) = 0;
	virtual void Destroy() = 0;

	virtual LPSHOP Get(DWORD dwVnum) = 0;
	virtual LPSHOP GetByNPCVnum(DWORD dwVnum) = 0;

	virtual bool StartShopping(LPCHARACTER pkChr, LPCHARACTER pkShopKeeper, int iShopVnum = 0) = 0;
	virtual void StopShopping(LPCHARACTER ch) = 0;



	virtual void Buy(LPCHARACTER ch, BYTE pos) = 0;
	virtual void Sell(LPCHARACTER ch, WORD wCell, WORD wCount = 0, BYTE bType = 0) = 0;

	virtual LPSHOP CreatePCShop(LPCHARACTER ch, TShopItemTable* pTable, BYTE bItemCount) = 0;
	virtual LPSHOP FindPCShop(DWORD dwVID) = 0;
	virtual void DestroyPCShop(LPCHARACTER ch) = 0;
#if defined(__SHOPEX_RENEWAL__)
	virtual bool ReadShopTableEx(const char* stFileName) = 0;
#endif

};

#endif // __INC_ISHOP_MANAGER_H__
