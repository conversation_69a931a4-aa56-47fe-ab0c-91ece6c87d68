#include "../../Server/game/src/game_plugin_manager.h"
#include <cstdio>
// Simple logging macros that don't depend on external functions
#define PLUGIN_LOG(msg) printf("[MinimalGamePlugin] %s\n", msg)
#define PLUGIN_LOG_FMT(fmt, ...) printf("[MinimalGamePlugin] " fmt "\n", __VA_ARGS__)

class MinimalGamePlugin : public IGamePlugin
{
private:
    bool m_initialized;
    bool m_running;
    PluginInfo m_info;
    PluginState m_state;
    
    struct Statistics {
        int charactersCreated;
        int itemsCreated;
        int combatEvents;
    } m_stats;

public:
    MinimalGamePlugin()
        : m_initialized(false)
        , m_running(false)
    {
        // Initialize plugin info
        m_info.name = "MinimalGamePlugin";
        m_info.version = PluginVersion(1, 0, 0);
        m_info.description = "Minimal game plugin demonstrating ABI-stable interfaces";
        m_info.author = "Plugin System Developer";
        
        m_state = PluginState::PLUGIN_UNLOADED;
        
        // Initialize statistics
        m_stats.charactersCreated = 0;
        m_stats.itemsCreated = 0;
        m_stats.combatEvents = 0;
        
        PLUGIN_LOG("Constructor called");
    }
    
    virtual ~MinimalGamePlugin()
    {
        if (m_running)
            Stop();
        if (m_initialized)
            Shutdown();
        
        PLUGIN_LOG("Destructor called");
    }
    
    // IPlugin interface implementation
    virtual bool Initialize() override
    {
        if (m_initialized)
            return true;
        
        PLUGIN_LOG("Initialize() called");

        m_state = PluginState::PLUGIN_INITIALIZED;
        m_initialized = true;

        PLUGIN_LOG("Plugin initialized successfully");
        return true;
    }
    
    virtual bool Start() override
    {
        if (!m_initialized)
        {
            PLUGIN_LOG("ERROR: Cannot start - plugin not initialized");
            return false;
        }
        
        if (m_running)
            return true;
        
        PLUGIN_LOG("Start() called");

        m_state = PluginState::PLUGIN_RUNNING;
        m_running = true;

        PLUGIN_LOG("Plugin started successfully with ABI-stable interfaces");
        
        return true;
    }
    
    virtual void Stop() override
    {
        if (!m_running)
            return;
        
        PLUGIN_LOG("Stop() called");

        m_running = false;
        m_state = PluginState::PLUGIN_STOPPED;

        PLUGIN_LOG("Plugin stopped");
    }
    
    virtual void Shutdown() override
    {
        if (!m_initialized)
            return;
        
        PLUGIN_LOG("Shutdown() called");

        if (m_running)
            Stop();

        m_initialized = false;
        m_state = PluginState::PLUGIN_UNLOADED;

        PLUGIN_LOG("Plugin shutdown complete");
    }
    
    virtual const PluginInfo& GetInfo() const override
    {
        return m_info;
    }
    
    virtual PluginState GetState() const override
    {
        return m_state;
    }
    void SetState(PluginState state)
    {
        m_state = state;
        m_info.state = state;
    }
    // IGamePlugin interface - Character events (ABI-stable)
    virtual void OnCharacterCreate(ICHARACTER* ch) override
    {
        if (!m_running || !ch)
            return;
        
        m_stats.charactersCreated++;
        
        PLUGIN_LOG_FMT("Character created: %s", ch->GetName());
        
        // Send welcome message using ICHARACTER interface
        ch->ChatPacket(0, "[Plugin] Welcome! MinimalGamePlugin is active with ABI-stable interfaces!");
    }
    
    virtual void OnCharacterLogin(ICHARACTER* ch) override
    {
        if (!m_running || !ch)
            return;
        
        PLUGIN_LOG_FMT("Character login: %s (Level: %d)", ch->GetName(), ch->GetLevel());
        
        // Show player information using ICHARACTER interface
        char buffer[512];
        snprintf(buffer, sizeof(buffer), 
            "Login detected! Name=%s, Level=%d, Gold=%lld, HP=%d/%d",
            ch->GetName(), ch->GetLevel(), ch->GetGold(), ch->GetHP(), ch->GetMaxHP());
        ch->ChatPacket(0, "[Plugin] %s", buffer);
    }
    
    virtual void OnCharacterLevelUp(ICHARACTER* ch, BYTE newLevel) override
    {
        if (!m_running || !ch)
            return;
        
        PLUGIN_LOG_FMT("Character %s leveled up to %d", ch->GetName(), newLevel);
        
        // Congratulate on milestone levels
        if (newLevel % 10 == 0)
        {
            char message[256];
            snprintf(message, sizeof(message), "Congratulations on reaching level %d!", newLevel);
            ch->ChatPacket(0, "[Plugin] %s", message);
        }
    }
    
    virtual void OnItemCreate(IITEM* item) override
    {
        if (!m_running || !item)
            return;
        
        m_stats.itemsCreated++;
        
        PLUGIN_LOG_FMT("Item created: %s (Vnum: %u)", item->GetName(), item->GetVnum());
    }
    
    virtual void OnAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage) override
    {
        if (!m_running || !attacker || !victim)
            return;
        
        m_stats.combatEvents++;
        
        PLUGIN_LOG_FMT("%s attacked %s for %d damage",
                attacker->GetName(), victim->GetName(), damage);
    }
    
    virtual bool OnCommand(ICHARACTER* ch, const char* command, const char* args) override
    {
        if (!m_running || !ch || !command)
            return false;
        
        if (strcmp(command, "plugintest") == 0)
        {
            ch->ChatPacket(0, "[Plugin] Test successful! ABI-stable interfaces working!");
            PLUGIN_LOG_FMT("Test command executed by %s", ch->GetName());
            return true;
        }
        else if (strcmp(command, "pluginstats") == 0)
        {
            char buffer[512];
            snprintf(buffer, sizeof(buffer), 
                "Plugin Statistics: Characters=%d, Items=%d, Combat=%d",
                m_stats.charactersCreated, m_stats.itemsCreated, m_stats.combatEvents);
            ch->ChatPacket(0, "[Plugin] %s", buffer);
            return true;
        }
        
        return false; // Command not handled by this plugin
    }
    
    // System events
    virtual void OnServerStart() override
    {
        PLUGIN_LOG("Server started");
    }

    virtual void OnServerShutdown() override
    {
        PLUGIN_LOG("Server shutting down");
    }
    
    // Stub implementations for other events
    virtual void OnCharacterDestroy(ICHARACTER* ch) override {}
    virtual void OnCharacterLogout(ICHARACTER* ch) override {}
    virtual void OnCharacterDead(ICHARACTER* ch, ICHARACTER* killer) override {}
    virtual void OnCharacterRevive(ICHARACTER* ch) override {}
    virtual void OnItemDestroy(IITEM* item) override {}
    virtual void OnItemEquip(ICHARACTER* ch, IITEM* item) override {}
    virtual void OnItemUnequip(ICHARACTER* ch, IITEM* item) override {}
    virtual void OnItemUse(ICHARACTER* ch, IITEM* item) override {}
    virtual void OnItemDrop(ICHARACTER* ch, IITEM* item) override {}
    virtual void OnItemPickup(ICHARACTER* ch, IITEM* item) override {}
    virtual void OnKill(ICHARACTER* killer, ICHARACTER* victim) override {}
    virtual void OnDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage) override {}
    virtual void OnGuildCreate(IGUILD* guild) override {}
    virtual void OnGuildDestroy(IGUILD* guild) override {}
    virtual void OnGuildJoin(ICHARACTER* ch, IGUILD* guild) override {}
    virtual void OnGuildLeave(ICHARACTER* ch, IGUILD* guild) override {}
    virtual void OnGuildWar(IGUILD* guild1, IGUILD* guild2) override {}
    virtual void OnShopBuy(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count) override {}
    virtual void OnShopSell(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count) override {}
    virtual void OnQuestStart(ICHARACTER* ch, int questIndex) override {}
    virtual void OnQuestComplete(ICHARACTER* ch, int questIndex) override {}
    virtual void OnQuestGiveUp(ICHARACTER* ch, int questIndex) override {}
    virtual void OnChat(ICHARACTER* ch, const char* message, int type) override {}
    virtual void OnWhisper(ICHARACTER* from, ICHARACTER* to, const char* message) override {}
    virtual void OnShout(ICHARACTER* ch, const char* message) override {}
    virtual void OnMapEnter(ICHARACTER* ch, long mapIndex) override {}
    virtual void OnMapLeave(ICHARACTER* ch, long mapIndex) override {}
    virtual void OnHeartbeat() override {}
    virtual void OnMinuteUpdate() override {}
    virtual void OnHourUpdate() override {}
    virtual void OnDayUpdate() override {}
};

// ============================================================================
// PLUGIN FACTORY FUNCTIONS FOR GAME PLUGIN MANAGER
// ============================================================================

extern "C" {
    // Old plugin interface (for compatibility)
    IPlugin* CreatePlugin()
    {
        return new MinimalGamePlugin();
    }

    void DestroyPlugin(IPlugin* plugin)
    {
        delete plugin;
    }

    // New game plugin interface (required by GamePluginManager)
    __declspec(dllexport) IGamePlugin* CreateGamePlugin()
    {
        return new MinimalGamePlugin();
    }

    __declspec(dllexport) void DestroyGamePlugin(IGamePlugin* plugin)
    {
        delete plugin;
    }

    const char* GetPluginName()
    {
        return "MinimalGamePlugin";
    }

    const char* GetPluginVersion()
    {
        return "1.0.0";
    }

    const char* GetPluginDescription()
    {
        return "Minimal game plugin demonstrating ABI-stable interfaces";
    }
}
