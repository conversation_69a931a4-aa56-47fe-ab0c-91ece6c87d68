#ifndef __INC_IMOB_MANAGER_H__
#define __INC_IMOB_MANAGER_H__

#include "../stl.h"
#include "../tables.h"
#include "../../game/src/typedef.h"
class CMob;
// Forward declarations
class IMob
{
    public:
	virtual TMobTable GetMobTable() const = 0;
    virtual void AddSkillSplash(int iIndex, DWORD dwTiming, DWORD dwHitDistance) = 0;
};
class CMobGroup;

/**
 * @brief Pure virtual interface for CMobManager singleton
 *
 * Provides ABI-stable access to mob management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 *
 * This interface focuses on mob data and spawn management.
 */
class IMobManager : virtual public Isingleton<IMobManager>
{
public:
    virtual ~IMobManager() = default;

    // ============================================================================
    // MOB TABLE MANAGEMENT
    // ============================================================================

    // Mob table access - based on actual CMobManager methods
    virtual const CMob* Get(DWORD dwVnum) = 0;
    virtual const CMob* Get(const char* c_pszName, bool bIsAbbrev) = 0;

    // ============================================================================
    // MOB GROUPS
    // ============================================================================

    // Group management - based on actual CMobManager methods
    virtual bool LoadGroup(const char* c_pszFileName
#if defined(__EXTENDED_RELOAD__)
        , bool bReload = false
#endif
    ) = 0;
    virtual bool LoadGroupGroup(const char* c_pszFileName
#if defined(__EXTENDED_RELOAD__)
        , bool bReload = false
#endif
    ) = 0;
    virtual CMobGroup* GetGroup(DWORD dwVnum) = 0;
    virtual DWORD GetGroupFromGroupGroup(DWORD dwVnum) = 0;

    // ============================================================================
    // CHARACTER MANAGEMENT
    // ============================================================================

    // Character operations
    virtual void RebindMobProto(LPCHARACTER ch) = 0;

    // ============================================================================
    // REGEN STATISTICS
    // ============================================================================

    // Regen tracking - based on actual CMobManager methods
    virtual void IncRegenCount(BYTE bRegenType, DWORD dwVnum, int iCount, int iTime) = 0;
    virtual void DumpRegenCount(const char* c_szFilename) = 0;
};

#endif // __INC_IMOB_MANAGER_H__
