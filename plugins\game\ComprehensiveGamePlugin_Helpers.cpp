#include "plugin_stdafx.h"
#include "ComprehensiveGamePlugin.h"

// Essential game system includes - avoiding problematic SQL includes
#include "char.h"
#include "char_manager.h"
#include "item.h"
#include "item_manager.h"
#include "desc.h"
#include "desc_manager.h"
#include "guild.h"
#include "guild_manager.h"
#include "shop.h"
#include "shop_manager.h"
#include "affect.h"
#include "log.h"
#include "config.h"
#include "utils.h"
#include "sectree_manager.h"
#include "locale_service.h"

// ============================================================================
// DEMONSTRATION METHODS - Show how to access game objects
// ============================================================================

void ComprehensiveGamePlugin::DemonstrateCharacterAccess(LPCHARACTER ch)
{
    if (!ch)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating CHARACTER access:");
    
    // Basic character information
    sys_log(0, "  Name: %s", ch->GetName());
    sys_log(0, "  Player ID: %u", ch->GetPlayerID());
    sys_log(0, "  Level: %d", ch->GetLevel());
    sys_log(0, "  Job: %d", ch->GetJob());
    sys_log(0, "  Race: %d", ch->GetRaceNum());
    sys_log(0, "  Empire: %d", ch->GetEmpire());
    
    // Position and map
    sys_log(0, "  Position: (%ld, %ld)", ch->GetX(), ch->GetY());
    sys_log(0, "  Map Index: %ld", ch->GetMapIndex());
    
    // Stats
    sys_log(0, "  HP: %d/%d", ch->GetHP(), ch->GetMaxHP());
    sys_log(0, "  SP: %d/%d", ch->GetSP(), ch->GetMaxSP());
    sys_log(0, "  Gold: %lld", ch->GetGold());
    sys_log(0, "  Experience: %lld", ch->GetExp());
    
    // Equipment demonstration
    for (int i = 0; i < WEAR_MAX_NUM; ++i)
    {
        LPITEM item = ch->GetWear(i);
        if (item)
        {
            sys_log(0, "  Equipment slot %d: %s (vnum: %u)", i, item->GetName(), item->GetVnum());
        }
    }
    
    // Guild information
    if (ch->GetGuild())
    {
        sys_log(0, "  Guild: %s (ID: %u)", ch->GetGuild()->GetName(), ch->GetGuild()->GetID());
    }
    
    // Party information
    if (ch->GetParty())
    {
        sys_log(0, "  Party: %d members", ch->GetParty()->GetMemberCount());
    }
    
    // Affect demonstration
    sys_log(0, "  Active affects:");
    const std::list<CAffect*>& affects = ch->GetAffectContainer();
    for (auto it = affects.begin(); it != affects.end(); ++it)
    {
        CAffect* affect = *it;
        if (affect)
        {
            sys_log(0, "    Affect type: %d, duration: %ld", affect->dwType, affect->lDuration);
        }
    }
}

void ComprehensiveGamePlugin::DemonstrateItemAccess(LPITEM item)
{
    if (!item)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating ITEM access:");
    sys_log(0, "  Name: %s", item->GetName());
    sys_log(0, "  Vnum: %u", item->GetVnum());
    sys_log(0, "  Type: %d", item->GetType());
    sys_log(0, "  SubType: %d", item->GetSubType());
    sys_log(0, "  Count: %u", item->GetCount());
    sys_log(0, "  Level: %d", item->GetLevel());
    sys_log(0, "  Refine: %d", item->GetRefineLevel());
    
    // Item attributes
    for (int i = 0; i < ITEM_ATTRIBUTE_MAX_NUM; ++i)
    {
        const TPlayerItemAttribute& attr = item->GetAttribute(i);
        if (attr.bType != 0)
        {
            sys_log(0, "  Attribute %d: type=%d, value=%d", i, attr.bType, attr.sValue);
        }
    }
    
    // Item sockets
    for (int i = 0; i < ITEM_SOCKET_MAX_NUM; ++i)
    {
        long socket = item->GetSocket(i);
        if (socket != 0)
        {
            sys_log(0, "  Socket %d: %ld", i, socket);
        }
    }
    
    // Owner information
    if (item->GetOwner())
    {
        sys_log(0, "  Owner: %s", item->GetOwner()->GetName());
    }
}

void ComprehensiveGamePlugin::DemonstrateGuildAccess(LPGUILD guild)
{
    if (!guild)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating GUILD access:");
    sys_log(0, "  Name: %s", guild->GetName());
    sys_log(0, "  ID: %u", guild->GetID());
    sys_log(0, "  Level: %d", guild->GetLevel());
    sys_log(0, "  Experience: %d", guild->GetExp());
    sys_log(0, "  Member Count: %d", guild->GetMemberCount());
    sys_log(0, "  Master: %s", guild->GetMasterName());
    
    // Guild skills
    for (int i = 0; i < GUILD_SKILL_COUNT; ++i)
    {
        int skillLevel = guild->GetSkillLevel(i);
        if (skillLevel > 0)
        {
            sys_log(0, "  Guild skill %d: level %d", i, skillLevel);
        }
    }
}

void ComprehensiveGamePlugin::DemonstrateShopAccess(LPSHOP shop)
{
    if (!shop)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating SHOP access:");
    sys_log(0, "  Shop VID: %u", shop->GetVID());
    sys_log(0, "  Shop items available");
    
    // Note: Shop item access would depend on the specific shop implementation
}

// DemonstratePartyAccess method removed as party events are not in the interface

void ComprehensiveGamePlugin::DemonstrateQuestAccess(LPCHARACTER ch, int questIndex)
{
    if (!ch)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating QUEST access:");
    sys_log(0, "  Character: %s", ch->GetName());
    sys_log(0, "  Quest Index: %d", questIndex);
    
    // Access quest manager
    PC* pc = quest::CQuestManager::instance().GetPC(ch->GetPlayerID());
    if (pc)
    {
        sys_log(0, "  Quest PC found for player");
        
        // Get quest state
        int state = pc->GetFlag(std::string("quest.") + std::to_string(questIndex) + ".state");
        sys_log(0, "  Quest state: %d", state);
    }
}

void ComprehensiveGamePlugin::DemonstrateSkillAccess(LPCHARACTER ch, DWORD skillVnum)
{
    if (!ch)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating SKILL access:");
    sys_log(0, "  Character: %s", ch->GetName());
    sys_log(0, "  Skill Vnum: %u", skillVnum);
    
    // Get skill level
    int skillLevel = ch->GetSkillLevel(skillVnum);
    sys_log(0, "  Skill Level: %d", skillLevel);
    
    // Get skill master type
    int masterType = ch->GetSkillMasterType(skillVnum);
    sys_log(0, "  Master Type: %d", masterType);
    
    // Check if skill can be used
    bool canUse = ch->CanUseSkill(skillVnum);
    sys_log(0, "  Can Use: %s", canUse ? "Yes" : "No");
}

void ComprehensiveGamePlugin::DemonstrateAffectAccess(LPCHARACTER ch)
{
    if (!ch)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating AFFECT access:");
    
    // Check for specific affects
    if (ch->IsAffectFlag(AFF_POISON))
    {
        sys_log(0, "  Character is poisoned");
    }
    
    if (ch->IsAffectFlag(AFF_SLOW))
    {
        sys_log(0, "  Character is slowed");
    }
    
    if (ch->IsAffectFlag(AFF_STUN))
    {
        sys_log(0, "  Character is stunned");
    }
    
    // Get affect by type
    CAffect* affect = ch->FindAffect(AFFECT_STUN);
    if (affect)
    {
        sys_log(0, "  Stun affect duration: %ld", affect->lDuration);
    }
}

void ComprehensiveGamePlugin::DemonstrateMapAccess(LPCHARACTER ch)
{
    if (!ch)
        return;
    
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating MAP access:");
    
    long mapIndex = ch->GetMapIndex();
    sys_log(0, "  Current Map Index: %ld", mapIndex);
    
    // Get sectree
    LPSECTREE sectree = ch->GetSectree();
    if (sectree)
    {
        sys_log(0, "  Sectree found");
        
        // Get nearby characters
        FCharacterVectorCollector collector;
        sectree->ForEachAround(collector);
        
        sys_log(0, "  Nearby characters: %zu", collector.GetVector().size());
    }
    
    // Check if in dungeon
    if (ch->GetDungeon())
    {
        sys_log(0, "  Character is in a dungeon");
    }
    
    // Check if in arena
    if (ch->GetArena())
    {
        sys_log(0, "  Character is in an arena");
    }
}

void ComprehensiveGamePlugin::DemonstrateUtilityAccess()
{
    sys_log(0, "[ComprehensiveGamePlugin] Demonstrating UTILITY access:");
    
    // Random number generation
    int randomNum = number(1, 100);
    sys_log(0, "  Random number (1-100): %d", randomNum);
    
    // Time utilities
    time_t currentTime = get_global_time();
    sys_log(0, "  Current time: %ld", currentTime);
    
    // String utilities
    std::string testStr = "Test String";
    std::string lowerStr = testStr;
    str_lower(lowerStr);
    sys_log(0, "  String conversion: '%s' -> '%s'", testStr.c_str(), lowerStr.c_str());
    
    // Configuration access
    int playerMaxLevel = g_iPlayerMaxLevel;
    sys_log(0, "  Player max level: %d", playerMaxLevel);
    
    // Locale service
    const char* localeString = LC_TEXT("WELCOME");
    sys_log(0, "  Locale string: %s", localeString ? localeString : "Not found");
}
