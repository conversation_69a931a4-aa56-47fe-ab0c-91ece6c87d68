# libpoly Library
cmake_minimum_required(VERSION 3.16)

file(GLOB_RECURSE SOURCES "*.cc" "*.c")
file(GLOB_RECURSE HEADERS "*.h")

create_server_library(libpoly
    SOURCES ${SOURCES}
    HEADERS ${HEADERS}
    DEPENDENCIES
        Server::libthecore
)

# Set output directory and name to match original Makefile
set_target_properties(libpoly PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    ARCHIVE_OUTPUT_NAME poly
)
