/*
* $Id: llex.h,v 1.47 2003/02/28 17:19:47 roberto Exp $
* Lexical Analyzer
* See Copyright Notice in lua.h
*/

#ifndef __INC_LLEX_H__
#define __INC_LLEX_H__

#include "lobject.h"
#include "lzio.h"

#define FIRST_RESERVED 257

/* maximum length of a reserved word */
#define TOKEN_LEN (sizeof("function")/sizeof(char))

/*
* WARNING: if you change the order of this enumeration,
* grep "ORDER RESERVED"
*/
enum RESERVED {
	/* terminal symbols denoted by reserved words */
	TK_AND = FIRST_RESERVED, TK_BREAK,
	TK_DO, TK_ELSE, TK_ELSEIF, TK_END, TK_FALSE, TK_FOR, TK_FUNCTION,
	TK_IF, TK_IN, TK_LOCAL, TK_NIL, TK_NOT, TK_OR, TK_REPEAT,
	TK_RETURN, TK_THEN, TK_TRUE, TK_UNTIL, TK_WHILE,
	TK_QUEST, TK_STATE, TK_WITH, TK_WHEN,
	TK_BEGIN,
	LAST_RESERVED = TK_BEGIN,
	/* other terminal symbols */
	TK_NAME, TK_CONCAT, TK_DOTS, TK_EQ, TK_GE, TK_LE, TK_NE, TK_NUMBER,
	TK_STRING, TK_EOS
};

/* number of reserved words */
#define NUM_RESERVED (cast(int, LAST_RESERVED-FIRST_RESERVED+1))
#define NUM_PRESERVED 1

extern int token_preserved[NUM_PRESERVED];

typedef union
{
	lua_Number r;
	TString* ts;
} SemInfo; /* semantics information */

typedef struct Token
{
	int token;
	SemInfo seminfo;
} Token;

typedef struct LexState
{
	int current; /* current character (charint) */
	int linenumber; /* input line counter */
	int lastline; /* line of last token `consumed' */
	Token t; /* current token */
	Token lookahead; /* look ahead token */
	struct FuncState* fs; /* `FuncState' is private to the parser */
	struct lua_State* L;
	ZIO* z; /* input stream */
	Mbuffer* buff; /* buffer for tokens */
	TString* source; /* current source name */
	int nestlevel; /* level of nested non-terminals */
} LexState;

void luaX_init(lua_State* L);
void luaX_setinput(lua_State* L, LexState* LS, ZIO* z, TString* source);
int luaX_lex(LexState* LS, SemInfo* seminfo);
void luaX_checklimit(LexState* ls, int val, int limit, const char* msg);
void luaX_syntaxerror(LexState* ls, const char* s);
void luaX_errorline(LexState* ls, const char* s, const char* token, int line);
const char* luaX_token2str(LexState* ls, int token);

#endif // __INC_LLEX_H__
